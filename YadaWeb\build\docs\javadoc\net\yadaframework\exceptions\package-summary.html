<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.exceptions (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.exceptions">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li>Related Packages</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li>Related Packages&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package net.yadaframework.exceptions" class="title">Package net.yadaframework.exceptions</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">net.yadaframework.exceptions</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="class-summary">
<div class="caption"><span>Exception Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="InternalException.html" title="class in net.yadaframework.exceptions">InternalException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">Deprecated.
<div class="deprecation-comment">use YadaInternalException instead</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="InvalidValueException.html" title="class in net.yadaframework.exceptions">InvalidValueException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">Deprecated.
<div class="deprecation-comment">use YadaInvalidException instead</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="SystemException.html" title="class in net.yadaframework.exceptions">SystemException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">Deprecated.
<div class="deprecation-comment">use YadaSystemException instead</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="YadaAlreadyRunningException.html" title="class in net.yadaframework.exceptions">YadaAlreadyRunningException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">
<div class="block">Thrown when a YadaLongRunningExclusive class is being called while already running</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="YadaConfigurationException.html" title="class in net.yadaframework.exceptions">YadaConfigurationException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">
<div class="block">Unchecked exception thrown when some needed configuration element is missing</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="YadaEmailException.html" title="class in net.yadaframework.exceptions">YadaEmailException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">
<div class="block">Runtime exception that wraps email checked exceptions.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="YadaInternalException.html" title="class in net.yadaframework.exceptions">YadaInternalException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">
<div class="block">Unchecked exception thrown when something is inconsistent</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">
<div class="block">Thrown when some prerequisite is missing when calling a method.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="YadaInvalidValueException.html" title="class in net.yadaframework.exceptions">YadaInvalidValueException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">
<div class="block">A method parameter is invalid.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="YadaJobFailedException.html" title="class in net.yadaframework.exceptions">YadaJobFailedException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">
<div class="block">Convenience exception to throw when a YadaJob implementation fails execution, if the application doesn't implement a more specific exception.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="YadaSocialException.html" title="class in net.yadaframework.exceptions">YadaSocialException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">
<div class="block">Error while using the social functions</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="YadaSystemException.html" title="class in net.yadaframework.exceptions">YadaSystemException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">
<div class="block">Unchecked exception thrown when the system is in error (filesystem problems, memory exceptions, etc)</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
