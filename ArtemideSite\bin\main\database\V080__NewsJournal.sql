# Nuova tabelle delle News (per la pagina Journal)

create table NewsJournal (id bigint not null auto_increment, enabled bit not null default 0, publishDate datetime, showDate datetime, version bigint not null, thumbnail_id bigint, topIndice_id bigint, primary key (id)) engine=InnoDB;
create table NewsJournal_titlePartOne (NewsJournal_id bigint not null, titlePartOne varchar(255), locale varchar(32) not null, primary key (NewsJournal_id, locale)) engine=InnoDB;
create table NewsJournal_titlePartTwo (NewsJournal_id bigint not null, titlePartTwo varchar(255), locale varchar(32) not null, primary key (NewsJournal_id, locale)) engine=InnoDB;

create table NewsModule (id bigint not null auto_increment, linkDownload varchar(128), linkFb varchar(128), linkIg varchar(128), linkTw varchar(128), type integer not null, version bigint not null, image_id bigint, news_id bigint, video_id bigint, primary key (id)) engine=InnoDB;
create table NewsModule_Prodotto (NewsModule_id bigint not null, products_id bigint not null) engine=InnoDB;
create table NewsModule_textOne (NewsModule_id bigint not null, textOne varchar(255), locale varchar(32) not null, primary key (NewsModule_id, locale)) engine=InnoDB;
create table NewsModule_textTwo (NewsModule_id bigint not null, textTwo varchar(255), locale varchar(32) not null, primary key (NewsModule_id, locale)) engine=InnoDB;
create table NewsModule_YadaAttachedFile (NewsModule_id bigint not null, carrouselImages_id bigint not null) engine=InnoDB;

create table Tag (id bigint not null auto_increment,pos integer not null, version bigint not null, primary key (id)) engine=InnoDB;
create table Tag_name (Tag_id bigint not null, name varchar(255), locale varchar(32) not null, primary key (Tag_id, locale)) engine=InnoDB;
create table Tag_NewsJournal (tags_id bigint not null, news_id bigint not null) engine=InnoDB;

alter table NewsModule_YadaAttachedFile add constraint UK_otd2bm7p0ptw3esa3vymb7ju9 unique (carrouselImages_id);
alter table Tag_name add constraint UK8a9en9qol3ngiqif6snwemlln unique (name, locale);
alter table Tag_NewsJournal add constraint UKoerw895n797780rss7p918dhe7 unique (tags_id, news_id);

alter table NewsJournal add constraint FKiv9j2ajc9b13d2lchgqk0g8te foreign key (thumbnail_id) references YadaAttachedFile (id);
alter table NewsJournal add constraint FK3arbluwurkr1i81kseogm6k2w foreign key (topIndice_id) references YadaAttachedFile (id);
alter table NewsJournal_titlePartOne add constraint FKd0r04cta98krds0qqhw34djo foreign key (NewsJournal_id) references NewsJournal (id);
alter table NewsJournal_titlePartTwo add constraint FK56g4pd5vk0vyw6eq2hh1kt9a9 foreign key (NewsJournal_id) references NewsJournal (id);

alter table NewsModule add constraint FKlnh2ew5ak8fos2ancwl96ocfm foreign key (image_id) references YadaAttachedFile (id);
alter table NewsModule add constraint FKr11udnmy9hgbmoce6mgexsdi4 foreign key (news_id) references NewsJournal (id);
alter table NewsModule add constraint FKhaisl82pa48o1r6nifkyb1qb5 foreign key (video_id) references YadaAttachedFile (id);
alter table NewsModule_Prodotto add constraint FKbvehbhmpoeyar5myy26lu3l9t foreign key (products_id) references Prodotto (id);
alter table NewsModule_Prodotto add constraint FKdnf4wdokdax0tljfsd2fw4ket foreign key (NewsModule_id) references NewsModule (id);
alter table NewsModule_textOne add constraint FKg4enrfpggidabvc6gmuxqv2gq foreign key (NewsModule_id) references NewsModule (id);
alter table NewsModule_textTwo add constraint FK4tus6pu8jo8umx0mtkdwl1b2h foreign key (NewsModule_id) references NewsModule (id);
alter table NewsModule_YadaAttachedFile add constraint FKfeunmbr19xh1bqs840igweatv foreign key (carrouselImages_id) references YadaAttachedFile (id);
alter table NewsModule_YadaAttachedFile add constraint FK156vh4dscr4e1lt69lkvb0rs7 foreign key (NewsModule_id) references NewsModule (id);

alter table Tag_name add constraint FK98j8p1pa1lmkhrwyi1uf6oms9 foreign key (Tag_id) references Tag (id);
alter table Tag_NewsJournal add constraint FKbi9kvka8gr1slxdu9tfb2g9rm foreign key (news_id) references NewsJournal (id);
alter table Tag_NewsJournal add constraint FK5ibxqffeg151y43hsui58l5af foreign key (tags_id) references Tag (id);
