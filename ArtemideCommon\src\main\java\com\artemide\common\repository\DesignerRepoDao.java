package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.Designer;
import com.yr.entity.Famiglia;

@Repository
@Transactional(readOnly = true)
public class DesignerRepoDao {

    @PersistenceContext EntityManager em;

    public List<Designer> getAllPublished(boolean preview) {
        String sql = "select distinct d from Subfamily s join s.famiglia f join f.designers d where (s.published=true or s.published != :preview) and (f.published=true or f.published != :preview) order by d.surname";
        return em.createQuery(sql, Designer.class)
            .setParameter("preview", preview)
            .getResultList();
    }

    public List<Designer> getAllDesigners() {
        String sql = "select distinct d from Designer d order by d.surname asc";
        return em.createQuery(sql, Designer.class)
            .getResultList();
    }

    public List<Designer> getDesignerByFamiglia(Famiglia famiglia) {
        String sql = "select distinct d from Famiglia f join f.designers d where f = :famiglia";
        return em.createQuery(sql, Designer.class)
            .setParameter("famiglia", famiglia)
            .getResultList();
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Designer", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Designer save(Designer entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Designer> findById(Long entityId) {
        Designer result = em.find(Designer.class, entityId);
        return Optional.ofNullable(result);
    }

    public Designer findOne(Long entityId) {
        return em.find(Designer.class, entityId);
    }

    public List<Designer> findAll() {
        return em.createQuery("from Designer", Designer.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Designer> batchToSave) {
        for (Designer entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Designer entity) {
        em.remove(entity);
    }
}
