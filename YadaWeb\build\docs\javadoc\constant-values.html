<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Constant Field Values (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="summary of constants">
<meta name="generator" content="javadoc/ConstantsSummaryWriterImpl">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="constants-summary-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#constant-values">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Constant Field Values" class="title">Constant Field Values</h1>
</div>
<section class="packages">
<h2 title="Contents">Contents</h2>
<ul class="contents-list">
<li><a href="#net.yadaframework">net.yadaframework.*</a></li>
<li><a href="#org.springframework">org.springframework.*</a></li>
</ul>
</section>
<section class="constants-summary" id="net.yadaframework">
<h2 title="net.yadaframework.*">net.yadaframework.*</h2>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.components.<a href="net/yadaframework/components/YadaFileManager.html" title="class in net.yadaframework.components">YadaFileManager</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.components.YadaFileManager.NOIMAGE_DATA">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/components/YadaFileManager.html#NOIMAGE_DATA">NOIMAGE_DATA</a></code></div>
<div class="col-last even-row-color"><code>"data:image/jpeg;base64,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"</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.components.<a href="net/yadaframework/components/YadaUtil.html" title="class in net.yadaframework.components">YadaUtil</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.components.YadaUtil.MILLIS_IN_DAY">public&nbsp;static&nbsp;final&nbsp;long</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/components/YadaUtil.html#MILLIS_IN_DAY">MILLIS_IN_DAY</a></code></div>
<div class="col-last even-row-color"><code>86400000L</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.components.YadaUtil.MILLIS_IN_HOUR">public&nbsp;static&nbsp;final&nbsp;long</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/components/YadaUtil.html#MILLIS_IN_HOUR">MILLIS_IN_HOUR</a></code></div>
<div class="col-last odd-row-color"><code>3600000L</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.components.YadaUtil.MILLIS_IN_MINUTE">public&nbsp;static&nbsp;final&nbsp;long</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/components/YadaUtil.html#MILLIS_IN_MINUTE">MILLIS_IN_MINUTE</a></code></div>
<div class="col-last even-row-color"><code>60000L</code></div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.core.<a href="net/yadaframework/core/YadaConstants.html" title="interface in net.yadaframework.core">YadaConstants</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaConstants.EMAIL_TEMPLATES_FOLDER">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#EMAIL_TEMPLATES_FOLDER">EMAIL_TEMPLATES_FOLDER</a></code></div>
<div class="col-last even-row-color"><code>"email"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaConstants.EMAIL_TEMPLATES_PREFIX">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#EMAIL_TEMPLATES_PREFIX">EMAIL_TEMPLATES_PREFIX</a></code></div>
<div class="col-last odd-row-color"><code>"template"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaConstants.KEY_NOTIFICATION_AUTOCLOSE">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#KEY_NOTIFICATION_AUTOCLOSE">KEY_NOTIFICATION_AUTOCLOSE</a></code></div>
<div class="col-last even-row-color"><code>"YADA_AUTOCLOSE"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaConstants.KEY_NOTIFICATION_BODY">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#KEY_NOTIFICATION_BODY">KEY_NOTIFICATION_BODY</a></code></div>
<div class="col-last odd-row-color"><code>"YADA_NBODY"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaConstants.KEY_NOTIFICATION_CALLSCRIPT">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#KEY_NOTIFICATION_CALLSCRIPT">KEY_NOTIFICATION_CALLSCRIPT</a></code></div>
<div class="col-last even-row-color"><code>"YADA_CALLSCRIPT"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaConstants.KEY_NOTIFICATION_REDIRECT">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#KEY_NOTIFICATION_REDIRECT">KEY_NOTIFICATION_REDIRECT</a></code></div>
<div class="col-last odd-row-color"><code>"YADA_REDIRECT"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaConstants.KEY_NOTIFICATION_RELOADONCLOSE">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#KEY_NOTIFICATION_RELOADONCLOSE">KEY_NOTIFICATION_RELOADONCLOSE</a></code></div>
<div class="col-last even-row-color"><code>"YADA_RELOADONCLOSE"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaConstants.KEY_NOTIFICATION_SEVERITY">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#KEY_NOTIFICATION_SEVERITY">KEY_NOTIFICATION_SEVERITY</a></code></div>
<div class="col-last odd-row-color"><code>"YADA_SEVERITY"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaConstants.KEY_NOTIFICATION_TITLE">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#KEY_NOTIFICATION_TITLE">KEY_NOTIFICATION_TITLE</a></code></div>
<div class="col-last even-row-color"><code>"YADA_NTITLE"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaConstants.KEY_NOTIFICATION_TOTALSEVERITY">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#KEY_NOTIFICATION_TOTALSEVERITY">KEY_NOTIFICATION_TOTALSEVERITY</a></code></div>
<div class="col-last odd-row-color"><code>"YADA_TOTSEVERITY"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaConstants.REQUEST_HASERROR_FLAG">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#REQUEST_HASERROR_FLAG">REQUEST_HASERROR_FLAG</a></code></div>
<div class="col-last even-row-color"><code>"YADA_HAS_ERROR"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaConstants.SESSION_USER_TIMEZONE">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#SESSION_USER_TIMEZONE">SESSION_USER_TIMEZONE</a></code></div>
<div class="col-last odd-row-color"><code>"YADA_USER_TIMEZONE"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaConstants.VAL_NOTIFICATION_SEVERITY_ERROR">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#VAL_NOTIFICATION_SEVERITY_ERROR">VAL_NOTIFICATION_SEVERITY_ERROR</a></code></div>
<div class="col-last even-row-color"><code>"error"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaConstants.VAL_NOTIFICATION_SEVERITY_INFO">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#VAL_NOTIFICATION_SEVERITY_INFO">VAL_NOTIFICATION_SEVERITY_INFO</a></code></div>
<div class="col-last odd-row-color"><code>"info"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaConstants.VAL_NOTIFICATION_SEVERITY_OK">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#VAL_NOTIFICATION_SEVERITY_OK">VAL_NOTIFICATION_SEVERITY_OK</a></code></div>
<div class="col-last even-row-color"><code>"ok"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaConstants.YADA_VIEW_PREFIX">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaConstants.html#YADA_VIEW_PREFIX">YADA_VIEW_PREFIX</a></code></div>
<div class="col-last odd-row-color"><code>"net/yadaframework/views"</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.core.<a href="net/yadaframework/core/YadaWebConfig.html" title="class in net.yadaframework.core">YadaWebConfig</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaWebConfig.MILLIS_IN_MINUTE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaWebConfig.html#MILLIS_IN_MINUTE">MILLIS_IN_MINUTE</a></code></div>
<div class="col-last even-row-color"><code>60000</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaWebConfig.MILLIS_IN_SECOND">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaWebConfig.html#MILLIS_IN_SECOND">MILLIS_IN_SECOND</a></code></div>
<div class="col-last odd-row-color"><code>1000</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaWebConfig.SECONDS_IN_MINUTE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaWebConfig.html#SECONDS_IN_MINUTE">SECONDS_IN_MINUTE</a></code></div>
<div class="col-last even-row-color"><code>60</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaWebConfig.STATIC_FILE_FOLDER">protected&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaWebConfig.html#STATIC_FILE_FOLDER">STATIC_FILE_FOLDER</a></code></div>
<div class="col-last odd-row-color"><code>"/static"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.core.YadaWebConfig.STATIC_RESOURCES_FOLDER">protected&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/core/YadaWebConfig.html#STATIC_RESOURCES_FOLDER">STATIC_RESOURCES_FOLDER</a></code></div>
<div class="col-last even-row-color"><code>"/res"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.core.YadaWebConfig.STATIC_YADARESOURCES_FOLDER">protected&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/core/YadaWebConfig.html#STATIC_YADARESOURCES_FOLDER">STATIC_YADARESOURCES_FOLDER</a></code></div>
<div class="col-last odd-row-color"><code>"/yadares"</code></div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.persistence.<a href="net/yadaframework/persistence/YadaDataTableDao.html" title="class in net.yadaframework.persistence">YadaDataTableDao</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.persistence.YadaDataTableDao.COLUMN_COMMAND">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/persistence/YadaDataTableDao.html#COLUMN_COMMAND">COLUMN_COMMAND</a></code></div>
<div class="col-last even-row-color"><code>"_yadaCommandColumn"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.persistence.YadaDataTableDao.COLUMN_SELECTION">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/persistence/YadaDataTableDao.html#COLUMN_SELECTION">COLUMN_SELECTION</a></code></div>
<div class="col-last odd-row-color"><code>"_yadaSelectionColumn"</code></div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.persistence.entity.<a href="net/yadaframework/persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.persistence.entity.YadaAttachedFile.COUNTER_SEPARATOR">protected&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/persistence/entity/YadaAttachedFile.html#COUNTER_SEPARATOR">COUNTER_SEPARATOR</a></code></div>
<div class="col-last even-row-color"><code>"_"</code></div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.raw.<a href="net/yadaframework/raw/YadaHttpUtil.html" title="class in net.yadaframework.raw">YadaHttpUtil</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.raw.YadaHttpUtil.CONTENT_CSS">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/raw/YadaHttpUtil.html#CONTENT_CSS">CONTENT_CSS</a></code></div>
<div class="col-last even-row-color"><code>2</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.raw.YadaHttpUtil.CONTENT_DOCUMENT">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/raw/YadaHttpUtil.html#CONTENT_DOCUMENT">CONTENT_DOCUMENT</a></code></div>
<div class="col-last odd-row-color"><code>0</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.raw.YadaHttpUtil.CONTENT_IMAGE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/raw/YadaHttpUtil.html#CONTENT_IMAGE">CONTENT_IMAGE</a></code></div>
<div class="col-last even-row-color"><code>6</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.raw.YadaHttpUtil.CONTENT_JAVASCRIPT">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/raw/YadaHttpUtil.html#CONTENT_JAVASCRIPT">CONTENT_JAVASCRIPT</a></code></div>
<div class="col-last odd-row-color"><code>1</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.raw.YadaHttpUtil.CONTENT_OTHER">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/raw/YadaHttpUtil.html#CONTENT_OTHER">CONTENT_OTHER</a></code></div>
<div class="col-last even-row-color"><code>3</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.raw.YadaHttpUtil.CONTENT_UNKNOWN">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/raw/YadaHttpUtil.html#CONTENT_UNKNOWN">CONTENT_UNKNOWN</a></code></div>
<div class="col-last odd-row-color"><code>4</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.raw.YadaHttpUtil.CONTENT_XML">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/raw/YadaHttpUtil.html#CONTENT_XML">CONTENT_XML</a></code></div>
<div class="col-last even-row-color"><code>5</code></div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.selenium.<a href="net/yadaframework/selenium/YadaSeleniumUtil.html" title="class in net.yadaframework.selenium">YadaSeleniumUtil</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.selenium.YadaSeleniumUtil.DRIVER_CHROME">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/selenium/YadaSeleniumUtil.html#DRIVER_CHROME">DRIVER_CHROME</a></code></div>
<div class="col-last even-row-color"><code>1</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.selenium.YadaSeleniumUtil.DRIVER_FIREFOX">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/selenium/YadaSeleniumUtil.html#DRIVER_FIREFOX">DRIVER_FIREFOX</a></code></div>
<div class="col-last odd-row-color"><code>0</code></div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.web.<a href="net/yadaframework/web/YadaCropQueue.html" title="class in net.yadaframework.web">YadaCropQueue</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaCropQueue.SESSION_KEY">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaCropQueue.html#SESSION_KEY">SESSION_KEY</a></code></div>
<div class="col-last even-row-color"><code>"net.yadaframework.web.YadaCropQueue"</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.<a href="net/yadaframework/web/YadaDatatablesOrder.html" title="class in net.yadaframework.web">YadaDatatablesOrder</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaDatatablesOrder.ASC">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaDatatablesOrder.html#ASC">ASC</a></code></div>
<div class="col-last even-row-color"><code>"asc"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaDatatablesOrder.DESC">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaDatatablesOrder.html#DESC">DESC</a></code></div>
<div class="col-last odd-row-color"><code>"desc"</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.<a href="net/yadaframework/web/YadaPageSort.html" title="class in net.yadaframework.web">YadaPageSort</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaPageSort.KEYWORD_ASC">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaPageSort.html#KEYWORD_ASC">KEYWORD_ASC</a></code></div>
<div class="col-last even-row-color"><code>"asc"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaPageSort.KEYWORD_DESC">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaPageSort.html#KEYWORD_DESC">KEYWORD_DESC</a></code></div>
<div class="col-last odd-row-color"><code>"desc"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaPageSort.KEYWORD_IGNORECASE">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaPageSort.html#KEYWORD_IGNORECASE">KEYWORD_IGNORECASE</a></code></div>
<div class="col-last even-row-color"><code>"ignorecase"</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.<a href="net/yadaframework/web/YadaViews.html" title="interface in net.yadaframework.web">YadaViews</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_CLOSE_MODAL">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_CLOSE_MODAL">AJAX_CLOSE_MODAL</a></code></div>
<div class="col-last even-row-color"><code>"/yada/ajaxCloseModal"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_FORBIDDEN">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_FORBIDDEN">AJAX_FORBIDDEN</a></code></div>
<div class="col-last odd-row-color"><code>"/yada/ajaxForbidden"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_NOTIFY_B3">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_NOTIFY_B3">AJAX_NOTIFY_B3</a></code></div>
<div class="col-last even-row-color"><code>"/yada/modalNotifyB3"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_NOTIFY_B4">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_NOTIFY_B4">AJAX_NOTIFY_B4</a></code></div>
<div class="col-last odd-row-color"><code>"/yada/modalNotifyB4"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_NOTIFY_B5">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_NOTIFY_B5">AJAX_NOTIFY_B5</a></code></div>
<div class="col-last even-row-color"><code>"/yada/modalNotifyB5"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_REDIRECT">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_REDIRECT">AJAX_REDIRECT</a></code></div>
<div class="col-last odd-row-color"><code>"/yada/ajaxRedirect"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_REDIRECT_NEWTAB">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_REDIRECT_NEWTAB">AJAX_REDIRECT_NEWTAB</a></code></div>
<div class="col-last even-row-color"><code>"newTab"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_REDIRECT_URL">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_REDIRECT_URL">AJAX_REDIRECT_URL</a></code></div>
<div class="col-last odd-row-color"><code>"targetUrl"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_REDIRECT_URL_RELATIVE">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_REDIRECT_URL_RELATIVE">AJAX_REDIRECT_URL_RELATIVE</a></code></div>
<div class="col-last even-row-color"><code>"targetUrlRelative"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_RELOAD">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_RELOAD">AJAX_RELOAD</a></code></div>
<div class="col-last odd-row-color"><code>"/yada/ajaxReload"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_SERVER_ERROR">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_SERVER_ERROR">AJAX_SERVER_ERROR</a></code></div>
<div class="col-last even-row-color"><code>"/yada/ajaxError"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_SERVER_ERROR_DESCRIPTION">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_SERVER_ERROR_DESCRIPTION">AJAX_SERVER_ERROR_DESCRIPTION</a></code></div>
<div class="col-last odd-row-color"><code>"errorDescription"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_SUCCESS">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_SUCCESS">AJAX_SUCCESS</a></code></div>
<div class="col-last even-row-color"><code>"/yada/ajaxSuccess"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaViews.AJAX_SUGGESTION_FRAGMENT">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaViews.html#AJAX_SUGGESTION_FRAGMENT">AJAX_SUGGESTION_FRAGMENT</a></code></div>
<div class="col-last odd-row-color"><code>"/yada/formfields/inputSuggestionFragment :: fragment"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaViews.CONFIRM_B3">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaViews.html#CONFIRM_B3">CONFIRM_B3</a></code></div>
<div class="col-last even-row-color"><code>"/yada/modalConfirmB3"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaViews.CONFIRM_B4">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaViews.html#CONFIRM_B4">CONFIRM_B4</a></code></div>
<div class="col-last odd-row-color"><code>"/yada/modalConfirmB4"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.YadaViews.CONFIRM_B5">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/YadaViews.html#CONFIRM_B5">CONFIRM_B5</a></code></div>
<div class="col-last even-row-color"><code>"/yada/modalConfirmB5"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.YadaViews.VIEW_EMPTY">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/YadaViews.html#VIEW_EMPTY">VIEW_EMPTY</a></code></div>
<div class="col-last odd-row-color"><code>"/yada/empty"</code></div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaAjaxAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaAjaxAttrProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaAjaxAttrProcessor.ATTR_NAME">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaAjaxAttrProcessor.html#ATTR_NAME">ATTR_NAME</a></code></div>
<div class="col-last even-row-color"><code>"ajax"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.dialect.YadaAjaxAttrProcessor.ATTR_PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/dialect/YadaAjaxAttrProcessor.html#ATTR_PRECEDENCE">ATTR_PRECEDENCE</a></code></div>
<div class="col-last odd-row-color"><code>10000</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaBrOnFirstSpaceAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaBrOnFirstSpaceAttrProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaBrOnFirstSpaceAttrProcessor.ATTR_NAME">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaBrOnFirstSpaceAttrProcessor.html#ATTR_NAME">ATTR_NAME</a></code></div>
<div class="col-last even-row-color"><code>"brspace"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.dialect.YadaBrOnFirstSpaceAttrProcessor.ATTR_PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/dialect/YadaBrOnFirstSpaceAttrProcessor.html#ATTR_PRECEDENCE">ATTR_PRECEDENCE</a></code></div>
<div class="col-last odd-row-color"><code>10000</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaDataTableTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaDataTableTagProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaDataTableTagProcessor.PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaDataTableTagProcessor.html#PRECEDENCE">PRECEDENCE</a></code></div>
<div class="col-last even-row-color"><code>12000</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaDialectUtil.html" title="class in net.yadaframework.web.dialect">YadaDialectUtil</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaDialectUtil.THYMELEAF_PREFIX_WITHCOLUMN">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaDialectUtil.html#THYMELEAF_PREFIX_WITHCOLUMN">THYMELEAF_PREFIX_WITHCOLUMN</a></code></div>
<div class="col-last even-row-color"><code>"th:"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.dialect.YadaDialectUtil.YADA_PREFIX_WITHCOLUMN">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/dialect/YadaDialectUtil.html#YADA_PREFIX_WITHCOLUMN">YADA_PREFIX_WITHCOLUMN</a></code></div>
<div class="col-last odd-row-color"><code>"yada:"</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaHrefAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaHrefAttrProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaHrefAttrProcessor.ATTR_NAME">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaHrefAttrProcessor.html#ATTR_NAME">ATTR_NAME</a></code></div>
<div class="col-last even-row-color"><code>"href"</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaInputCounterTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaInputCounterTagProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaInputCounterTagProcessor.PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaInputCounterTagProcessor.html#PRECEDENCE">PRECEDENCE</a></code></div>
<div class="col-last even-row-color"><code>12000</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaInputTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaInputTagProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaInputTagProcessor.PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaInputTagProcessor.html#PRECEDENCE">PRECEDENCE</a></code></div>
<div class="col-last even-row-color"><code>12000</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaNewlineTextAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaNewlineTextAttrProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaNewlineTextAttrProcessor.ATTR_NAME">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaNewlineTextAttrProcessor.html#ATTR_NAME">ATTR_NAME</a></code></div>
<div class="col-last even-row-color"><code>"newlinetext"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.dialect.YadaNewlineTextAttrProcessor.ATTR_PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/dialect/YadaNewlineTextAttrProcessor.html#ATTR_PRECEDENCE">ATTR_PRECEDENCE</a></code></div>
<div class="col-last odd-row-color"><code>10000</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaSimpleAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaSimpleAttrProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaSimpleAttrProcessor.ATTR_PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaSimpleAttrProcessor.html#ATTR_PRECEDENCE">ATTR_PRECEDENCE</a></code></div>
<div class="col-last even-row-color"><code>10000</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaSrcAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaSrcAttrProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaSrcAttrProcessor.ATTR_NAME">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaSrcAttrProcessor.html#ATTR_NAME">ATTR_NAME</a></code></div>
<div class="col-last even-row-color"><code>"src"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.dialect.YadaSrcAttrProcessor.ATTR_PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/dialect/YadaSrcAttrProcessor.html#ATTR_PRECEDENCE">ATTR_PRECEDENCE</a></code></div>
<div class="col-last odd-row-color"><code>10000</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaSrcsetAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaSrcsetAttrProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaSrcsetAttrProcessor.ATTR_NAME">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaSrcsetAttrProcessor.html#ATTR_NAME">ATTR_NAME</a></code></div>
<div class="col-last even-row-color"><code>"srcset"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.web.dialect.YadaSrcsetAttrProcessor.ATTR_PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/web/dialect/YadaSrcsetAttrProcessor.html#ATTR_PRECEDENCE">ATTR_PRECEDENCE</a></code></div>
<div class="col-last odd-row-color"><code>10000</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.web.dialect.<a href="net/yadaframework/web/dialect/YadaTextareaTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaTextareaTagProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.web.dialect.YadaTextareaTagProcessor.PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/web/dialect/YadaTextareaTagProcessor.html#PRECEDENCE">PRECEDENCE</a></code></div>
<div class="col-last even-row-color"><code>12000</code></div>
</div>
</li>
</ul>
</section>
<section class="constants-summary" id="org.springframework">
<h2 title="org.springframework.*">org.springframework.*</h2>
<ul class="block-list">
<li>
<div class="caption"><span>org.springframework.web.multipart.commons.<a href="org/springframework/web/multipart/commons/YadaCommonsMultipartResolver.html" title="class in org.springframework.web.multipart.commons">YadaCommonsMultipartResolver</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="org.springframework.web.multipart.commons.YadaCommonsMultipartResolver.MAX_UPLOAD_SIZE_EXCEEDED_KEY">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="org/springframework/web/multipart/commons/YadaCommonsMultipartResolver.html#MAX_UPLOAD_SIZE_EXCEEDED_KEY">MAX_UPLOAD_SIZE_EXCEEDED_KEY</a></code></div>
<div class="col-last even-row-color"><code>"MaxUploadSizeExceededException"</code></div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
