<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.web.datatables.proxy Class Hierarchy (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="tree: package: net.yadaframework.web.datatables.proxy">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package net.yadaframework.web.datatables.proxy</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">net.yadaframework.web.datatables.<a href="../YadaDataTable.html" class="type-name-link" title="class in net.yadaframework.web.datatables">YadaDataTable</a>
<ul>
<li class="circle">net.yadaframework.web.datatables.proxy.<a href="YadaDataTableProxy.html" class="type-name-link" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableProxy</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" class="type-name-link" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;T&gt;
<ul>
<li class="circle">net.yadaframework.web.datatables.config.<a href="../config/YadaDataTableButton.html" class="type-name-link" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a>
<ul>
<li class="circle">net.yadaframework.web.datatables.proxy.<a href="YadaDataTableButtonProxy.html" class="type-name-link" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableButtonProxy</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.web.datatables.config.<a href="../config/YadaDataTableColumn.html" class="type-name-link" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a>
<ul>
<li class="circle">net.yadaframework.web.datatables.proxy.<a href="YadaDataTableColumnProxy.html" class="type-name-link" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableColumnProxy</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.web.datatables.config.<a href="../config/YadaDataTableConfirmDialog.html" class="type-name-link" title="class in net.yadaframework.web.datatables.config">YadaDataTableConfirmDialog</a>
<ul>
<li class="circle">net.yadaframework.web.datatables.proxy.<a href="YadaDataTableConfirmDialogProxy.html" class="type-name-link" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableConfirmDialogProxy</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.web.datatables.config.<a href="../config/YadaDataTableHTML.html" class="type-name-link" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>
<ul>
<li class="circle">net.yadaframework.web.datatables.proxy.<a href="YadaDataTableHTMLProxy.html" class="type-name-link" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableHTMLProxy</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.web.datatables.config.<a href="../config/YadaDataTableLanguage.html" class="type-name-link" title="class in net.yadaframework.web.datatables.config">YadaDataTableLanguage</a>
<ul>
<li class="circle">net.yadaframework.web.datatables.proxy.<a href="YadaDataTableLanguageProxy.html" class="type-name-link" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableLanguageProxy</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.web.datatables.options.<a href="../options/YadaDTColumns.html" class="type-name-link" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a>
<ul>
<li class="circle">net.yadaframework.web.datatables.options.<a href="../options/YadaDTColumnDef.html" class="type-name-link" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a>
<ul>
<li class="circle">net.yadaframework.web.datatables.proxy.<a href="YadaDTColumnDefProxy.html" class="type-name-link" title="class in net.yadaframework.web.datatables.proxy">YadaDTColumnDefProxy</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.web.datatables.proxy.<a href="YadaDTColumnsProxy.html" class="type-name-link" title="class in net.yadaframework.web.datatables.proxy">YadaDTColumnsProxy</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.web.datatables.options.<a href="../options/YadaDTOptions.html" class="type-name-link" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>
<ul>
<li class="circle">net.yadaframework.web.datatables.proxy.<a href="YadaDTOptionsProxy.html" class="type-name-link" title="class in net.yadaframework.web.datatables.proxy">YadaDTOptionsProxy</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
