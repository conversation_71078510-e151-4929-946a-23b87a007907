package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.CountryProdottoList;

@Repository
@Transactional(readOnly = true)
public class CountryProdottoListDao {

    @PersistenceContext EntityManager em;

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from CountryProdottoList", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public CountryProdottoList save(CountryProdottoList entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<CountryProdottoList> findById(Long entityId) {
        CountryProdottoList result = em.find(CountryProdottoList.class, entityId);
        return Optional.ofNullable(result);
    }

    public CountryProdottoList findOne(Long entityId) {
        return em.find(CountryProdottoList.class, entityId);
    }

    public List<CountryProdottoList> findAll() {
        return em.createQuery("from CountryProdottoList", CountryProdottoList.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<CountryProdottoList> batchToSave) {
        for (CountryProdottoList entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(CountryProdottoList entity) {
        em.remove(entity);
    }
}
