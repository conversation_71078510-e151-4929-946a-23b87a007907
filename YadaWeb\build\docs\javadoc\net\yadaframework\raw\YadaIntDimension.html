<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaIntDimension (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.raw, class: YadaIntDimension">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.raw</a></div>
<h1 title="Class YadaIntDimension" class="title">Class YadaIntDimension</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.raw.YadaIntDimension</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="annotations">@Embeddable
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaIntDimension</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></span></div>
<div class="block">Like java.awt.Dimension but with int values.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../serialized-form.html#net.yadaframework.raw.YadaIntDimension">Serialized Form</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>int</code></div>
<div class="col-second even-row-color"><code><a href="#height" class="member-name-link">height</a></code></div>
<div class="col-last even-row-color">
<div class="block">The height dimension; negative values can be used.</div>
</div>
<div class="col-first odd-row-color"><code>static final <a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></code></div>
<div class="col-second odd-row-color"><code><a href="#UNSET" class="member-name-link">UNSET</a></code></div>
<div class="col-last odd-row-color">
<div class="block">UNSET is a special value to be used in @Entity instances instead of null that would generate a constraint violation</div>
</div>
<div class="col-first even-row-color"><code>int</code></div>
<div class="col-second even-row-color"><code><a href="#width" class="member-name-link">width</a></code></div>
<div class="col-last even-row-color">
<div class="block">The width dimension; negative values can be used.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaIntDimension</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Creates an instance of <code>YadaIntDimension</code> with a width
 of zero and a height of zero.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.Integer,java.lang.Integer)" class="member-name-link">YadaIntDimension</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;width,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;height)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructs a <code>YadaIntDimension</code> and initializes
 it to the specified width and specified height.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(net.yadaframework.raw.YadaIntDimension)" class="member-name-link">YadaIntDimension</a><wbr>(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;d)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates an instance of <code>YadaIntDimension</code> whose width
 and height are the same as for the specified dimension.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#biggest(net.yadaframework.raw.YadaIntDimension...)" class="member-name-link">biggest</a><wbr>(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>...&nbsp;dimensions)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the dimension with the width or height that has the biggest value, or the first one if there are many that qualify.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#biggestCover(net.yadaframework.raw.YadaIntDimension...)" class="member-name-link">biggestCover</a><wbr>(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>...&nbsp;dimensions)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the dimension with the width and height that cover all given dimensions.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#equals(java.lang.Object)" class="member-name-link">equals</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Checks whether two dimension objects have equal values.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHeight()" class="member-name-link">getHeight</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMax()" class="member-name-link">getMax</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the biggest of width and height</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWidth()" class="member-name-link">getWidth</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hashCode()" class="member-name-link">hashCode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the hash code for this <code>YadaIntDimension</code>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isAnyBiggerThan(net.yadaframework.raw.YadaIntDimension)" class="member-name-link">isAnyBiggerThan</a><wbr>(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;yadaIntDimension)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if at least one dimension is bigger.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isAnySmallerThan(net.yadaframework.raw.YadaIntDimension)" class="member-name-link">isAnySmallerThan</a><wbr>(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;yadaIntDimension)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if at least one dimension is smaller.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isBiggerThan(net.yadaframework.raw.YadaIntDimension)" class="member-name-link">isBiggerThan</a><wbr>(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;yadaIntDimension)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if both dimensions are not smaller than the argument, nor both equal.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isEqualTo(net.yadaframework.raw.YadaIntDimension)" class="member-name-link">isEqualTo</a><wbr>(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;yadaIntDimension)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if this instance is equal to the argument</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSmallerThan(net.yadaframework.raw.YadaIntDimension)" class="member-name-link">isSmallerThan</a><wbr>(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;yadaIntDimension)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if both dimensions are not bigger than the argument, nor both equal.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isUnset()" class="member-name-link">isUnset</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSize(int,int)" class="member-name-link">setSize</a><wbr>(int&nbsp;width,
 int&nbsp;height)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the size of this <code>YadaIntDimension</code> object to
 the specified width and height.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSize(net.yadaframework.raw.YadaIntDimension)" class="member-name-link">setSize</a><wbr>(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;d)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the size of this <code>YadaIntDimension</code> object to the specified size.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#smallest(net.yadaframework.raw.YadaIntDimension...)" class="member-name-link">smallest</a><wbr>(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>...&nbsp;dimensions)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the smallest dimension, or the first one if there are many of the same size</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a string representation in the form WxH</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="UNSET">
<h3>UNSET</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></span>&nbsp;<span class="element-name">UNSET</span></div>
<div class="block">UNSET is a special value to be used in @Entity instances instead of null that would generate a constraint violation</div>
</section>
</li>
<li>
<section class="detail" id="width">
<h3>width</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">width</span></div>
<div class="block">The width dimension; negative values can be used.</div>
</section>
</li>
<li>
<section class="detail" id="height">
<h3>height</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">height</span></div>
<div class="block">The height dimension; negative values can be used.</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaIntDimension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaIntDimension</span>()</div>
<div class="block">Creates an instance of <code>YadaIntDimension</code> with a width
 of zero and a height of zero.</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(net.yadaframework.raw.YadaIntDimension)">
<h3>YadaIntDimension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaIntDimension</span><wbr><span class="parameters">(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;d)</span></div>
<div class="block">Creates an instance of <code>YadaIntDimension</code> whose width
 and height are the same as for the specified dimension.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>d</code> - the specified dimension for the
               <code>width</code> and
               <code>height</code> values</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.Integer,java.lang.Integer)">
<h3>YadaIntDimension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaIntDimension</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;width,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;height)</span></div>
<div class="block">Constructs a <code>YadaIntDimension</code> and initializes
 it to the specified width and specified height.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>width</code> - the width, null becomes 0</dd>
<dd><code>height</code> - the height, null becomes 0</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getMax()">
<h3>getMax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMax</span>()</div>
<div class="block">Returns the biggest of width and height</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="biggestCover(net.yadaframework.raw.YadaIntDimension...)">
<h3>biggestCover</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></span>&nbsp;<span class="element-name">biggestCover</span><wbr><span class="parameters">(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>...&nbsp;dimensions)</span></div>
<div class="block">Returns the dimension with the width and height that cover all given dimensions.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dimensions</code> - can be null or UNSET</dd>
<dt>Returns:</dt>
<dd>a dimension with the maximum width and height from the input arguments, or null when the input is all null or UNSET</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="biggest(net.yadaframework.raw.YadaIntDimension...)">
<h3>biggest</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></span>&nbsp;<span class="element-name">biggest</span><wbr><span class="parameters">(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>...&nbsp;dimensions)</span></div>
<div class="block">Returns the dimension with the width or height that has the biggest value, or the first one if there are many that qualify.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dimensions</code> - can be null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="smallest(net.yadaframework.raw.YadaIntDimension...)">
<h3>smallest</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></span>&nbsp;<span class="element-name">smallest</span><wbr><span class="parameters">(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>...&nbsp;dimensions)</span></div>
<div class="block">Returns the smallest dimension, or the first one if there are many of the same size</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dimensions</code> - can be null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isUnset()">
<h3>isUnset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isUnset</span>()</div>
</section>
</li>
<li>
<section class="detail" id="isBiggerThan(net.yadaframework.raw.YadaIntDimension)">
<h3>isBiggerThan</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isBiggerThan</span><wbr><span class="parameters">(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;yadaIntDimension)</span></div>
<div class="block">Returns true if both dimensions are not smaller than the argument, nor both equal.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaIntDimension</code> - can be null or unset and the result would be true</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isSmallerThan(net.yadaframework.raw.YadaIntDimension)">
<h3>isSmallerThan</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSmallerThan</span><wbr><span class="parameters">(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;yadaIntDimension)</span></div>
<div class="block">Returns true if both dimensions are not bigger than the argument, nor both equal.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaIntDimension</code> - can be null or unset and the result would be true</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isAnyBiggerThan(net.yadaframework.raw.YadaIntDimension)">
<h3>isAnyBiggerThan</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isAnyBiggerThan</span><wbr><span class="parameters">(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;yadaIntDimension)</span></div>
<div class="block">Returns true if at least one dimension is bigger.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaIntDimension</code> - can be null and the result would be true</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isAnySmallerThan(net.yadaframework.raw.YadaIntDimension)">
<h3>isAnySmallerThan</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isAnySmallerThan</span><wbr><span class="parameters">(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;yadaIntDimension)</span></div>
<div class="block">Returns true if at least one dimension is smaller.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaIntDimension</code> - can be null and the result would be true</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isEqualTo(net.yadaframework.raw.YadaIntDimension)">
<h3>isEqualTo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isEqualTo</span><wbr><span class="parameters">(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;yadaIntDimension)</span></div>
<div class="block">Returns true if this instance is equal to the argument</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaIntDimension</code> - can be null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWidth()">
<h3>getWidth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getWidth</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getHeight()">
<h3>getHeight</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getHeight</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setSize(int,int)">
<h3>setSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSize</span><wbr><span class="parameters">(int&nbsp;width,
 int&nbsp;height)</span></div>
<div class="block">Sets the size of this <code>YadaIntDimension</code> object to
 the specified width and height.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>width</code> - the new width for the <code>YadaIntDimension</code> object</dd>
<dd><code>height</code> - the new height for the <code>YadaIntDimension</code> object</dd>
<dt>Since:</dt>
<dd>1.2</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSize(net.yadaframework.raw.YadaIntDimension)">
<h3>setSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSize</span><wbr><span class="parameters">(<a href="YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a>&nbsp;d)</span></div>
<div class="block">Sets the size of this <code>YadaIntDimension</code> object to the specified size.</div>
</section>
</li>
<li>
<section class="detail" id="equals(java.lang.Object)">
<h3>equals</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">equals</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</span></div>
<div class="block">Checks whether two dimension objects have equal values.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hashCode()">
<h3>hashCode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">hashCode</span>()</div>
<div class="block">Returns the hash code for this <code>YadaIntDimension</code>.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<div class="block">Returns a string representation in the form WxH</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
