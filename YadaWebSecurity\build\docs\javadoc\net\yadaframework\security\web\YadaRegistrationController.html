<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaRegistrationController (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.security.web, class: YadaRegistrationController">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li><a href="#nested-class-summary">Nested</a></li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li><a href="#nested-class-summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.security.web</a></div>
<h1 title="Class YadaRegistrationController" class="title">Class YadaRegistrationController</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.security.web.YadaRegistrationController</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Controller
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaRegistrationController</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="YadaRegistrationController.YadaChangeUsernameOutcome.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameOutcome</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static enum&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="YadaRegistrationController.YadaChangeUsernameResult.html" class="type-name-link" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="YadaRegistrationController.YadaRegistrationOutcome.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationOutcome</a>&lt;<a href="YadaRegistrationController.YadaRegistrationOutcome.html" title="type parameter in YadaRegistrationController.YadaRegistrationOutcome">T</a> extends <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>,<wbr><a href="YadaRegistrationController.YadaRegistrationOutcome.html" title="type parameter in YadaRegistrationController.YadaRegistrationOutcome">R</a> extends <a href="../persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a>&gt;</code></div>
<div class="col-last even-row-color">
<div class="block">The outcome of a registration.</div>
</div>
<div class="col-first odd-row-color"><code>static enum&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="YadaRegistrationController.YadaRegistrationStatus.html" class="type-name-link" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaRegistrationController</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaRegistrationController.YadaChangeUsernameOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameOutcome</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#changeUsername(java.lang.String)" class="member-name-link">changeUsername</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;token)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Change a username after the user clicked on the confirmation email link</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T extends <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&gt;<br>T</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createNewUser(java.lang.String,java.lang.String,java.lang.String%5B%5D,java.util.Locale,java.lang.Class)" class="member-name-link">createNewUser</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;email,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clearPassword,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;userRoles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;userProfileClass)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a new user.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T extends <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>,<wbr>
R extends <a href="../persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a>&gt;<br><a href="YadaRegistrationController.YadaRegistrationOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationOutcome</a><wbr>&lt;T,<wbr>R&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleRegistrationConfirmation(java.lang.String,java.lang.String%5B%5D,java.util.Locale,jakarta.servlet.http.HttpSession,java.lang.Class,java.lang.Class)" class="member-name-link">handleRegistrationConfirmation</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;token,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;userRoles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 jakarta.servlet.http.HttpSession&nbsp;session,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;userProfileClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;R&gt;&nbsp;registrationRequestClass)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">To be called when the link in the registration confirmation email has been clicked.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#handleRegistrationRequest(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,org.springframework.validation.BindingResult,org.springframework.ui.Model,jakarta.servlet.http.HttpServletRequest,java.util.Locale)" class="member-name-link">handleRegistrationRequest</a><wbr>(<a href="../persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a>&nbsp;yadaRegistrationRequest,
 org.springframework.validation.BindingResult&nbsp;bindingResult,
 org.springframework.ui.Model&nbsp;model,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This method should be called by a registration controller to perform the actual registration</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init()" class="member-name-link">init</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#passwordChangeAfterRequest(net.yadaframework.web.form.YadaFormPasswordChange,org.springframework.validation.BindingResult,org.springframework.ui.Model,java.util.Locale)" class="member-name-link">passwordChangeAfterRequest</a><wbr>(net.yadaframework.web.form.YadaFormPasswordChange&nbsp;yadaFormPasswordChange,
 org.springframework.validation.BindingResult&nbsp;bindingResult,
 org.springframework.ui.Model&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Default method to change a user password.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#passwordChangeModal(java.lang.String,java.lang.String,net.yadaframework.web.form.YadaFormPasswordChange)" class="member-name-link">passwordChangeModal</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;token,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;username,
 net.yadaframework.web.form.YadaFormPasswordChange&nbsp;yadaFormPasswordChange)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#passwordResetForm(java.lang.String,org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">passwordResetForm</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;token,
 org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">To be called in the controller that handles the password recovery link in the email.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#yadaPasswordResetPost(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,org.springframework.validation.BindingResult,java.util.Locale,org.springframework.web.servlet.mvc.support.RedirectAttributes,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">yadaPasswordResetPost</a><wbr>(<a href="../persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a>&nbsp;yadaRegistrationRequest,
 org.springframework.validation.BindingResult&nbsp;bindingResult,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Handles the password reset form</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaRegistrationController</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaRegistrationController</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="init()">
<h3>init</h3>
<div class="member-signature"><span class="annotations">@PostConstruct
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span>()</div>
</section>
</li>
<li>
<section class="detail" id="handleRegistrationConfirmation(java.lang.String,java.lang.String[],java.util.Locale,jakarta.servlet.http.HttpSession,java.lang.Class,java.lang.Class)">
<h3>handleRegistrationConfirmation</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters-long">&lt;T extends <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>,<wbr>
R extends <a href="../persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a>&gt;</span>
<span class="return-type"><a href="YadaRegistrationController.YadaRegistrationOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationOutcome</a>&lt;T,<wbr>R&gt;</span>&nbsp;<span class="element-name">handleRegistrationConfirmation</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;token,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;userRoles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 jakarta.servlet.http.HttpSession&nbsp;session,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;userProfileClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;R&gt;&nbsp;registrationRequestClass)</span></div>
<div class="block">To be called when the link in the registration confirmation email has been clicked.
 It creates a YadaUserCredential instance with basic YadaUserProfile values which should be refined and saved by the caller</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>token</code> - </dd>
<dd><code>userRoles</code> - an array of configured roles, like <pre>new String[]{"USER"}</pre>. 
 The role name must be exactly as configured in &lt;security&gt;&lt;roles&gt;&lt;role&gt;&lt;key&gt;</dd>
<dd><code>locale</code> - </dd>
<dt>Returns:</dt>
<dd>a <a href="YadaRegistrationController.YadaRegistrationOutcome.html" title="class in net.yadaframework.security.web"><code>YadaRegistrationController.YadaRegistrationOutcome</code></a></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createNewUser(java.lang.String,java.lang.String,java.lang.String[],java.util.Locale,java.lang.Class)">
<h3>createNewUser</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T extends <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">createNewUser</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;email,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clearPassword,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;userRoles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;userProfileClass)</span></div>
<div class="block">Create a new user. Throws a runtime exception if the email already exists</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>email</code> - </dd>
<dd><code>clearPassword</code> - cleartext password</dd>
<dd><code>userRoles</code> - </dd>
<dd><code>locale</code> - </dd>
<dd><code>userProfileClass</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="handleRegistrationRequest(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,org.springframework.validation.BindingResult,org.springframework.ui.Model,jakarta.servlet.http.HttpServletRequest,java.util.Locale)">
<h3>handleRegistrationRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">handleRegistrationRequest</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a>&nbsp;yadaRegistrationRequest,
 org.springframework.validation.BindingResult&nbsp;bindingResult,
 org.springframework.ui.Model&nbsp;model,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</span></div>
<div class="block">This method should be called by a registration controller to perform the actual registration</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaRegistrationRequest</code> - </dd>
<dd><code>bindingResult</code> - </dd>
<dd><code>model</code> - the flag "yadaUserExists" il set to true when the user already exists</dd>
<dd><code>locale</code> - </dd>
<dt>Returns:</dt>
<dd>true if the user has been registered, false otherwise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="passwordChangeAfterRequest(net.yadaframework.web.form.YadaFormPasswordChange,org.springframework.validation.BindingResult,org.springframework.ui.Model,java.util.Locale)">
<h3>passwordChangeAfterRequest</h3>
<div class="member-signature"><span class="annotations">@RequestMapping("/passwordChangeAfterRequest")
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">passwordChangeAfterRequest</span><wbr><span class="parameters">(net.yadaframework.web.form.YadaFormPasswordChange&nbsp;yadaFormPasswordChange,
 org.springframework.validation.BindingResult&nbsp;bindingResult,
 org.springframework.ui.Model&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</span></div>
<div class="block">Default method to change a user password.</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="passwordChangeModal(java.lang.String,java.lang.String,net.yadaframework.web.form.YadaFormPasswordChange)">
<h3>passwordChangeModal</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
@RequestMapping("/yadaPasswordChangeModal/{token}/{username}/end")
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">passwordChangeModal</span><wbr><span class="parameters">(@PathVariable
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;token,
 @PathVariable
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;username,
 @ModelAttribute
 net.yadaframework.web.form.YadaFormPasswordChange&nbsp;yadaFormPasswordChange)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Called via ajax to open the final password change modal</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>token</code> - </dd>
<dd><code>username</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="passwordResetForm(java.lang.String,org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>passwordResetForm</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">passwordResetForm</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;token,
 org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="block">To be called in the controller that handles the password recovery link in the email.
 It creates these model attributes: username, token, dialogType=passwordRecovery</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>false for an invalid request (link expired), true if valid</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaPasswordResetPost(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,org.springframework.validation.BindingResult,java.util.Locale,org.springframework.web.servlet.mvc.support.RedirectAttributes,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)">
<h3>yadaPasswordResetPost</h3>
<div class="member-signature"><span class="annotations">@RequestMapping("/yadaPasswordResetPost")
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">yadaPasswordResetPost</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a>&nbsp;yadaRegistrationRequest,
 org.springframework.validation.BindingResult&nbsp;bindingResult,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span></div>
<div class="block">Handles the password reset form</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaRegistrationRequest</code> - will contain the email address that requires a password reset</dd>
<dd><code>bindingResult</code> - </dd>
<dd><code>locale</code> - </dd>
<dd><code>redirectAttributes</code> - </dd>
<dd><code>request</code> - </dd>
<dd><code>response</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="changeUsername(java.lang.String)">
<h3>changeUsername</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaRegistrationController.YadaChangeUsernameOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameOutcome</a></span>&nbsp;<span class="element-name">changeUsername</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;token)</span></div>
<div class="block">Change a username after the user clicked on the confirmation email link</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>token</code> - </dd>
<dd><code>fromUsername</code> - the old username (email)</dd>
<dd><code>locale</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
