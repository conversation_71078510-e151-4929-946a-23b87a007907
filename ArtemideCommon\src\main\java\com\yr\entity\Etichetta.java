package com.yr.entity;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.Type;

@Entity
@Table(uniqueConstraints = @UniqueConstraint( columnNames={"codice", "anno"} ))
public class Etichetta implements Serializable {

	public static final Date YEAR_MISSING = new GregorianCalendar(1900,0, 1).getTime();
	
	private static final long serialVersionUID = -3995184369255643362L;

	@Id	@GeneratedValue(strategy=GenerationType.IDENTITY)
	private Long id;
	
	@Column(length=32)
	private String codice;
	
	@Column(nullable=false)
	@Temporal(TemporalType.DATE)
	private Date anno = YEAR_MISSING;
	
	private int etichetta;
	
	public Etichetta() {
	}

	public Etichetta(String codice, int etichetta) {
		setCodice(codice);
		setEtichetta(etichetta);
	}

	public Etichetta(String codice, Date anno, int etichetta) {
		setCodice(codice);
		setAnno(anno);
		setEtichetta(etichetta);
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	
	public String getCodice() {
		return codice;
	}

	public void setCodice(String codice) {
		this.codice = codice;
	}

	public Date getAnno() {
		return anno;
	}

	public void setAnno(Date anno) {
		if (anno == null) {
			this.anno = YEAR_MISSING;
		} else {
			GregorianCalendar gc = new GregorianCalendar();
			gc.setTime(anno);
			gc.set(Calendar.HOUR_OF_DAY, 0);
			gc.set(Calendar.MINUTE, 0);
			gc.set(Calendar.SECOND, 0);
			gc.set(Calendar.MILLISECOND, 0);
			this.anno = gc.getTime();
		}
	}

	public int getEtichetta() {
		return etichetta;
	}

	public void setEtichetta(int etichetta) {
		this.etichetta = etichetta;
	}

	@Override
	public String toString() {
		SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
		return "(" + this.etichetta + ") " + this.codice + " " + sdf.format(this.getAnno());
	}

}
