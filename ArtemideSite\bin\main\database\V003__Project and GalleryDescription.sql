# New table for GalleryDescription
# 25 July 2017
create table GalleryDescription (id bigint not null auto_increment, description varchar(512), plusPositionx float not null, plusPositiony float not null, pos integer not null, subfamily_id bigint, primary key (id)) ENGINE=InnoDB;
create table GalleryDescription_SortedUploadedFiles (GalleryDescription_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255), primary key (GalleryDescription_id, sortedUploadedFilesMap_KEY), unique (sortedUploadedFilesMap_id)) ENGINE=InnoDB;

alter table GalleryDescription add index FK195D096AFB787D17 (subfamily_id), add constraint FK195D096AFB787D17 foreign key (subfamily_id) references Subfamily (id);
alter table GalleryDescription_SortedUploadedFiles add index FKAA48E5C5F9E3541D (GalleryDescription_id), add constraint FKAA48E5C5F9E3541D foreign key (GalleryDescription_id) references GalleryDescription (id);
alter table GalleryDescription_SortedUploadedFiles add index FKAA48E5C521033D80 (sortedUploadedFilesMap_id), add constraint FKAA48E5C521033D80 foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);

# New data for Projects
# 27 July 2017
create table ProjectAuthorTitle (id bigint not null auto_increment, localeCode varchar(5), value longtext, Project_id bigint, primary key (id)) ENGINE=InnoDB;

alter table ProjectAuthorTitle add index FKBF4BAED45D4DE037 (Project_id), add constraint FKBF4BAED45D4DE037 foreign key (Project_id) references Project (id);
alter table Project add year varchar(4);

