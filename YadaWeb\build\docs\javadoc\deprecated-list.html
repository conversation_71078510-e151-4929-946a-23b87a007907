<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Deprecated List (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="deprecated elements">
<meta name="generator" content="javadoc/DeprecatedListWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="deprecated-list-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="nav-bar-cell1-rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#deprecated">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Deprecated API" class="title">Deprecated API</h1>
</div>
<h2 title="Contents">Contents</h2>
<ul class="contents-list">
<li id="contents-class"><a href="#class">Classes</a></li>
<li id="contents-exception-class"><a href="#exception-class">Exception Classes</a></li>
<li id="contents-method"><a href="#method">Methods</a></li>
</ul>
<ul class="block-list">
<li>
<div id="class">
<div class="caption"><span>Deprecated Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaDateFormatter.html" title="class in net.yadaframework.components">net.yadaframework.components.YadaDateFormatter</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/core/YadaLocalePathLinkBuilder.html" title="class in net.yadaframework.core">net.yadaframework.core.YadaLocalePathLinkBuilder</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/persistence/YadaDao.html" title="class in net.yadaframework.persistence">net.yadaframework.persistence.YadaDao</a></div>
<div class="col-last even-row-color">
<div class="block">because you will eventually need a specific select, so better make a specific DAO which has type checking</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/persistence/YadaSqlBuilder.html" title="class in net.yadaframework.persistence">net.yadaframework.persistence.YadaSqlBuilder</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/web/dialect/YadaSrcAttrProcessor.html" title="class in net.yadaframework.web.dialect">net.yadaframework.web.dialect.YadaSrcAttrProcessor</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/web/dialect/YadaSrcsetAttrProcessor.html" title="class in net.yadaframework.web.dialect">net.yadaframework.web.dialect.YadaSrcsetAttrProcessor</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/web/YadaJsonView.html" title="class in net.yadaframework.web">net.yadaframework.web.YadaJsonView</a></div>
<div class="col-last even-row-color"></div>
</div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div id="exception-class">
<div class="caption"><span>Deprecated Exception Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Exception Class</div>
<div class="table-header col-last">Description</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/exceptions/InternalException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.InternalException</a></div>
<div class="col-last even-row-color">
<div class="block">use YadaInternalException instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/exceptions/InvalidValueException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.InvalidValueException</a></div>
<div class="col-last odd-row-color">
<div class="block">use YadaInvalidException instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/exceptions/SystemException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.SystemException</a></div>
<div class="col-last even-row-color">
<div class="block">use YadaSystemException instead</div>
</div>
</div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div id="method">
<div class="caption"><span>Deprecated Methods</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaEmailService.html#sendHtmlEmail(java.lang.String%5B%5D,java.lang.String%5B%5D,java.lang.String,java.lang.String,java.lang.Object%5B%5D,java.util.Map,java.util.Map,java.util.Map,java.util.Locale,boolean)">net.yadaframework.components.YadaEmailService.sendHtmlEmail<wbr>(String[], String[], String, String, Object[], Map&lt;String, Object&gt;, Map&lt;String, String&gt;, Map&lt;String, File&gt;, Locale, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">use <a href="net/yadaframework/components/YadaEmailBuilder.html" title="class in net.yadaframework.components"><code>YadaEmailBuilder</code></a> for a cleaner api</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaEmailService.html#sendHtmlEmail(java.lang.String%5B%5D,java.lang.String%5B%5D,java.lang.String,java.lang.String,java.lang.Object%5B%5D,java.util.Map,java.util.Map,java.util.Map,java.util.Locale,boolean,boolean)">net.yadaframework.components.YadaEmailService.sendHtmlEmail<wbr>(String[], String[], String, String, Object[], Map&lt;String, Object&gt;, Map&lt;String, String&gt;, Map&lt;String, File&gt;, Locale, boolean, boolean)</a></div>
<div class="col-last odd-row-color">
<div class="block">use <a href="net/yadaframework/components/YadaEmailBuilder.html" title="class in net.yadaframework.components"><code>YadaEmailBuilder</code></a> for a cleaner api</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaEmailService.html#sendHtmlEmail(java.lang.String%5B%5D,java.lang.String,java.lang.Object%5B%5D,java.util.Map,java.util.Map,java.util.Locale,boolean)">net.yadaframework.components.YadaEmailService.sendHtmlEmail<wbr>(String[], String, Object[], Map&lt;String, Object&gt;, Map&lt;String, String&gt;, Locale, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">use <a href="net/yadaframework/components/YadaEmailBuilder.html" title="class in net.yadaframework.components"><code>YadaEmailBuilder</code></a> for a cleaner api</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaEmailService.html#sendHtmlEmail(java.lang.String%5B%5D,java.lang.String,java.lang.String,java.lang.Object%5B%5D,java.util.Map,java.util.Map,java.util.Locale,boolean)">net.yadaframework.components.YadaEmailService.sendHtmlEmail<wbr>(String[], String, String, Object[], Map&lt;String, Object&gt;, Map&lt;String, String&gt;, Locale, boolean)</a></div>
<div class="col-last odd-row-color">
<div class="block">use <a href="net/yadaframework/components/YadaEmailBuilder.html" title="class in net.yadaframework.components"><code>YadaEmailBuilder</code></a> for a cleaner api</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaEmailService.html#sendHtmlEmail(net.yadaframework.web.YadaEmailParam)">net.yadaframework.components.YadaEmailService.sendHtmlEmail<wbr>(YadaEmailParam)</a></div>
<div class="col-last even-row-color">
<div class="block">use <a href="net/yadaframework/components/YadaEmailBuilder.html" title="class in net.yadaframework.components"><code>YadaEmailBuilder</code></a> for a cleaner api</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaFileManager.html#deleteFileAttachment(java.lang.Long)">net.yadaframework.components.YadaFileManager.deleteFileAttachment<wbr>(Long)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaNotify.html#instance(org.springframework.ui.Model)">net.yadaframework.components.YadaNotify.instance<wbr>(Model)</a></div>
<div class="col-last even-row-color">
<div class="block">use "@Autowired YadaNotify yadaNotify", followed by "yadaNotify.title(model)..."</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaNotify.html#instance(org.springframework.web.servlet.mvc.support.RedirectAttributes)">net.yadaframework.components.YadaNotify.instance<wbr>(RedirectAttributes)</a></div>
<div class="col-last odd-row-color">
<div class="block">use "@Autowired YadaNotify yadaNotify", followed by "yadaNotify.title(redirectAttributes)..."</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaNotify.html#isNotifyModalPending(jakarta.servlet.http.HttpServletRequest)">net.yadaframework.components.YadaNotify.isNotifyModalPending<wbr>(HttpServletRequest)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaNotify.html#isYadaError(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)">net.yadaframework.components.YadaNotify.isYadaError<wbr>(Model, RedirectAttributes)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaNotify.html#isYadaNotifySaved(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)">net.yadaframework.components.YadaNotify.isYadaNotifySaved<wbr>(Model, RedirectAttributes)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaNotify.html#redirectOnClose(java.lang.String)">net.yadaframework.components.YadaNotify.redirectOnClose<wbr>(String)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaNotify.html#script(java.lang.String)">net.yadaframework.components.YadaNotify.script<wbr>(String)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaAutoclose(long)">net.yadaframework.components.YadaNotify.yadaAutoclose<wbr>(long)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaError()">net.yadaframework.components.YadaNotify.yadaError()</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaInfo()">net.yadaframework.components.YadaNotify.yadaInfo()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaMessage(java.lang.String)">net.yadaframework.components.YadaNotify.yadaMessage<wbr>(String)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaMessageKey(java.lang.String...)">net.yadaframework.components.YadaNotify.yadaMessageKey<wbr>(String...)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaMessageSource(org.springframework.context.MessageSource,java.util.Locale)">net.yadaframework.components.YadaNotify.yadaMessageSource<wbr>(MessageSource, Locale)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaOk()">net.yadaframework.components.YadaNotify.yadaOk()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaReloadOnClose()">net.yadaframework.components.YadaNotify.yadaReloadOnClose()</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaSave()">net.yadaframework.components.YadaNotify.yadaSave()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaTitle(java.lang.String)">net.yadaframework.components.YadaNotify.yadaTitle<wbr>(String)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaNotify.html#yadaTitleKey(java.lang.String...)">net.yadaframework.components.YadaNotify.yadaTitleKey<wbr>(String...)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaUtil.html#exec(java.lang.String,java.util.List,java.io.ByteArrayOutputStream)">net.yadaframework.components.YadaUtil.exec<wbr>(String, List&lt;String&gt;, ByteArrayOutputStream)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaUtil.html#exec(java.lang.String,java.util.List,java.util.Map,java.io.ByteArrayOutputStream)">net.yadaframework.components.YadaUtil.exec<wbr>(String, List&lt;String&gt;, Map, ByteArrayOutputStream)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaUtil.html#exec(java.lang.String,java.util.Map)">net.yadaframework.components.YadaUtil.exec<wbr>(String, Map)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaUtil.html#makeTempFolder()">net.yadaframework.components.YadaUtil.makeTempFolder()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaUtil.html#normalizzaCellulareItaliano(java.lang.String)">net.yadaframework.components.YadaUtil.normalizzaCellulareItaliano<wbr>(String)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaUtil.html#reduceToSafeFilename(java.lang.String)">net.yadaframework.components.YadaUtil.reduceToSafeFilename<wbr>(String)</a></div>
<div class="col-last odd-row-color">
<div class="block">Does not produce the same results of all OS</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaUtil.html#reduceToSafeFilename(java.lang.String,boolean)">net.yadaframework.components.YadaUtil.reduceToSafeFilename<wbr>(String, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">Does not produce the same results of all OS</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaUtil.html#roundBackToHour(java.util.Date)">net.yadaframework.components.YadaUtil.roundBackToHour<wbr>(Date)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaUtil.html#validaCellulare(java.lang.String)">net.yadaframework.components.YadaUtil.validaCellulare<wbr>(String)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#callScriptOnModal(java.lang.String,org.springframework.ui.Model)">net.yadaframework.components.YadaWebUtil.callScriptOnModal<wbr>(String, Model)</a></div>
<div class="col-last odd-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#getClientIp(jakarta.servlet.http.HttpServletRequest)">net.yadaframework.components.YadaWebUtil.getClientIp<wbr>(HttpServletRequest)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#getWebappAddress(jakarta.servlet.http.HttpServletRequest)">net.yadaframework.components.YadaWebUtil.getWebappAddress<wbr>(HttpServletRequest)</a></div>
<div class="col-last odd-row-color">
<div class="block">use YadaConfiguration.getWebappAddress instead, because this version does not work behind an ajp connector</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#isModalError(org.springframework.ui.Model)">net.yadaframework.components.YadaWebUtil.isModalError<wbr>(Model)</a></div>
<div class="col-last even-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#isNotifyModalPending(jakarta.servlet.http.HttpServletRequest)">net.yadaframework.components.YadaWebUtil.isNotifyModalPending<wbr>(HttpServletRequest)</a></div>
<div class="col-last odd-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#isNotifyModalRequested(org.springframework.ui.Model)">net.yadaframework.components.YadaWebUtil.isNotifyModalRequested<wbr>(Model)</a></div>
<div class="col-last even-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#modalAutoclose(long,org.springframework.ui.Model)">net.yadaframework.components.YadaWebUtil.modalAutoclose<wbr>(long, Model)</a></div>
<div class="col-last odd-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#modalAutoclose(long,org.springframework.web.servlet.mvc.support.RedirectAttributes)">net.yadaframework.components.YadaWebUtil.modalAutoclose<wbr>(long, RedirectAttributes)</a></div>
<div class="col-last even-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#modalError(java.lang.String,java.lang.String,org.springframework.ui.Model)">net.yadaframework.components.YadaWebUtil.modalError<wbr>(String, String, Model)</a></div>
<div class="col-last odd-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#modalError(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)">net.yadaframework.components.YadaWebUtil.modalError<wbr>(String, String, RedirectAttributes)</a></div>
<div class="col-last even-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#modalInfo(java.lang.String,java.lang.String,org.springframework.ui.Model)">net.yadaframework.components.YadaWebUtil.modalInfo<wbr>(String, String, Model)</a></div>
<div class="col-last odd-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#modalInfo(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)">net.yadaframework.components.YadaWebUtil.modalInfo<wbr>(String, String, RedirectAttributes)</a></div>
<div class="col-last even-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#modalOk(java.lang.String,java.lang.String,org.springframework.ui.Model)">net.yadaframework.components.YadaWebUtil.modalOk<wbr>(String, String, Model)</a></div>
<div class="col-last odd-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#modalOk(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)">net.yadaframework.components.YadaWebUtil.modalOk<wbr>(String, String, RedirectAttributes)</a></div>
<div class="col-last even-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#modalReloadOnClose(org.springframework.ui.Model)">net.yadaframework.components.YadaWebUtil.modalReloadOnClose<wbr>(Model)</a></div>
<div class="col-last odd-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#modalReloadOnClose(org.springframework.web.servlet.mvc.support.RedirectAttributes)">net.yadaframework.components.YadaWebUtil.modalReloadOnClose<wbr>(RedirectAttributes)</a></div>
<div class="col-last even-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/components/YadaWebUtil.html#notifyModal(java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)">net.yadaframework.components.YadaWebUtil.notifyModal<wbr>(String, String, String, String, Model)</a></div>
<div class="col-last odd-row-color">
<div class="block">Use YadaNotify instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/core/CloneableFiltered.html#getExcludedFields()">net.yadaframework.core.CloneableFiltered.getExcludedFields()</a></div>
<div class="col-last even-row-color">
<div class="block">use <a href="net/yadaframework/components/YadaCopyNot.html" title="annotation interface in net.yadaframework.components"><code>YadaCopyNot</code></a> on single fields instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/core/YadaConfiguration.html#getLocaleSet()">net.yadaframework.core.YadaConfiguration.getLocaleSet()</a></div>
<div class="col-last odd-row-color">
<div class="block">because doesn't consider countries when configured</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/core/YadaConfiguration.html#getLocaleStrings()">net.yadaframework.core.YadaConfiguration.getLocaleStrings()</a></div>
<div class="col-last even-row-color">
<div class="block">because doesn't consider countries when configured</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/core/YadaConfiguration.html#getLocalSet(java.lang.String,java.lang.String,java.util.Locale,org.springframework.context.MessageSource)">net.yadaframework.core.YadaConfiguration.getLocalSet<wbr>(String, String, Locale, MessageSource)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/core/YadaConfiguration.html#getRoleName(java.lang.Integer)">net.yadaframework.core.YadaConfiguration.getRoleName<wbr>(Integer)</a></div>
<div class="col-last even-row-color">
<div class="block">use <a href="net/yadaframework/core/YadaConfiguration.html#getRoleKey(java.lang.Integer)"><code>YadaConfiguration.getRoleKey(Integer)</code></a> instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/core/YadaConfiguration.html#getServerAddress(jakarta.servlet.http.HttpServletRequest)">net.yadaframework.core.YadaConfiguration.getServerAddress<wbr>(HttpServletRequest)</a></div>
<div class="col-last odd-row-color">
<div class="block">use <a href="net/yadaframework/core/YadaConfiguration.html#getServerAddress()"><code>YadaConfiguration.getServerAddress()</code></a> instead</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/persistence/entity/YadaAttachedFile.html#calcAndSetTargetFile(java.lang.String,java.lang.String,java.lang.Integer,net.yadaframework.persistence.entity.YadaAttachedFile.YadaAttachedFileType,net.yadaframework.core.YadaConfiguration)">net.yadaframework.persistence.entity.YadaAttachedFile.calcAndSetTargetFile<wbr>(String, String, Integer, YadaAttachedFile.YadaAttachedFileType, YadaConfiguration)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/persistence/entity/YadaAttachedFile.html#calcAndSetTargetFile(java.lang.String,java.lang.String,net.yadaframework.persistence.entity.YadaAttachedFile.YadaAttachedFileType,net.yadaframework.raw.YadaIntDimension,net.yadaframework.core.YadaConfiguration)">net.yadaframework.persistence.entity.YadaAttachedFile.calcAndSetTargetFile<wbr>(String, String, YadaAttachedFile.YadaAttachedFileType, YadaIntDimension, YadaConfiguration)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/persistence/entity/YadaAttachedFile.html#getAbsoluteFile(net.yadaframework.persistence.entity.YadaAttachedFile.YadaAttachedFileType,net.yadaframework.core.YadaConfiguration)">net.yadaframework.persistence.entity.YadaAttachedFile.getAbsoluteFile<wbr>(YadaAttachedFile.YadaAttachedFileType, YadaConfiguration)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/persistence/repository/YadaAttachedFileDao.html#findById(java.lang.Long)">net.yadaframework.persistence.repository.YadaAttachedFileDao.findById<wbr>(Long)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/persistence/YadaDataTableDao.html#getJsonPage(net.yadaframework.web.YadaDatatablesRequest,java.lang.Class,java.util.Locale)">net.yadaframework.persistence.YadaDataTableDao.getJsonPage<wbr>(YadaDatatablesRequest, Class&lt;?&gt;, Locale)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/persistence/YadaSql.html#setParameterNotNull(java.lang.String,java.lang.Object)">net.yadaframework.persistence.YadaSql.setParameterNotNull<wbr>(String, Object)</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/web/YadaCropImage.html#cropDesktop()">net.yadaframework.web.YadaCropImage.cropDesktop()</a></div>
<div class="col-last even-row-color">
<div class="block">this flag depends on the presence of the relative dimensions in the configuration</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/web/YadaCropImage.html#cropMobile()">net.yadaframework.web.YadaCropImage.cropMobile()</a></div>
<div class="col-last odd-row-color">
<div class="block">this flag depends on the presence of the relative dimensions in the configuration</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/web/YadaCropImage.html#cropPdf()">net.yadaframework.web.YadaCropImage.cropPdf()</a></div>
<div class="col-last even-row-color">
<div class="block">this flag depends on the presence of the relative dimensions in the configuration</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/web/YadaPageRequest.html#addSort(java.lang.String,java.lang.Boolean,java.lang.Boolean)">net.yadaframework.web.YadaPageRequest.addSort<wbr>(String, Boolean, Boolean)</a></div>
<div class="col-last odd-row-color">
<div class="block">use <a href="net/yadaframework/web/YadaPageRequest.html#appendSort(java.lang.String)"><code>YadaPageRequest.appendSort(String)</code></a> and <a href="net/yadaframework/web/YadaPageRequest.html#prependSort(java.lang.String)"><code>YadaPageRequest.prependSort(String)</code></a> instead</div>
</div>
</div>
</div>
</li>
</ul>
<script type="text/javascript">document.addEventListener("DOMContentLoaded", function(e) {
    document.querySelectorAll('input[type="checkbox"]').forEach(
        function(c) {
            c.disabled = false;
            c.onclick();
        });
    });
window.addEventListener("load", function(e) {
    document.querySelectorAll('input[type="checkbox"]').forEach(
        function(c) {
            c.onclick();
        });
    });
</script>
</main>
</div>
</div>
</body>
</html>
