# Tabelle per il Configuratore
# Aggiunta relazione tra shape e modello con mappa indicizzata da sottofamiglia 

create table ConfiguratorShape_YadaAttachedFile (ConfiguratorShape_id bigint not null, models_id bigint not null, Subfamily_id bigint not null, primary key (ConfiguratorShape_id, Subfamily_id)) engine=InnoDB;
alter table ConfiguratorShape_YadaAttachedFile add constraint UK_nnu00umoxnw45wa4orq3hb95i unique (models_id);
alter table ConfiguratorShape_YadaAttachedFile add constraint FKthshilmssooibg8iocsy70d0x foreign key (models_id) references YadaAttachedFile (id);
alter table ConfiguratorShape_YadaAttachedFile add constraint FK1conw8c7gtwaymyks1y0ipylm foreign key (Subfamily_id) references Subfamily (id);
alter table ConfiguratorShape_YadaAttachedFile add constraint FKbd44wkqq5625flmk4e1wu5e0o foreign key (ConfiguratorShape_id) references ConfiguratorShape (id);

