<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Yada<PERSON><PERSON> (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.components, class: YadaUtil">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.components</a></div>
<h1 title="Class YadaUtil" class="title">Class YadaUtil</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.components.YadaUtil</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Component
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaUtil</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static org.springframework.context.ApplicationContext</code></div>
<div class="col-second even-row-color"><code><a href="#applicationContext" class="member-name-link">applicationContext</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final <a href="YadaUtil.html" title="class in net.yadaframework.components">YadaUtil</a></code></div>
<div class="col-second odd-row-color"><code><a href="#INSTANCE" class="member-name-link">INSTANCE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Instance to be used when autowiring is not available</div>
</div>
<div class="col-first even-row-color"><code>static org.springframework.context.MessageSource</code></div>
<div class="col-second even-row-color"><code><a href="#messageSource" class="member-name-link">messageSource</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final long</code></div>
<div class="col-second odd-row-color"><code><a href="#MILLIS_IN_DAY" class="member-name-link">MILLIS_IN_DAY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final long</code></div>
<div class="col-second even-row-color"><code><a href="#MILLIS_IN_HOUR" class="member-name-link">MILLIS_IN_HOUR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final long</code></div>
<div class="col-second odd-row-color"><code><a href="#MILLIS_IN_MINUTE" class="member-name-link">MILLIS_IN_MINUTE</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaUtil</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#abbreviate(java.lang.String,int,boolean)" class="member-name-link">abbreviate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;string,
 int&nbsp;length,
 boolean&nbsp;breakAtWord)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Cuts the input string at the given length, optionally keeping the whole last word and adding some dots at the end</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#abbreviate(java.lang.String,int,boolean,java.lang.String)" class="member-name-link">abbreviate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;string,
 int&nbsp;length,
 boolean&nbsp;breakAtWord,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ellipsis)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Cuts the input string at the given length, optionally keeping the whole last word and adding some characters at the end</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addDays(java.util.Calendar,int)" class="member-name-link">addDays</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar,
 int&nbsp;days)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Adds or removes the days.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addDays(java.util.Date,int)" class="member-name-link">addDays</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 int&nbsp;days)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Aggiunge (o rimuove) i giorni indicati dalla data</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addDaysClone(java.util.Calendar,int)" class="member-name-link">addDaysClone</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;source,
 int&nbsp;days)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Adds or removes the days.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addHours(java.util.Date,int)" class="member-name-link">addHours</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 int&nbsp;hours)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Aggiunge (o rimuove) le ore indicate dalla data</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T&gt;&nbsp;void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addIfMissing(java.util.List,T)" class="member-name-link">addIfMissing</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;&nbsp;list,
 T&nbsp;element)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an element to the list only if not there already</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T&gt;&nbsp;void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addIfNotNull(java.util.List,T)" class="member-name-link">addIfNotNull</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;&nbsp;list,
 T&nbsp;element)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an element to the list only if the element is not null</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addMinutes(java.util.Calendar,int)" class="member-name-link">addMinutes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar,
 int&nbsp;minutes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Adds or removes the minutes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addMinutes(java.util.Date,int)" class="member-name-link">addMinutes</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 int&nbsp;minutes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Aggiunge (o rimuove) i minuti indicati dalla data</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#addYears(java.util.Date,int)" class="member-name-link">addYears</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 int&nbsp;years)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Aggiunge (o rimuove) gli anni indicati dalla data (approssimato)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#autowire(java.lang.Object)" class="member-name-link">autowire</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;instance)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Perform autowiring of an instance that doesn't come from the Spring context, e.g.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#autowireAndInitialize(java.lang.Object)" class="member-name-link">autowireAndInitialize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;instance)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Perform autowiring of an instance that doesn't come from the Spring context, e.g.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanupFolder(java.nio.file.Path,java.lang.String)" class="member-name-link">cleanupFolder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;folder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Removes files from a folder starting with the prefix (can be an empty string)
 The folder itself is not removed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanupFolder(java.nio.file.Path,java.lang.String,java.util.Date)" class="member-name-link">cleanupFolder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;folder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;olderThan)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Removes files from a folder starting with the prefix (can be an empty string) and older than the given date.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#closeSilently(java.io.Closeable)" class="member-name-link">closeSilently</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>&nbsp;closeable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Close a closeable ignoring exceptions and null.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#copyEntity(net.yadaframework.core.CloneableFiltered)" class="member-name-link">copyEntity</a><wbr>(<a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a>&nbsp;source)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Copy (clone) an object via getter/setter.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#copyEntity(net.yadaframework.core.CloneableFiltered,boolean)" class="member-name-link">copyEntity</a><wbr>(<a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a>&nbsp;source,
 boolean&nbsp;setFieldDirectly)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#copyEntity(net.yadaframework.core.CloneableFiltered,java.lang.Class)" class="member-name-link">copyEntity</a><wbr>(<a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a>&nbsp;source,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;classObject)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Questo metodo crea la copia di un oggetto TRAMITE I SUOI GETTER (anche privati), facendo in modo che alcune collection/mappe vengano copiate pur restando indipendenti.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#copyEntity(net.yadaframework.core.CloneableFiltered,java.lang.Class,boolean)" class="member-name-link">copyEntity</a><wbr>(<a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a>&nbsp;source,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;classObject,
 boolean&nbsp;setFieldDirectly)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#copyEntity(net.yadaframework.core.CloneableFiltered,java.lang.Class,boolean,net.yadaframework.components.YadaAttachedFileCloneSet)" class="member-name-link">copyEntity</a><wbr>(<a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a>&nbsp;source,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;classObject,
 boolean&nbsp;setFieldDirectly,
 <a href="YadaAttachedFileCloneSet.html" title="class in net.yadaframework.components">YadaAttachedFileCloneSet</a>&nbsp;yadaAttachedFileCloneSet)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copyStream(java.io.InputStream,java.io.OutputStream,java.lang.Integer,java.lang.Long)" class="member-name-link">copyStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;inputStream,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;outputStream,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;bufferSize,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;sizeLimit)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies an inputStream to an outputStream.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createZipFile(java.io.File,java.io.File%5B%5D,java.lang.String%5B%5D)" class="member-name-link">createZipFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;sourceFiles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;filenamesNoExtension)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a zip of a list of files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createZipFile(java.io.File,java.io.File%5B%5D,java.lang.String%5B%5D,boolean)" class="member-name-link">createZipFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;sourceFiles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;filenamesNoExtension,
 boolean&nbsp;ignoreErrors)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a zip of a list of files.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createZipFileFromFolders(java.io.File,java.io.File%5B%5D)" class="member-name-link">createZipFileFromFolders</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;foldersToZip)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a zip file of a folder</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createZipProcess(java.io.File,java.io.File%5B%5D,java.lang.String%5B%5D,boolean)" class="member-name-link">createZipProcess</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;sourceFiles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;filenames,
 boolean&nbsp;fixNames)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a zip of a set of files using an external process.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dateValid(java.util.Date,java.lang.Integer)" class="member-name-link">dateValid</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;someDate,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;maxYears)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if a date is not more than maxYears years from now, not in an accurate way.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#dateWithin(java.util.Calendar,int,int,int,int)" class="member-name-link">dateWithin</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;dateToCheck,
 int&nbsp;fromMonth,
 int&nbsp;fromDayInclusive,
 int&nbsp;toMonth,
 int&nbsp;toDayExcluded)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Check if a date is within two dates expressed as month/day, regardless of the year and of the validity of such dates.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#daysAgo(int)" class="member-name-link">daysAgo</a><wbr>(int&nbsp;days)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ritorna la data nel passato per il numero di giorni indicati</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#daysBetween(java.time.ZonedDateTime,java.time.ZonedDateTime)" class="member-name-link">daysBetween</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/ZonedDateTime.html" title="class or interface in java.time" class="external-link">ZonedDateTime</a>&nbsp;olderDate,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/ZonedDateTime.html" title="class or interface in java.time" class="external-link">ZonedDateTime</a>&nbsp;earlierDate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the days between two dates.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#daysDifference(java.util.Date,java.util.Date)" class="member-name-link">daysDifference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date1,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date2)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Counts the days interval between two dates.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteAll(java.io.File,java.lang.String)" class="member-name-link">deleteAll</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;folder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delete all files in a folder that have the specified prefix</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteFileSilently(java.nio.file.Path)" class="member-name-link">deleteFileSilently</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Deletes a file without reporting any errors.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteIfEmpty(java.nio.file.Path)" class="member-name-link">deleteIfEmpty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;folder)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Deleted a folder only when empty</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteSilently(java.io.File)" class="member-name-link">deleteSilently</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delete a file ignoring errors.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#ensurePoiTempFolder()" class="member-name-link">ensurePoiTempFolder</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ensure the temporary folder for POI files exists and is writable.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#ensureSafeFilename(java.lang.String)" class="member-name-link">ensureSafeFilename</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;originalFilename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Converts a candidate filename so that it is valid on all operating systems and browsers, if needed, and also to lowercase.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#ensureSafeFilename(java.lang.String,boolean)" class="member-name-link">ensureSafeFilename</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;originalFilename,
 boolean&nbsp;toLowercase)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Converts a candidate filename so that it is valid on all operating systems and browsers, if needed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#exec(java.lang.String,java.util.List,java.io.ByteArrayOutputStream)" class="member-name-link">exec</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#exec(java.lang.String,java.util.List,java.util.Map,java.io.ByteArrayOutputStream)" class="member-name-link">exec</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#exec(java.lang.String,java.util.Map)" class="member-name-link">exec</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;shellCommandKey,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findAvailableFilename(java.lang.String,java.lang.String,java.lang.String,java.util.Set)" class="member-name-link">findAvailableFilename</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extensionNoDot,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;counterSeparator,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;usedNames)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ensure that the given filename has not been already used, by adding a counter.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#findAvailableName(java.io.File,java.lang.String)" class="member-name-link">findAvailableName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;counterSeparator)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates a file with a unique filename by appending a number after the specified separator if needed.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#findAvailableName(java.io.File,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">findAvailableName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFolder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extensionNoDot,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;counterSeparator)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates an empty file that doesn't already exist in the specified folder
 with the specified leading characters (baseName).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findAvailableNameHighest(java.io.File,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">findAvailableNameHighest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFolder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extensionNoDot,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;counterSeparator)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a file that doesn't already exist in the specified folder
 with the specified leading characters (baseName) and optional extension.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a><wbr>&lt;?&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findGenericClass(java.lang.Object)" class="member-name-link">findGenericClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;specificClassInstance)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given the instance of a "specific" class created specifying a single type T while extending a generic class,
 retrieve the class of the type T.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#formatTimeInterval(long,java.util.concurrent.TimeUnit)" class="member-name-link">formatTimeInterval</a><wbr>(long&nbsp;amount,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/concurrent/TimeUnit.html" title="class or interface in java.util.concurrent" class="external-link">TimeUnit</a>&nbsp;timeUnit)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Convert from an amount of time to a string in the format xxd:hh:mm:ss</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static org.springframework.context.ApplicationContext</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getApplicationContext()" class="member-name-link">getApplicationContext</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Ritorna un riferimento all'ApplicationContext per l'utilizzo fuori dal Container (ad esempio negli Entity)</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;T</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getBean(java.lang.Class,java.lang.Object...)" class="member-name-link">getBean</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;beanClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;args)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Get any bean defined in the Spring ApplicationContext</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getBean(java.lang.String)" class="member-name-link">getBean</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nameInApplicationContext)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Get any bean defined in the Spring ApplicationContext</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getBean(java.lang.String,java.lang.Object...)" class="member-name-link">getBean</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nameInApplicationContext,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;args)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Get any bean defined in the Spring ApplicationContext</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getClassesInPackage(java.lang.Package)" class="member-name-link">getClassesInPackage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Package.html" title="class or interface in java.lang" class="external-link">Package</a>&nbsp;thePackage)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Return all the classes of a given package.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentStackTraceFormatted()" class="member-name-link">getCurrentStackTraceFormatted</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the current stack trace as a string, formatted on separate lines</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDateFromDateTimeIsoString(java.lang.String,java.lang.String,java.util.TimeZone)" class="member-name-link">getDateFromDateTimeIsoString</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;isoDateString,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;isoTimeString,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given a ISO date, a ISO time and a timezone, return the Date.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/LocalDate.html" title="class or interface in java.time" class="external-link">LocalDate</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDays(java.time.LocalDate,java.time.LocalDate)" class="member-name-link">getDays</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/LocalDate.html" title="class or interface in java.time" class="external-link">LocalDate</a>&nbsp;fromInclusive,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/LocalDate.html" title="class or interface in java.time" class="external-link">LocalDate</a>&nbsp;toInclusive)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a list of days between two dates included.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/LocalDate.html" title="class or interface in java.time" class="external-link">LocalDate</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDays(java.util.Date,java.util.Date)" class="member-name-link">getDays</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;fromInclusive,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;toInclusive)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a list of days between two dates included.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getEmptySortedSet(java.util.List)" class="member-name-link">getEmptySortedSet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;sortOrder)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a new TreeSet that sorts values according to the order specified in the parameter.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/reflect/Field.html" title="class or interface in java.lang.reflect" class="external-link">Field</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFieldNoTraversing(java.lang.Class,java.lang.String)" class="member-name-link">getFieldNoTraversing</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;rootClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the Field of a given class, even from a superclass but not "nested" in a path</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFileExtension(java.io.File)" class="member-name-link">getFileExtension</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFileExtension(java.lang.String)" class="member-name-link">getFileExtension</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Da un nome tipo abcd.JPG ritorna "jpg"</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFileNoPath(java.lang.String)" class="member-name-link">getFileNoPath</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileWithPath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the file name given the file path</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFilesInFolder(java.nio.file.Path,java.lang.String)" class="member-name-link">getFilesInFolder</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;folderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;contains)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a list of files from a folder where the name contains the given string, sorted alphabetically</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../raw/YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getImageDimension(java.io.File)" class="member-name-link">getImageDimension</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;imageFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets the image dimensions considering the EXIF orientation flag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../raw/YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getImageDimensionDumb(java.io.File)" class="member-name-link">getImageDimensionDumb</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;imageFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Gets image dimensions for given file, ignoring orientation flag</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIsoDateStringForTimezone(java.util.Date,java.util.TimeZone)" class="member-name-link">getIsoDateStringForTimezone</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convert a date in the timezone to a ISO string, like '2011-12-03'</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIsoDateTimeStringForTimezone(java.util.Date,java.util.TimeZone)" class="member-name-link">getIsoDateTimeStringForTimezone</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;dateTime,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convert a datetime in the timezone to a ISO string, like '2011-12-03T10:15:30'</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getIsoTimeStringForTimezone(java.util.Date,java.util.TimeZone)" class="member-name-link">getIsoTimeStringForTimezone</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;time,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convert a time in the timezone to a ISO string, like '10:15' or '10:15:30'</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJsonArray(java.util.Map,java.lang.String)" class="member-name-link">getJsonArray</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;jsonSource,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;objectPath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given a json stored as a map, returns the json array at the specified key
 Non need for this method if the objectPath is a simple key: just use the Map get(key) method.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJsonAttribute(java.util.Map,java.lang.String)" class="member-name-link">getJsonAttribute</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;jsonSource,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;objectPath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given a json stored as a map, returns the value at the specified key, with optional nesting.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJsonObject(java.util.Map,java.lang.String)" class="member-name-link">getJsonObject</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;jsonSource,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;objectPath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given a json stored as a map, returns the json at the specified key.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJsonObject(java.util.Map,java.lang.String,int)" class="member-name-link">getJsonObject</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;jsonSource,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;listPath,
 int&nbsp;listIndex)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given a json stored as a map, returns the json at the specified list index</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getLastMidnight()" class="member-name-link">getLastMidnight</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the last midnight</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getLocalValue(java.util.Map)" class="member-name-link">getLocalValue</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;LocalizedValueMap)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the localized value from a map of Locale -&gt; String.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getLocalValue(java.util.Map,java.util.Locale)" class="member-name-link">getLocalValue</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;localizedValueMap,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the localized value from a map of Locale -&gt; String.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getMessage(java.lang.String,java.lang.Object...)" class="member-name-link">getMessage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;key,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;params)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Ritorna un messaggio localizzato da un contesto fuori da Spring (ad esempio in un enum o un Entity)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNewInstanceSamePackage(java.lang.Class,java.lang.String)" class="member-name-link">getNewInstanceSamePackage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;anyClassInPackage,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;simpleClassName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create an instance of a class that belongs to the same package of some given class</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getObjectToString(java.lang.Object)" class="member-name-link">getObjectToString</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;object)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a string representation of the object as Object.toString() does, even if toString() has been overridden</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRandom(int,int)" class="member-name-link">getRandom</a><wbr>(int&nbsp;minIncluded,
 int&nbsp;maxIncluded)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a random integer number</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T&gt;&nbsp;T</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRandomElement(java.util.List)" class="member-name-link">getRandomElement</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;&nbsp;list)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a random element from the list</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRandomString(int)" class="member-name-link">getRandomString</a><wbr>(int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a random string of the given length with characters in the range "A".."Z", "a".."z", "0".."9"</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRandomText(int)" class="member-name-link">getRandomText</a><wbr>(int&nbsp;minlen)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a random string (currently an hex random number)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRfcDateTimeStringForTimezone(java.util.Date,java.util.TimeZone,java.util.Locale)" class="member-name-link">getRfcDateTimeStringForTimezone</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a string for date and time in the specified timezone and locale</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRootException(java.lang.Throwable)" class="member-name-link">getRootException</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trova l'eccezione che ha causato questa, nella catena delle eccezioni</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTimestampAsRelative(java.time.ZonedDateTime,java.util.Locale,java.lang.Integer)" class="member-name-link">getTimestampAsRelative</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/ZonedDateTime.html" title="class or interface in java.time" class="external-link">ZonedDateTime</a>&nbsp;timestamp,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;maxHours)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given a date in the past, returns a string like "12 minutes ago", "2 hours ago", "today at 12:51", "yesterday at 5:32"...</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTimezoneOffsets(java.lang.String)" class="member-name-link">getTimezoneOffsets</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get a list of GMT/UTC time offsets from UTC-12:00 to UTC+14:00</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTimezones()" class="member-name-link">getTimezones</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a list of user-friendly timezones like "Europe/Rome"</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getType(java.lang.Class,java.lang.String)" class="member-name-link">getType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;rootClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributePath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Reflection to get the type of a given field, even nested or in a superclass.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init()" class="member-name-link">init</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isCodiceFiscaleValid(java.lang.String)" class="member-name-link">isCodiceFiscaleValid</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;codiceFiscale)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isEmailValid(java.lang.String)" class="member-name-link">isEmailValid</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;email)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Simple email address syntax check: the format should be X@Y.Y
 where X does not contain @ and Y does not contain @, nor .</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#isType(java.lang.Class,java.lang.Class)" class="member-name-link">isType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;fieldType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;requiredType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Check if a class is of a given type, considering superclasses and interfaces (of superclasses)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#joinFiles(java.nio.file.Path,java.lang.String,java.io.File,java.lang.Integer,java.lang.Boolean)" class="member-name-link">joinFiles</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;sourceFolder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceFilePattern,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;outputFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;depth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;deleteSource)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Merges all files matched by a pattern, in no particular order.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#joinIfNotEmpty(java.lang.String,java.lang.String...)" class="member-name-link">joinIfNotEmpty</a><wbr>(@NotNull <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;separator,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;toJoin)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Joins a number of strings, adding a separator only when the strings are not empty.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;T&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#lazyUnsafeInit(java.util.List)" class="member-name-link">lazyUnsafeInit</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;&nbsp;list)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Utility method for lazy initialization of ArrayLists.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;T&gt;&nbsp;T</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#lazyUnsafeInit(T,java.util.function.Supplier)" class="member-name-link">lazyUnsafeInit</a><wbr>(T&nbsp;instance,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/function/Supplier.html" title="class or interface in java.util.function" class="external-link">Supplier</a>&lt;T&gt;&nbsp;initializer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Utility method for lazy initialization of any object type.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeJsonObject()" class="member-name-link">makeJsonObject</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a single empty "json" object for use in other methods.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeJsonObject(java.util.Map,java.lang.String)" class="member-name-link">makeJsonObject</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;parentObject,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates an empty json object at the given path, if missing.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#makeTempFolder()" class="member-name-link">makeTempFolder</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#mapToString(java.util.Map)" class="member-name-link">mapToString</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;stringMap)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convert a map of strings to a commaspace-separated string of name=value pairs</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#md5Hash(java.lang.String)" class="member-name-link">md5Hash</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clear)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a MD5 hash of a string (from http://snippets.dzone.com/posts/show/3686)</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#millisSinceMidnight(java.util.Calendar)" class="member-name-link">millisSinceMidnight</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the number of milliseconds since midnight</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#minutesAgo(int)" class="member-name-link">minutesAgo</a><wbr>(int&nbsp;minuti)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ritorna la data nel passato per il numero di minuti indicati</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#minutesDifference(java.util.Date,java.util.Date)" class="member-name-link">minutesDifference</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;recentDate,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;oldDate)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the minutes between two dates.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static long</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#minutesDifferenceAbs(java.util.Date,java.util.Date)" class="member-name-link">minutesDifferenceAbs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;firstDate,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;secondDate)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the absolute value of the minutes between two dates.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#normalizzaCellulareItaliano(java.lang.String)" class="member-name-link">normalizzaCellulareItaliano</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cellulare)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;entityClass&gt;<br>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#prefetchLocalizedStringList(java.util.Collection,java.lang.Class,java.lang.String...)" class="member-name-link">prefetchLocalizedStringList</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;entityClass&gt;&nbsp;entities,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;entityClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;attributes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Force initialization of localized strings implemented with Map&lt;Locale, String&gt;.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;entityClass&gt;<br>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#prefetchLocalizedStringListRecursive(java.util.List,java.lang.Class,java.lang.String...)" class="member-name-link">prefetchLocalizedStringListRecursive</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;entityClass&gt;&nbsp;entities,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;entityClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;attributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Force initialization of localized strings implemented with Map&lt;Locale, String&gt;.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;targetClass&gt;<br>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#prefetchLocalizedStrings(targetClass,java.lang.Class,java.lang.String...)" class="member-name-link">prefetchLocalizedStrings</a><wbr>(targetClass&nbsp;fetchedEntity,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;targetClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;attributes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Force initialization of localized strings implemented with Map&lt;Locale, String&gt;.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static &lt;targetClass&gt;<br>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#prefetchLocalizedStringsRecursive(targetClass,java.lang.Class,java.lang.String...)" class="member-name-link">prefetchLocalizedStringsRecursive</a><wbr>(targetClass&nbsp;fetchedEntity,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;targetClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;attributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Force initialization of localized strings implemented with Map&lt;Locale, String&gt;.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#reduceToSafeFilename(java.lang.String)" class="member-name-link">reduceToSafeFilename</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;originalFilename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Does not produce the same results of all OS</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#reduceToSafeFilename(java.lang.String,boolean)" class="member-name-link">reduceToSafeFilename</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;originalFilename,
 boolean&nbsp;toLowercase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Does not produce the same results of all OS</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#relativize(java.io.File,java.io.File)" class="member-name-link">relativize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;ancestorFolder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;descendantFolder)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the path between two files or folders using forward (unix) slashes as a separator</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#relativize(java.nio.file.Path,java.nio.file.Path)" class="member-name-link">relativize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;ancestorPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;descendantPath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Finds the path between two files or folders using forward (unix) slashes as a separator.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#roundBackToHour(java.util.Calendar)" class="member-name-link">roundBackToHour</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the same calendar object aligned to the previous hour</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#roundBackToHour(java.util.Date)" class="member-name-link">roundBackToHour</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#roundBackToHour(java.util.Date,java.util.TimeZone)" class="member-name-link">roundBackToHour</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Resets minutes to zero</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#roundBackToLastMonthStart(java.util.Calendar)" class="member-name-link">roundBackToLastMonthStart</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#roundBackToMidnight(java.util.Calendar)" class="member-name-link">roundBackToMidnight</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Rounds back the calendar to the start of the day.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#roundBackToMidnight(java.util.Date,java.util.TimeZone)" class="member-name-link">roundBackToMidnight</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#roundBackToMidnightClone(java.util.Calendar)" class="member-name-link">roundBackToMidnightClone</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;source)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Create a new calendar rounded back to the start of the day.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#roundBackToMonth(java.util.Calendar)" class="member-name-link">roundBackToMonth</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#roundBackToMonth(java.util.Date,java.util.TimeZone)" class="member-name-link">roundBackToMonth</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#roundForwardToAlmostMidnight(java.util.Calendar)" class="member-name-link">roundForwardToAlmostMidnight</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Rounds forward the calendar to the end of the day at 23:59:59.999</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#roundForwardToHour(java.util.Calendar)" class="member-name-link">roundForwardToHour</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the same calendar object aligned to the next hour</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#roundFowardToMonth(java.util.Date,java.util.TimeZone)" class="member-name-link">roundFowardToMonth</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sameDay(java.util.Date,java.util.Date)" class="member-name-link">sameDay</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;a,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;b)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the two dates are on the same day</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setApplicationContext(org.springframework.context.ApplicationContext)" class="member-name-link">setApplicationContext</a><wbr>(org.springframework.context.ApplicationContext&nbsp;applicationContext)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJsonAttribute(java.util.Map,java.lang.String,java.lang.Object)" class="member-name-link">setJsonAttribute</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;jsonObject,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a json property at the given path.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#shellExec(java.lang.String,java.util.List,java.io.ByteArrayOutputStream)" class="member-name-link">shellExec</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run an external shell command without keyword substitution in the parameters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#shellExec(java.lang.String,java.util.List,java.util.Map,java.io.ByteArrayOutputStream)" class="member-name-link">shellExec</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#shellExec(java.lang.String,java.util.List,java.util.Map,java.io.ByteArrayOutputStream,int)" class="member-name-link">shellExec</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream,
 int&nbsp;timeoutSeconds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run an external shell command.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#shellExec(java.lang.String,java.util.Map)" class="member-name-link">shellExec</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;shellCommandKey,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run an external shell command that has been defined in the configuration file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#shellExec(java.lang.String,java.util.Map,java.io.ByteArrayOutputStream)" class="member-name-link">shellExec</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;shellCommandKey,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run an external shell command that has been defined in the configuration file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sleep(long)" class="member-name-link">sleep</a><wbr>(long&nbsp;milliseconds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sleepRandom(long,long)" class="member-name-link">sleepRandom</a><wbr>(long&nbsp;minMilliseconds,
 long&nbsp;maxMilliseconds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/SortedSet.html" title="class or interface in java.util" class="external-link">SortedSet</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.Entry.html" title="class or interface in java.util" class="external-link">Map.Entry</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#sortByKey(java.util.Map)" class="member-name-link">sortByKey</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/SortedSet.html" title="class or interface in java.util" class="external-link">SortedSet</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.Entry.html" title="class or interface in java.util" class="external-link">Map.Entry</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#sortByValue(java.util.Map)" class="member-name-link">sortByValue</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#splitAtWord(java.lang.String,int)" class="member-name-link">splitAtWord</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 int&nbsp;splitPoint)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Spezza una stringa in due, circa al carattere splitPoint, ma a fine parola.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#splitFileNameAndExtension(java.lang.String)" class="member-name-link">splitFileNameAndExtension</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Splits a filename in the prefix and the extension parts.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#splitHtml(java.lang.String,int)" class="member-name-link">splitHtml</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;htmlToSplit,
 int&nbsp;splitPos)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Split an HTML string in two parts, not breaking words, handling closing and reopening of html tags.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>double</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stringToDouble(java.lang.String,java.util.Locale)" class="member-name-link">stringToDouble</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Parse a string as a double, using the correct decimal separator (if any).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#stripCounterFromFilename(java.lang.String,java.lang.String)" class="member-name-link">stripCounterFromFilename</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;counterSeparator)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Remove a counter that has been added by <a href="#findAvailableName(java.io.File,java.lang.String,java.lang.String,java.lang.String)"><code>findAvailableName(java.io.File, java.lang.String, java.lang.String, java.lang.String)</code></a></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#validaCellulare(java.lang.String)" class="member-name-link">validaCellulare</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cellulare)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="applicationContext">
<h3>applicationContext</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">org.springframework.context.ApplicationContext</span>&nbsp;<span class="element-name">applicationContext</span></div>
</section>
</li>
<li>
<section class="detail" id="messageSource">
<h3>messageSource</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">org.springframework.context.MessageSource</span>&nbsp;<span class="element-name">messageSource</span></div>
</section>
</li>
<li>
<section class="detail" id="MILLIS_IN_MINUTE">
<h3>MILLIS_IN_MINUTE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">MILLIS_IN_MINUTE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.components.YadaUtil.MILLIS_IN_MINUTE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MILLIS_IN_HOUR">
<h3>MILLIS_IN_HOUR</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">MILLIS_IN_HOUR</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.components.YadaUtil.MILLIS_IN_HOUR">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MILLIS_IN_DAY">
<h3>MILLIS_IN_DAY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">MILLIS_IN_DAY</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.components.YadaUtil.MILLIS_IN_DAY">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="INSTANCE">
<h3>INSTANCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="YadaUtil.html" title="class in net.yadaframework.components">YadaUtil</a></span>&nbsp;<span class="element-name">INSTANCE</span></div>
<div class="block">Instance to be used when autowiring is not available</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaUtil</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaUtil</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="init()">
<h3>init</h3>
<div class="member-signature"><span class="annotations">@EventListener(org.springframework.context.event.ContextRefreshedEvent.class)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span>()</div>
</section>
</li>
<li>
<section class="detail" id="ensurePoiTempFolder()">
<h3>ensurePoiTempFolder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">ensurePoiTempFolder</span>()</div>
<div class="block">Ensure the temporary folder for POI files exists and is writable.
 It must be called every time before using POI because the temp folder is
 periodically deleted by tomcat and POI does not recreate it automatically (on linux).</div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="../exceptions/YadaSystemException.html" title="class in net.yadaframework.exceptions">YadaSystemException</a></code> - if the folder cannot be created or is not writable</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="lazyUnsafeInit(T,java.util.function.Supplier)">
<h3 id="lazyUnsafeInit(java.lang.Object,java.util.function.Supplier)">lazyUnsafeInit</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">lazyUnsafeInit</span><wbr><span class="parameters">(T&nbsp;instance,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/function/Supplier.html" title="class or interface in java.util.function" class="external-link">Supplier</a>&lt;T&gt;&nbsp;initializer)</span></div>
<div class="block">Utility method for lazy initialization of any object type. Not thread safe.
 Example: <pre>
 private MyThing myThing;
 ...
 this.myThing = YadaUtil.lazyUnsafeInit(this.myThing, MyThing::new);
 return this.myThing;
 ...
 this.myThing = YadaUtil.lazyUnsafeInit(this.myThing, () -&gt; new MyThing(value));
 </pre></div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - The type of the instance.</dd>
<dt>Parameters:</dt>
<dd><code>instance</code> - The instance to check and initialize if null.</dd>
<dd><code>initializer</code> - A <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/function/Supplier.html" title="class or interface in java.util.function" class="external-link"><code>Supplier</code></a> that provides a new instance if the current one is null.</dd>
<dt>Returns:</dt>
<dd>The initialized instance.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li>
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>YadaUtil#lazyVolatileInit(List)</code></pre>
</details>
</li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="lazyUnsafeInit(java.util.List)">
<h3>lazyUnsafeInit</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;</span>&nbsp;<span class="element-name">lazyUnsafeInit</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;&nbsp;list)</span></div>
<div class="block">Utility method for lazy initialization of ArrayLists. Not thread safe.
 Example: <pre>
 private List someList;
 ...
 this.someList = YadaUtil.lazyUnsafeInit(this.someList);
 return this.someList;
 </pre></div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - </dd>
<dt>Parameters:</dt>
<dd><code>list</code> - a list or null</dd>
<dt>Returns:</dt>
<dd>a new ArrayList when list is null, otherwise list</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#lazyUnsafeInit(T,java.util.function.Supplier)"><code>to initialize other object types</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFilesInFolder(java.nio.file.Path,java.lang.String)">
<h3>getFilesInFolder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&gt;</span>&nbsp;<span class="element-name">getFilesInFolder</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;folderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;contains)</span>
                            throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Returns a list of files from a folder where the name contains the given string, sorted alphabetically</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>folderPath</code> - the folder where to looks for files, excluding subfolders.</dd>
<dd><code>contains</code> - a string that the name must contain, can be empty or null to accept any</dd>
<dt>Returns:</dt>
<dd>a list of files, can be empty</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRandomString(int)">
<h3>getRandomString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRandomString</span><wbr><span class="parameters">(int&nbsp;length)</span></div>
<div class="block">Returns a random string of the given length with characters in the range "A".."Z", "a".."z", "0".."9"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>length</code> - the exact length of the string</dd>
<dt>Returns:</dt>
<dd>a random string</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRandomText(int)">
<h3>getRandomText</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRandomText</span><wbr><span class="parameters">(int&nbsp;minlen)</span></div>
<div class="block">Returns a random string (currently an hex random number)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minlen</code> - minimum length of the string. The maximum length is random.</dd>
<dt>Returns:</dt>
<dd>a random string</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getRandomString(int)"><code>getRandomString(int)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="joinIfNotEmpty(java.lang.String,java.lang.String...)">
<h3>joinIfNotEmpty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">joinIfNotEmpty</span><wbr><span class="parameters">(@NotNull
 @NotNull <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;separator,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;toJoin)</span></div>
<div class="block">Joins a number of strings, adding a separator only when the strings are not empty.
 In other words, null or empty strings are skipped without adding a separator.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>separator</code> - </dd>
<dd><code>toJoin</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTimestampAsRelative(java.time.ZonedDateTime,java.util.Locale,java.lang.Integer)">
<h3>getTimestampAsRelative</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTimestampAsRelative</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/ZonedDateTime.html" title="class or interface in java.time" class="external-link">ZonedDateTime</a>&nbsp;timestamp,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;maxHours)</span></div>
<div class="block">Given a date in the past, returns a string like "12 minutes ago", "2 hours ago", "today at 12:51", "yesterday at 5:32"...
 For dates before yesterday, the full RFC_1123 format is used, as 'Tue, 3 Jun 2008 11:05:30 GMT'.
 No "x days ago" format is currently provided.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timestamp</code> - </dd>
<dd><code>locale</code> - </dd>
<dd><code>maxHours</code> - the max value of x for using the "x hours ago" format after which the "today at hh:mm" format is used
                        The default is 3 when null. There is no maximum value, in order to have a "76 hours ago" result if needed.</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="stringToDouble(java.lang.String,java.util.Locale)">
<h3>stringToDouble</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">double</span>&nbsp;<span class="element-name">stringToDouble</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</span>
                      throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/text/ParseException.html" title="class or interface in java.text" class="external-link">ParseException</a></span></div>
<div class="block">Parse a string as a double, using the correct decimal separator (if any).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>value</code> - a number that may have a decimal part</dd>
<dd><code>locale</code> - </dd>
<dt>Returns:</dt>
<dd>a double</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/text/ParseException.html" title="class or interface in java.text" class="external-link">ParseException</a></code> - if the string is not a valid double in the locale specified</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addIfNotNull(java.util.List,T)">
<h3 id="addIfNotNull(java.util.List,java.lang.Object)">addIfNotNull</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addIfNotNull</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;&nbsp;list,
 T&nbsp;element)</span></div>
<div class="block">Add an element to the list only if the element is not null</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - </dd>
<dt>Parameters:</dt>
<dd><code>list</code> - </dd>
<dd><code>element</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addIfMissing(java.util.List,T)">
<h3 id="addIfMissing(java.util.List,java.lang.Object)">addIfMissing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addIfMissing</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;&nbsp;list,
 T&nbsp;element)</span></div>
<div class="block">Add an element to the list only if not there already</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - </dd>
<dt>Parameters:</dt>
<dd><code>list</code> - </dd>
<dd><code>element</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getEmptySortedSet(java.util.List)">
<h3>getEmptySortedSet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getEmptySortedSet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;sortOrder)</span></div>
<div class="block">Create a new TreeSet that sorts values according to the order specified in the parameter.
 Values that are missing from sortOrder are sorted alphabetically</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sortOrder</code> - </dd>
<dt>Returns:</dt>
<dd>an empty sorted set that can receive a subset of the values in the sortOrder and keep them sorted the same way</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDays(java.util.Date,java.util.Date)">
<h3>getDays</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/LocalDate.html" title="class or interface in java.time" class="external-link">LocalDate</a>&gt;</span>&nbsp;<span class="element-name">getDays</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;fromInclusive,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;toInclusive)</span></div>
<div class="block">Returns a list of days between two dates included. Days are at midnight in the system timezone.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fromInclusive</code> - start day, the time is ignored</dd>
<dd><code>toInclusive</code> - end day, the time is ignored</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDays(java.time.LocalDate,java.time.LocalDate)">
<h3>getDays</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/LocalDate.html" title="class or interface in java.time" class="external-link">LocalDate</a>&gt;</span>&nbsp;<span class="element-name">getDays</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/LocalDate.html" title="class or interface in java.time" class="external-link">LocalDate</a>&nbsp;fromInclusive,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/LocalDate.html" title="class or interface in java.time" class="external-link">LocalDate</a>&nbsp;toInclusive)</span></div>
<div class="block">Returns a list of days between two dates included.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fromInclusive</code> - start day, the time is ignored</dd>
<dd><code>toInclusive</code> - end day, the time is ignored</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDateFromDateTimeIsoString(java.lang.String,java.lang.String,java.util.TimeZone)">
<h3>getDateFromDateTimeIsoString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">getDateFromDateTimeIsoString</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;isoDateString,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;isoTimeString,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</span></div>
<div class="block">Given a ISO date, a ISO time and a timezone, return the Date.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>isoDateString</code> - like '2011-12-03'</dd>
<dd><code>isoTimeString</code> - like '10:15' or '10:15:30' (optional, can be null or empty)</dd>
<dd><code>timezone</code> - the timezone where the date/time strings belong (optional, can be null)</dd>
<dt>Returns:</dt>
<dd>a Date representing the datetime in the timezone, or null when invalid</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRfcDateTimeStringForTimezone(java.util.Date,java.util.TimeZone,java.util.Locale)">
<h3>getRfcDateTimeStringForTimezone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRfcDateTimeStringForTimezone</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</span></div>
<div class="block">Returns a string for date and time in the specified timezone and locale</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>date</code> - the date to format</dd>
<dd><code>timezone</code> - the timezone in which the date is to be considered</dd>
<dd><code>locale</code> - the locale to use for formatting</dd>
<dt>Returns:</dt>
<dd>The RFC-1123 formatted date, such as 'Tue, 3 Jun 2008 11:05:30 GMT'.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIsoDateStringForTimezone(java.util.Date,java.util.TimeZone)">
<h3>getIsoDateStringForTimezone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getIsoDateStringForTimezone</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</span></div>
<div class="block">Convert a date in the timezone to a ISO string, like '2011-12-03'</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>date</code> - </dd>
<dd><code>timezone</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIsoTimeStringForTimezone(java.util.Date,java.util.TimeZone)">
<h3>getIsoTimeStringForTimezone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getIsoTimeStringForTimezone</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;time,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</span></div>
<div class="block">Convert a time in the timezone to a ISO string, like '10:15' or '10:15:30'</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timezone</code> - </dd>
<dd><code>date</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getIsoDateTimeStringForTimezone(java.util.Date,java.util.TimeZone)">
<h3>getIsoDateTimeStringForTimezone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getIsoDateTimeStringForTimezone</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;dateTime,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</span></div>
<div class="block">Convert a datetime in the timezone to a ISO string, like '2011-12-03T10:15:30'</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timezone</code> - </dd>
<dd><code>date</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeJsonObject()">
<h3>makeJsonObject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">makeJsonObject</span>()</div>
<div class="block">Create a single empty "json" object for use in other methods.
 Json objects are actually maps that get converted by Spring on return.</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJsonObject(java.util.Map,java.lang.String)">
<h3>getJsonObject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getJsonObject</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;jsonSource,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;objectPath)</span></div>
<div class="block">Given a json stored as a map, returns the json at the specified key.
 Non need for this method if the objectPath is a simple key: just use the Map get(key) method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jsonSource</code> - </dd>
<dd><code>objectPath</code> - the name of a (nested) json property holding an object</dd>
<dt>Returns:</dt>
<dd>the object or null if it does not exist</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#makeJsonObject(java.util.Map,java.lang.String)"><code>makeJsonObject(parentObject, path)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJsonObject(java.util.Map,java.lang.String,int)">
<h3>getJsonObject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getJsonObject</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;jsonSource,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;listPath,
 int&nbsp;listIndex)</span></div>
<div class="block">Given a json stored as a map, returns the json at the specified list index</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jsonSource</code> - </dd>
<dd><code>listPath</code> - the path of the json property holing the list</dd>
<dd><code>listIndex</code> - the list index</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJsonArray(java.util.Map,java.lang.String)">
<h3>getJsonArray</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">getJsonArray</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;jsonSource,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;objectPath)</span></div>
<div class="block">Given a json stored as a map, returns the json array at the specified key
 Non need for this method if the objectPath is a simple key: just use the Map get(key) method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jsonSource</code> - </dd>
<dd><code>objectPath</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJsonAttribute(java.util.Map,java.lang.String)">
<h3>getJsonAttribute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getJsonAttribute</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;jsonSource,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;objectPath)</span></div>
<div class="block">Given a json stored as a map, returns the value at the specified key, with optional nesting.
 No need for this method if the objectPath is a simple key: just use the Map get(key) method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jsonSource</code> - </dd>
<dd><code>objectPath</code> - the path of the attribute, using dot notation and arrays. E.g. "order.amount[2].currency"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeJsonObject(java.util.Map,java.lang.String)">
<h3>makeJsonObject</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">makeJsonObject</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;parentObject,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">Creates an empty json object at the given path, if missing.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>parentObject</code> - the json object containing the new object</dd>
<dd><code>path</code> - where the object should be created, using dot notation and arrays. E.g. "order.amount[2].currency".
 Any missing array cells are also created with an empty object as needed.</dd>
<dt>Returns:</dt>
<dd>the created object</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJsonAttribute(java.util.Map,java.lang.String,java.lang.Object)">
<h3>setJsonAttribute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJsonAttribute</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;jsonObject,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">Sets a json property at the given path. Non need for this method if the path is a simple key: just use the Map put(key, value) method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jsonObject</code> - the json object that should contain the property</dd>
<dd><code>path</code> - path of the property using dot notation and arrays. E.g. "order.amount[2].currency".
 Any missing array cells are also created with an empty object as needed.</dd>
<dd><code>value</code> - the value to store, can also be a "json object"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTimezones()">
<h3>getTimezones</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getTimezones</span>()</div>
<div class="block">Returns a list of user-friendly timezones like "Europe/Rome"</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTimezoneOffsets(java.lang.String)">
<h3>getTimezoneOffsets</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getTimezoneOffsets</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</span></div>
<div class="block">Get a list of GMT/UTC time offsets from UTC-12:00 to UTC+14:00</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>prefix</code> - use either "GMT" or "UTC"</dd>
<dt>Returns:</dt>
<dd>from "GMT-12:00" to "GMT+14:00"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isEmailValid(java.lang.String)">
<h3>isEmailValid</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isEmailValid</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;email)</span></div>
<div class="block">Simple email address syntax check: the format should be X@Y.Y
 where X does not contain @ and Y does not contain @, nor . at the edges.
 Also no spaces anywhere.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>email</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="mapToString(java.util.Map)">
<h3>mapToString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">mapToString</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;stringMap)</span></div>
<div class="block">Convert a map of strings to a commaspace-separated string of name=value pairs</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stringMap</code> - </dd>
<dt>Returns:</dt>
<dd>a name-value string like "n1=v1, n2=v2"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRandom(int,int)">
<h3>getRandom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getRandom</span><wbr><span class="parameters">(int&nbsp;minIncluded,
 int&nbsp;maxIncluded)</span></div>
<div class="block">Returns a random integer number</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minIncluded</code> - minimum value, included</dd>
<dd><code>maxIncluded</code> - maximum value, included</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="findGenericClass(java.lang.Object)">
<h3>findGenericClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;</span>&nbsp;<span class="element-name">findGenericClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;specificClassInstance)</span></div>
<div class="block">Given the instance of a "specific" class created specifying a single type T while extending a generic class,
 retrieve the class of the type T.
 It also works when looking for the generic super-super class at any hierarchy level.
 Example:
 the generic class is public abstract class Shape<T extends Color> {...}
 the specific class is public class Circle extends Shape<Red>
 the instance is new Circle()
 the returned value is Red.class</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>specificClassInstance</code> - instance of the specific class, usually "this" when called from inside either the specific or the generic abstract class.</dd>
<dt>Returns:</dt>
<dd>the class T used to make the generic specific, or null if there is no generic superclass in the hierarchy</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="joinFiles(java.nio.file.Path,java.lang.String,java.io.File,java.lang.Integer,java.lang.Boolean)">
<h3>joinFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">joinFiles</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;sourceFolder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceFilePattern,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;outputFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;depth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;deleteSource)</span>
               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Merges all files matched by a pattern, in no particular order.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceFolder</code> - root folder where files are to be found</dd>
<dd><code>sourceFilePattern</code> - regex pattern to match files, e.g. ".*.js"</dd>
<dd><code>outputFile</code> - file that will contain the joined files</dd>
<dd><code>depth</code> - (optional) max depth of folders: null or 1 for no recursion</dd>
<dd><code>deleteSource</code> - (optional) Boolean.TRUE to attempt deletion of source files</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeTempFolder()">
<h3>makeTempFolder</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">makeTempFolder</span>()
                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Creates a folder in the system temp folder. The name is prefixed with "yada".</div>
<dl class="notes">
<dt>Returns:</dt>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="relativize(java.io.File,java.io.File)">
<h3>relativize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">relativize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;ancestorFolder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;descendantFolder)</span></div>
<div class="block">Finds the path between two files or folders using forward (unix) slashes as a separator</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ancestorFolder</code> - </dd>
<dd><code>descendantFolder</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="relativize(java.nio.file.Path,java.nio.file.Path)">
<h3>relativize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">relativize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;ancestorPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;descendantPath)</span></div>
<div class="block">Finds the path between two files or folders using forward (unix) slashes as a separator.
 It works even if the two arguments point to places in different trees.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ancestorPath</code> - the root path e.g. "/a/b"</dd>
<dd><code>descendantPath</code> - the final path e.g. "/a/b/c/the.gif"</dd>
<dt>Returns:</dt>
<dd>the relative path from ancestorPath to descendantPath e.g. "c/the.gif"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="splitHtml(java.lang.String,int)">
<h3>splitHtml</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">splitHtml</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;htmlToSplit,
 int&nbsp;splitPos)</span></div>
<div class="block">Split an HTML string in two parts, not breaking words, handling closing and reopening of html tags.
 Useful when showing some part of a text and the whole of it after a user clicks.
 For example, the string "&lt;p&gt;Some text here&lt;/p&gt; becomes ["&lt;p&gt;Some text&lt;/p&gt;","&lt;p&gt;here&lt;/p&gt;"].
 The HTML is not splitted exactly at splitPos if there's a word there, a tag, or if the paragraph ends in the next 20 characters:
 in such cases the split position is increased accordingly.
 Note: does not work in any possible scenario. For example &lt;ul&gt;&lt;li&gt; is not split properly because it creates two list entries
 if the split point is inside the li. Tag attributes are not currently handled properly.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>htmlToSplit</code> - The html text to split, must be well-formed (all opened tags must be closed properly)</dd>
<dd><code>splitPos</code> - the minimum position, in number of characters including tags, where to split</dd>
<dt>Returns:</dt>
<dd>an array of two self-contained html parts, where each opened tag is correctly closed. The second part could be null.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#splitAtWord(java.lang.String,int)"><code>splitAtWord(String, int)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findAvailableFilename(java.lang.String,java.lang.String,java.lang.String,java.util.Set)">
<h3>findAvailableFilename</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">findAvailableFilename</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extensionNoDot,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;counterSeparator,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;usedNames)</span>
                             throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Ensure that the given filename has not been already used, by adding a counter.
 For example, if baseName is "dog" and usedNames is {"dog.jpg", "dog_1.jpg", "dog_2.jpg"}, the
 result will be "dog_3.jpg"
 The usedNames array doesn't have to contain identical or sequential baseNames: {"dog.jpg", "cat.jpg", "dog_2.jpg"}
 This version does not check if a file exists on disk. For that, see <a href="#findAvailableName(java.io.File,java.lang.String,java.lang.String,java.lang.String)"><code>findAvailableName(File, String, String, String)</code></a>
 This method can be used with any strings, not necessarily filenames: just use null for the extension.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>baseName</code> - filename to add, without extension</dd>
<dd><code>extensionNoDot</code> - filename extension without dot, can be empty or null if the extension is not needed</dd>
<dd><code>counterSeparator</code> - string to separate the filename and the counter, can be empty or null</dd>
<dd><code>usedNames</code> - filenames used so far, can start empty but never null, and will be modified by adding the new name</dd>
<dt>Returns:</dt>
<dd>the original filename with extension, or a new version with a counter added</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dateValid(java.util.Date,java.lang.Integer)">
<h3>dateValid</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">dateValid</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;someDate,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;maxYears)</span></div>
<div class="block">Check if a date is not more than maxYears years from now, not in an accurate way.
 Useful to check validity of a date coming from the browser.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>someDate</code> - </dd>
<dd><code>maxYears</code> - max number of years (positive or negative) for this date to be valid. When null, defaults to 4000 years.</dd>
<dt>Returns:</dt>
<dd>false if the date is too distant from now</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getImageDimensionDumb(java.io.File)">
<h3>getImageDimensionDumb</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../raw/YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></span>&nbsp;<span class="element-name">getImageDimensionDumb</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;imageFile)</span></div>
<div class="block">Gets image dimensions for given file, ignoring orientation flag</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>imageFile</code> - image file</dd>
<dt>Returns:</dt>
<dd>dimensions of image, or YadaIntDimension.UNSET when not found</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getImageDimension(java.io.File)">
<h3>getImageDimension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../raw/YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></span>&nbsp;<span class="element-name">getImageDimension</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;imageFile)</span></div>
<div class="block">Gets the image dimensions considering the EXIF orientation flag.
 Remember to use the "-auto-orient" flag of the ImageMagick convert command.
 If the EXIF width and height information is missing, the getImageDimensionDumb() method is called instead.
 See https://www.impulseadventure.com/photo/exif-orientation.html</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>imageFile</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentStackTraceFormatted()">
<h3>getCurrentStackTraceFormatted</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCurrentStackTraceFormatted</span>()</div>
<div class="block">Returns the current stack trace as a string, formatted on separate lines</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRandomElement(java.util.List)">
<h3>getRandomElement</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">getRandomElement</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;&nbsp;list)</span></div>
<div class="block">Returns a random element from the list</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>list</code> - </dd>
<dt>Returns:</dt>
<dd>a random element from the list, or null if the list is empty</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="formatTimeInterval(long,java.util.concurrent.TimeUnit)">
<h3>formatTimeInterval</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">formatTimeInterval</span><wbr><span class="parameters">(long&nbsp;amount,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/concurrent/TimeUnit.html" title="class or interface in java.util.concurrent" class="external-link">TimeUnit</a>&nbsp;timeUnit)</span></div>
<div class="block">Convert from an amount of time to a string in the format xxd:hh:mm:ss</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>amount</code> - interval that needs to be formatted</dd>
<dd><code>timeUnit</code> - the unit of the interval</dd>
<dt>Returns:</dt>
<dd>a formatted string representing the input interval</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="autowire(java.lang.Object)">
<h3>autowire</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">autowire</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;instance)</span></div>
<div class="block">Perform autowiring of an instance that doesn't come from the Spring context, e.g. a JPA @Entity.
 Post processing (@PostConstruct etc) is also performed but initialization is not.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>instance</code> - to autowire</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><code>AutowireCapableBeanFactory.autowireBean(Object)</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="autowireAndInitialize(java.lang.Object)">
<h3>autowireAndInitialize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">autowireAndInitialize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;instance)</span></div>
<div class="block">Perform autowiring of an instance that doesn't come from the Spring context, e.g. a JPA @Entity or normal java instance made with new.
 Post processing (@PostConstruct etc) and initialization are also performed.
 Beans from the WebApplicationContext like @Controller are not injected because they don't belong to the root Application Context used here.
 If you need to do that, use <a href="YadaWebUtil.html#autowireAndInitialize(java.lang.Object)"><code>YadaWebUtil.autowireAndInitialize(Object)</code></a></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>instance</code> - to autowire</dd>
<dt>Returns:</dt>
<dd>the autowired/initialized bean instance, either the original or a wrapped one</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stripCounterFromFilename(java.lang.String,java.lang.String)">
<h3>stripCounterFromFilename</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">stripCounterFromFilename</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;counterSeparator)</span></div>
<div class="block">Remove a counter that has been added by <a href="#findAvailableName(java.io.File,java.lang.String,java.lang.String,java.lang.String)"><code>findAvailableName(java.io.File, java.lang.String, java.lang.String, java.lang.String)</code></a></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - </dd>
<dd><code>counterSeparator</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="findAvailableNameHighest(java.io.File,java.lang.String,java.lang.String,java.lang.String)">
<h3>findAvailableNameHighest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">findAvailableNameHighest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFolder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extensionNoDot,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;counterSeparator)</span></div>
<div class="block">Returns a file that doesn't already exist in the specified folder
 with the specified leading characters (baseName) and optional extension.
 The file will always have a number at the end that is higher than any other numbers on
 similar files in the folder.
 Can be used to find both files and folders.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetFolder</code> - </dd>
<dd><code>baseName</code> - </dd>
<dd><code>extensionNoDot</code> - </dd>
<dd><code>counterSeparator</code> - </dd>
<dt>Returns:</dt>
<dd>a file with a name like baseName_0001 or baseName_0003.txt</dd>
<dt>Throws:</dt>
<dd><code><a href="../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - when targetFolder is not a folder</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findAvailableName(java.io.File,java.lang.String,java.lang.String,java.lang.String)">
<h3>findAvailableName</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">findAvailableName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFolder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;baseName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extensionNoDot,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;counterSeparator)</span>
                              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Creates an empty file that doesn't already exist in the specified folder
 with the specified leading characters (baseName).
 A counter may be appended to make the file unique.
 This operation is thread safe.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetFolder</code> - the folder where the file has to be placed</dd>
<dd><code>baseName</code> - the leading characters for the file, like "product"</dd>
<dd><code>counterSeparator</code> - the separator to be used before appending the number, e.g. "_"</dd>
<dd><code>extension</code> - the extension without a dot, like "jpg"</dd>
<dt>Returns:</dt>
<dd>a new file in that folder, with a name like "product_2.jpg" or "product.jpg"</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findAvailableName(java.io.File,java.lang.String)">
<h3>findAvailableName</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">findAvailableName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;counterSeparator)</span>
                              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Creates a file with a unique filename by appending a number after the specified separator if needed.
 If the targetFile exists already, a new file is created with a proper counter at the end. The counter may be stripped
 altogether (if the original file had a counter and no file without counter exists) or added or incremented.
 The new counter might not be higher than the original one, nor sequential. It depends on what's already on
 the filesystem.
 This operation is thread safe.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetFile</code> - the file that we want to create.</dd>
<dd><code>counterSeparator</code> - (optional) when null, "_" is used.</dd>
<dt>Returns:</dt>
<dd>a File that doesn't already exist</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="prefetchLocalizedStrings(targetClass,java.lang.Class,java.lang.String...)">
<h3 id="prefetchLocalizedStrings(java.lang.Object,java.lang.Class,java.lang.String[])">prefetchLocalizedStrings</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;targetClass&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">prefetchLocalizedStrings</span><wbr><span class="parameters">(targetClass&nbsp;fetchedEntity,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;targetClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;attributes)</span></div>
<div class="block">Force initialization of localized strings implemented with Map&lt;Locale, String&gt;.
 It must be called in a transaction.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fetchedEntity</code> - object fetched from database that may contain localized strings</dd>
<dd><code>targetClass</code> - type of fetchedEntity element</dd>
<dd><code>attributes</code> - the localized string attributes to prefetch (optional). If missing, all attributes of the right type are prefetched.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="prefetchLocalizedStringsRecursive(targetClass,java.lang.Class,java.lang.String...)">
<h3 id="prefetchLocalizedStringsRecursive(java.lang.Object,java.lang.Class,java.lang.String[])">prefetchLocalizedStringsRecursive</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;targetClass&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">prefetchLocalizedStringsRecursive</span><wbr><span class="parameters">(targetClass&nbsp;fetchedEntity,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;targetClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;attributes)</span></div>
<div class="block">Force initialization of localized strings implemented with Map&lt;Locale, String&gt;.
 It must be called in a transaction.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fetchedEntity</code> - object fetched from database that may contain localized strings</dd>
<dd><code>targetClass</code> - type of fetchedEntities elements</dd>
<dd><code>attributes</code> - the localized string attributes to prefetch (optional). If missing, all attributes of the right type are prefetched.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="prefetchLocalizedStringListRecursive(java.util.List,java.lang.Class,java.lang.String...)">
<h3>prefetchLocalizedStringListRecursive</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;entityClass&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">prefetchLocalizedStringListRecursive</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;entityClass&gt;&nbsp;entities,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;entityClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;attributes)</span></div>
<div class="block">Force initialization of localized strings implemented with Map&lt;Locale, String&gt;.
 It must be called in a transaction.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>entities</code> - objects fetched from database that may contain localized strings</dd>
<dd><code>entityClass</code> - type of fetchedEntities elements</dd>
<dd><code>attributes</code> - the localized string attributes to prefetch (optional). If missing, all attributes of the right type are prefetched.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="prefetchLocalizedStringList(java.util.Collection,java.lang.Class,java.lang.String...)">
<h3>prefetchLocalizedStringList</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;entityClass&gt;</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">prefetchLocalizedStringList</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;entityClass&gt;&nbsp;entities,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;entityClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;attributes)</span></div>
<div class="block">Force initialization of localized strings implemented with Map&lt;Locale, String&gt;.
 It must be called in a transaction.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>entities</code> - objects fetched from database that may contain localized strings</dd>
<dd><code>entityClass</code> - type of fetchedEntities elements</dd>
<dd><code>attributes</code> - the localized string attributes to prefetch (optional). If missing, all attributes of the right type are prefetched.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getObjectToString(java.lang.Object)">
<h3>getObjectToString</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getObjectToString</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;object)</span></div>
<div class="block">Returns a string representation of the object as Object.toString() does, even if toString() has been overridden</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>object</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLocalValue(java.util.Map)">
<h3>getLocalValue</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getLocalValue</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;LocalizedValueMap)</span></div>
<div class="block">Returns the localized value from a map of Locale -&gt; String.
 Used in entities with localized string attributes.
 If a default locale has been configured with <code>&lt;locale default='true'&gt;</code>, then that locale is attempted when
 there is no value (null or "") for the needed locale (and they differ)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>LocalizedValueMap</code> - </dd>
<dt>Returns:</dt>
<dd>the localized value, or the empty string if no value has been defined and no default locale has been configured</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLocalValue(java.util.Map,java.util.Locale)">
<h3>getLocalValue</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getLocalValue</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;localizedValueMap,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</span></div>
<div class="block">Returns the localized value from a map of Locale -&gt; String.
 Used in entities with localized string attributes.
 If a default locale has been configured with <code>&lt;locale default='true'&gt;</code>, then that locale is attempted when
 there is no value for the needed locale (and they differ)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>localizedValueMap</code> - </dd>
<dd><code>locale</code> - the needed locale for the value, can be null for the current request locale</dd>
<dt>Returns:</dt>
<dd>the localized value, or the empty string if no value has been defined and no default locale has been configured</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteFileSilently(java.nio.file.Path)">
<h3>deleteFileSilently</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">deleteFileSilently</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;file)</span></div>
<div class="block">Deletes a file without reporting any errors.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the file. It could also be an empty foder. Folders containing files are not deleted.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="closeSilently(java.io.Closeable)">
<h3>closeSilently</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">closeSilently</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Closeable.html" title="class or interface in java.io" class="external-link">Closeable</a>&nbsp;closeable)</span></div>
<div class="block">Close a closeable ignoring exceptions and null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>closeable</code> - the object to close(), can be null</dd>
<dt>Returns:</dt>
<dd>true if closed cleanly (or null), false in case of exception</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sleepRandom(long,long)">
<h3>sleepRandom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">sleepRandom</span><wbr><span class="parameters">(long&nbsp;minMilliseconds,
 long&nbsp;maxMilliseconds)</span></div>
</section>
</li>
<li>
<section class="detail" id="sleep(long)">
<h3>sleep</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">sleep</span><wbr><span class="parameters">(long&nbsp;milliseconds)</span></div>
</section>
</li>
<li>
<section class="detail" id="md5Hash(java.lang.String)">
<h3>md5Hash</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">md5Hash</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clear)</span>
               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/security/NoSuchAlgorithmException.html" title="class or interface in java.security" class="external-link">NoSuchAlgorithmException</a></span></div>
<div class="block">Create a MD5 hash of a string (from http://snippets.dzone.com/posts/show/3686)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>clear</code> - the source text</dd>
<dt>Returns:</dt>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/security/NoSuchAlgorithmException.html" title="class or interface in java.security" class="external-link">NoSuchAlgorithmException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="copyStream(java.io.InputStream,java.io.OutputStream,java.lang.Integer,java.lang.Long)">
<h3>copyStream</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">copyStream</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/InputStream.html" title="class or interface in java.io" class="external-link">InputStream</a>&nbsp;inputStream,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/OutputStream.html" title="class or interface in java.io" class="external-link">OutputStream</a>&nbsp;outputStream,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;bufferSize,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;sizeLimit)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copies an inputStream to an outputStream.
 This method blocks until input data is available, end of file is detected, or an exception is thrown.
 Streams are not closed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputStream</code> - </dd>
<dd><code>outputStream</code> - </dd>
<dd><code>bufferSize</code> - the size in bytes of the temporary buffer to use on the copy loop; null for the default of 4096 bytes.
 Use a small buffer (256) when data is over the internet to prevent timeouts somewhere. Use a big buffer for in-memory or disk operations.</dd>
<dd><code>sizeLimit</code> - the maximum number of bytes to read (inclusive)</dd>
<dt>Returns:</dt>
<dd>the number of bytes read, or -1 if the sizeLimit has been exceeded.</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Files.html#copy(java.nio.file.Path,java.nio.file.Path,java.nio.file.CopyOption...)" title="class or interface in java.nio.file" class="external-link"><code>Files.copy(Path, Path, java.nio.file.CopyOption...)</code></a></li>
<li><code>IOUtils.copy(InputStream, OutputStream)</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFieldNoTraversing(java.lang.Class,java.lang.String)">
<h3>getFieldNoTraversing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/reflect/Field.html" title="class or interface in java.lang.reflect" class="external-link">Field</a></span>&nbsp;<span class="element-name">getFieldNoTraversing</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;rootClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName)</span></div>
<div class="block">Get the Field of a given class, even from a superclass but not "nested" in a path</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rootClass</code> - </dd>
<dd><code>attributeName</code> - </dd>
<dt>Returns:</dt>
<dd>the Field found or null</dd>
<dt>Throws:</dt>
<dd><code><a href="../exceptions/YadaInvalidValueException.html" title="class in net.yadaframework.exceptions">YadaInvalidValueException</a></code> - if attributeName is a path (with a dot in it)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getType(java.lang.Class,java.lang.String)">
<h3>getType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">getType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;rootClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributePath)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/NoSuchFieldException.html" title="class or interface in java.lang" class="external-link">NoSuchFieldException</a>,
<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/SecurityException.html" title="class or interface in java.lang" class="external-link">SecurityException</a></span></div>
<div class="block">Reflection to get the type of a given field, even nested or in a superclass.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rootClass</code> - </dd>
<dd><code>attributePath</code> - field name like "surname" or even a path like "friend.name"</dd>
<dt>Returns:</dt>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/NoSuchFieldException.html" title="class or interface in java.lang" class="external-link">NoSuchFieldException</a></code> - if the field is not found in the class hierarchy</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/SecurityException.html" title="class or interface in java.lang" class="external-link">SecurityException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMessage(java.lang.String,java.lang.Object...)">
<h3>getMessage</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getMessage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;key,
 @Nullable
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;params)</span></div>
<div class="block">Ritorna un messaggio localizzato da un contesto fuori da Spring (ad esempio in un enum o un Entity)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>key</code> - </dd>
<dd><code>params</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNewInstanceSamePackage(java.lang.Class,java.lang.String)">
<h3>getNewInstanceSamePackage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getNewInstanceSamePackage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;anyClassInPackage,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;simpleClassName)</span></div>
<div class="block">Create an instance of a class that belongs to the same package of some given class</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>anyClassInPackage</code> - a class that is in the same package of the one to instantiate (it could be the one to instantiate)</dd>
<dd><code>simpleClassName</code> - the simple name of the class to instantiate, like "UserProfile"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClassesInPackage(java.lang.Package)">
<h3>getClassesInPackage</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&gt;</span>&nbsp;<span class="element-name">getClassesInPackage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Package.html" title="class or interface in java.lang" class="external-link">Package</a>&nbsp;thePackage)</span></div>
<div class="block">Return all the classes of a given package.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>thePackage</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBean(java.lang.Class,java.lang.Object...)">
<h3>getBean</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">T</span>&nbsp;<span class="element-name">getBean</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;beanClass,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;args)</span></div>
<div class="block">Get any bean defined in the Spring ApplicationContext</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>beanClass</code> - </dd>
<dd><code>args</code> - constructor arguments, can be null or not present</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBean(java.lang.String,java.lang.Object...)">
<h3>getBean</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getBean</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nameInApplicationContext,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;args)</span></div>
<div class="block">Get any bean defined in the Spring ApplicationContext</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nameInApplicationContext</code> - the Class.getSimpleName() starting lowercase, e.g. "processController"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBean(java.lang.String)">
<h3>getBean</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getBean</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nameInApplicationContext)</span></div>
<div class="block">Get any bean defined in the Spring ApplicationContext</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nameInApplicationContext</code> - the Class.getSimpleName() starting lowercase, e.g. "processController"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="minutesAgo(int)">
<h3>minutesAgo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">minutesAgo</span><wbr><span class="parameters">(int&nbsp;minuti)</span></div>
<div class="block">Ritorna la data nel passato per il numero di minuti indicati</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>days</code> - numero di minuti fa</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="daysAgo(int)">
<h3>daysAgo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">daysAgo</span><wbr><span class="parameters">(int&nbsp;days)</span></div>
<div class="block">Ritorna la data nel passato per il numero di giorni indicati</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>days</code> - numero di giorni fa</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteAll(java.io.File,java.lang.String)">
<h3>deleteAll</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">deleteAll</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;folder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</span></div>
<div class="block">Delete all files in a folder that have the specified prefix</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>folder</code> - </dd>
<dd><code>prefix</code> - </dd>
<dt>Returns:</dt>
<dd>true if all files have been deleted, false if at least one file has not been deleted</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteSilently(java.io.File)">
<h3>deleteSilently</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">deleteSilently</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
<div class="block">Delete a file ignoring errors.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>file</code> - the file to delete, can be null</dd>
<dt>Returns:</dt>
<dd>true if the file has been deleted, false if the file didn't exist or was null or in case of exception</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteIfEmpty(java.nio.file.Path)">
<h3>deleteIfEmpty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">deleteIfEmpty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;folder)</span></div>
<div class="block">Deleted a folder only when empty</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>folder</code> - </dd>
<dt>Returns:</dt>
<dd>true if deleted, false otherwise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanupFolder(java.nio.file.Path,java.lang.String)">
<h3>cleanupFolder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">cleanupFolder</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;folder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix)</span></div>
<div class="block">Removes files from a folder starting with the prefix (can be an empty string)
 The folder itself is not removed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>folder</code> - </dd>
<dd><code>prefix</code> - the initial part of the filename or "" for any file
 return the number of deleted files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanupFolder(java.nio.file.Path,java.lang.String,java.util.Date)">
<h3>cleanupFolder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">cleanupFolder</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;folder,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;prefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;olderThan)</span></div>
<div class="block">Removes files from a folder starting with the prefix (can be an empty string) and older than the given date.
 The folder itself is not removed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>folder</code> - </dd>
<dd><code>prefix</code> - </dd>
<dd><code>olderThan</code> - </dd>
<dt>Returns:</dt>
<dd>the number of deleted files</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFileNoPath(java.lang.String)">
<h3>getFileNoPath</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFileNoPath</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fileWithPath)</span></div>
<div class="block">Returns the file name given the file path</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileWithPath</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="splitFileNameAndExtension(java.lang.String)">
<h3>splitFileNameAndExtension</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">splitFileNameAndExtension</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Splits a filename in the prefix and the extension parts. If there is no extension, the second array cell is the empty string</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - </dd>
<dt>Returns:</dt>
<dd>an array with [ filename without extension, extension without dot]</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFileExtension(java.lang.String)">
<h3>getFileExtension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFileExtension</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Da un nome tipo abcd.JPG ritorna "jpg"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filename</code> - </dd>
<dt>Returns:</dt>
<dd>l'estensione, oppure null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFileExtension(java.io.File)">
<h3>getFileExtension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFileExtension</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;file)</span></div>
</section>
</li>
<li>
<section class="detail" id="shellExec(java.lang.String,java.util.List,java.util.Map,java.io.ByteArrayOutputStream)">
<h3>shellExec</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">shellExec</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<dl class="notes">
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="shellExec(java.lang.String,java.util.List,java.util.Map,java.io.ByteArrayOutputStream,int)">
<h3>shellExec</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">shellExec</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream,
 int&nbsp;timeoutSeconds)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Run an external shell command.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>command</code> - the shell command to run, without parameters</dd>
<dd><code>args</code> - optional command line parameters. Can be null for no parameters. Each parameter can have spaces without delimiting quotes.</dd>
<dd><code>timeoutSeconds</code> - timeout in seconds after which ExecuteException is thrown. Use -1 for default timeout of 60 seconds and 0 for infinite timeout</dd>
<dd><code>optional</code> - substitutionMap key-value of placeholders to replace in the parameters. A placeholder is like ${key}, a substitution
 pair is like "key"--&gt;"value". If the value is a collection, arguments are unrolled so key--&gt;collection will result in key0=val0 key1=val1...</dd>
<dd><code>optional</code> - outputStream ByteArrayOutputStream that will contain the command output (out + err)</dd>
<dt>Returns:</dt>
<dd>the command exit value (maybe not)</dd>
<dt>Throws:</dt>
<dd><code>org.apache.commons.exec.ExecuteException</code> - when the exit value is 1 or the timeout is triggered</dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="shellExec(java.lang.String,java.util.List,java.io.ByteArrayOutputStream)">
<h3>shellExec</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">shellExec</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Run an external shell command without keyword substitution in the parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>command</code> - the shell command to run, without parameters</dd>
<dd><code>args</code> - command line literal parameters. Can be null for no parameters. Each parameter can have spaces without delimiting quotes.</dd>
<dd><code>optional</code> - outputStream ByteArrayOutputStream that will contain the command output (out + err)</dd>
<dt>Returns:</dt>
<dd>the command exit value</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="shellExec(java.lang.String,java.util.Map)">
<h3>shellExec</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">shellExec</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;shellCommandKey,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Run an external shell command that has been defined in the configuration file.
 The command must be as in the following example:
 <pre>
        &lt;imageConvert timeoutseconds="20"&gt;
                &lt;executable windows="true"&gt;magick&lt;/executable&gt;
                &lt;executable mac="true" linux="true"&gt;/usr/local/bin/magick&lt;/executable&gt;
                &lt;arg&gt;convert&lt;/arg&gt;
                &lt;arg&gt;${FILENAMEIN}&lt;/arg&gt;
                &lt;arg&gt;${FILENAMEOUT}&lt;/arg&gt;
        &lt;/imageConvert&gt;
 </pre>
 Be aware that args can not contain "Commons Configuration variables" because they clash with placeholders as defined below.
 See the yadaframework documentation for full syntax.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>shellCommandKey</code> - xpath key of the shell command, e.g. "config/shell/cropImage"</dd>
<dd><code>substitutionMap</code> - optional key-value of placeholders to replace in the parameters. A placeholder is like ${key}, a substitution
 pair is like "key"--&gt;"value". If the value is a collection, arguments are unrolled so key--&gt;collection will result in key0=val0 key1=val1...</dd>
<dt>Returns:</dt>
<dd>the command exit value</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="shellExec(java.lang.String,java.util.Map,java.io.ByteArrayOutputStream)">
<h3>shellExec</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">shellExec</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;shellCommandKey,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream)</span>
              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Run an external shell command that has been defined in the configuration file.
 The command must be as in the following example:
 <pre>
        &lt;imageConvert timeoutseconds="20"&gt;
                &lt;executable windows="true"&gt;magick&lt;/executable&gt;
                &lt;executable mac="true" linux="true"&gt;/usr/local/bin/magick&lt;/executable&gt;
                &lt;arg&gt;convert&lt;/arg&gt;
                &lt;arg&gt;${FILENAMEIN}&lt;/arg&gt;
                &lt;arg&gt;${FILENAMEOUT}&lt;/arg&gt;
        &lt;/imageConvert&gt;
 </pre>
 See the yadaframework documentation for full syntax.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>shellCommandKey</code> - xpath key of the shell command, e.g. "config/shell/cropImage"</dd>
<dd><code>substitutionMap</code> - optional key-value of placeholders to replace in the parameters. A placeholder is like ${key}, a substitution
 pair is like "key"--&gt;"value". If the value is a collection, arguments are unrolled so key--&gt;collection will result in key0=val0 key1=val1...</dd>
<dd><code>outputStream</code> - optional ByteArrayOutputStream that will contain the command output (out + err)</dd>
<dt>Returns:</dt>
<dd>the command exit value</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="exec(java.lang.String,java.util.List,java.util.Map,java.io.ByteArrayOutputStream)">
<h3>exec</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">exec</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Esegue un comando di shell</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>command</code> - comando</dd>
<dd><code>args</code> - lista di argomenti (ogni elemento puo' contenere spazi), puo' essere null</dd>
<dd><code>substitutionMap</code> - key-value of placeholders to replace in the command. A placeholder in the command is like ${key}, a substitution
 pair is like "key"--&gt;"value" . If the value is a collection, arguments are unrolled.</dd>
<dd><code>outputStream</code> - ByteArrayOutputStream che conterrà l'output del comando (out + err)</dd>
<dt>Returns:</dt>
<dd>the error message (will be empty for a return code &gt;0), or null if there was no error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="exec(java.lang.String,java.util.List,java.io.ByteArrayOutputStream)">
<h3>exec</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">exec</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;command,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;args,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/ByteArrayOutputStream.html" title="class or interface in java.io" class="external-link">ByteArrayOutputStream</a>&nbsp;outputStream)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Esegue un comando di shell</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>command</code> - comando</dd>
<dd><code>args</code> - lista di argomenti (ogni elemento puo' contenere spazi), puo' essere null</dd>
<dd><code>outputStream</code> - ByteArrayOutputStream che conterrà l'output del comando</dd>
<dt>Returns:</dt>
<dd>the error message (will be empty for a return code &gt;0), or null if there was no error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="exec(java.lang.String,java.util.Map)">
<h3>exec</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">exec</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;shellCommandKey,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;substitutionMap)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Esegue il comando configurato</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>shellCommandKey</code> - chiave completa xpath del comando shell da eseguire e.g. "config/shell/processTunableWhiteImage"</dd>
<dd><code>substitutionMap</code> - key-value of placeholders to replace in the command. A placeholder in the command is like ${key}, a substitution
 pair is like "key"--&gt;"value"</dd>
<dt>Returns:</dt>
<dd>true if successful</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isCodiceFiscaleValid(java.lang.String)">
<h3>isCodiceFiscaleValid</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isCodiceFiscaleValid</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;codiceFiscale)</span></div>
</section>
</li>
<li>
<section class="detail" id="getRootException(java.lang.Throwable)">
<h3>getRootException</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a></span>&nbsp;<span class="element-name">getRootException</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Throwable.html" title="class or interface in java.lang" class="external-link">Throwable</a>&nbsp;exception)</span></div>
<div class="block">Trova l'eccezione che ha causato questa, nella catena delle eccezioni</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>exception</code> - eccezione conseguente</dd>
<dt>Returns:</dt>
<dd>throwable originario, preso con getCause()</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="splitAtWord(java.lang.String,int)">
<h3>splitAtWord</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]</span>&nbsp;<span class="element-name">splitAtWord</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value,
 int&nbsp;splitPoint)</span></div>
<div class="block">Spezza una stringa in due, circa al carattere splitPoint, ma a fine parola.</div>
</section>
</li>
<li>
<section class="detail" id="abbreviate(java.lang.String,int,boolean)">
<h3>abbreviate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">abbreviate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;string,
 int&nbsp;length,
 boolean&nbsp;breakAtWord)</span></div>
<div class="block">Cuts the input string at the given length, optionally keeping the whole last word and adding some dots at the end</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>string</code> - text to shorten</dd>
<dd><code>length</code> - characters to keep starting from the beginning</dd>
<dd><code>breakAtWord</code> - true to keep the whole last word, false to eventually cut it</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="abbreviate(java.lang.String,int,boolean,java.lang.String)">
<h3>abbreviate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">abbreviate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;string,
 int&nbsp;length,
 boolean&nbsp;breakAtWord,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ellipsis)</span></div>
<div class="block">Cuts the input string at the given length, optionally keeping the whole last word and adding some characters at the end</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>string</code> - text to shorten</dd>
<dd><code>length</code> - characters to keep starting from the beginning</dd>
<dd><code>breakAtWord</code> - true to keep the whole last word, false to eventually cut it</dd>
<dd><code>ellipsis</code> - the characters to add at the end, use null for " [...]"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="copyEntity(net.yadaframework.core.CloneableFiltered)">
<h3>copyEntity</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">copyEntity</span><wbr><span class="parameters">(<a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a>&nbsp;source)</span></div>
<div class="block">Copy (clone) an object via getter/setter.
 The object must implement CloneableFiltered and optionally state what fields should be excluded and left null.
 The id is always excluded.
 All collections are recreated with the same instances unless their classes implement CloneableDeep, in which case the instances are copied with this same method.
 All objects are shallow copied unless they implement CloneableDeep, in which case they are copied with this same method.
 Map keys are never cloned.

 NOTE: the object doesn't have to be an @Entity, despite the method name
 NOTE: collection fields of an @Entity must be initialized to an empty instance in the class, or they won't be cloned
 NOTE: any @Entity in the hierarchy is cloned without id and should be explicitly persisted after cloning unless there's PERSIST propagation.
 NOTE: this method works quite well and should be trusted to copy even complex hierarchies.
 NOTE: a transaction should be active to copy entities with lazy associations

 Questo metodo crea la copia di un oggetto TRAMITE I SUOI GETTER (anche privati), facendo in modo che alcune collection/mappe vengano copiate pur restando indipendenti.
 In pratica le collection/mappe sono ricreate come istanze nuove con i medesimi oggetti di quelle originali.
 Questo permette di condividere gli oggetti tra le copie, ma di mantenere le associazioni slegate.
 Così se copio un Prodotto, mi trovo gli stessi componenti dell'originale (gli Articolo sono gli stessi) ma posso in seguito toglierli/aggiungerli
 senza influire sull'altra istanza da cui son partito a copiare.

 E' possibile specificare quali attributi non copiare grazie all'interfaccia CloneableFiltered. La id non è mai copiata.
 Gli attributi senza getter/setter non sono copiati a prescindere dal filtro.
 Per esempio se l'oggetto che si vuole copiare ha l'attributo pippo e l'attributo pluto, si può fare in modo che sia copiato solo pippo e non pluto (che rimane quindi null).

 Se una collection/mappa deve essere clonata anche nel contenuto, i suoi elementi devono implementare CloneableDeep (vedi LocalString).
 Per esempio se l'attributo pippo è una collection di oggetti Cane che non implementa ClonableDeep, nella copia verrà creata una nuova collection
 di oggetti Cane che saranno gli stessi della collection di partenza. Se invcece Cane implementa ClonableDeep, allora gli oggetti Cane contenuti
 nella copia di pippo sono essi stessi delle copie che seguono le stesse regole qui indicate.

 ATTENZIONE:
 - da verificare se gli attributi dei parent sono duplicati pure loro</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="copyEntity(net.yadaframework.core.CloneableFiltered,boolean)">
<h3>copyEntity</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">copyEntity</span><wbr><span class="parameters">(<a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a>&nbsp;source,
 boolean&nbsp;setFieldDirectly)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - </dd>
<dd><code>setFieldDirectly</code> - true to copy using fields and not getter/setter</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="copyEntity(net.yadaframework.core.CloneableFiltered,java.lang.Class)">
<h3>copyEntity</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">copyEntity</span><wbr><span class="parameters">(<a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a>&nbsp;source,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;classObject)</span></div>
<div class="block">Questo metodo crea la copia di un oggetto TRAMITE I SUOI GETTER (anche privati), facendo in modo che alcune collection/mappe vengano copiate pur restando indipendenti.
 In pratica le collection/mappe sono ricreate come istanze nuove con i medesimi oggetti di quelle originali.
 Questo permette di condividere gli oggetti tra le copie, ma di mantenere le associazioni slegate.
 Così se copio un Prodotto mi trovo gli stessi componenti dell'originale (gli Articolo sono gli stessi) ma posso in seguito toglierli/aggiungerli
 senza influire sull'altra istanza da cui son partito a copiare.

 E' possibile specificare quali attributi non copiare grazie all'interfaccia CloneableFiltered. La id non è mai copiata.
 Per esempio se l'oggetto che si vuole copiare ha l'attributo pippo e l'attributo pluto, si può fare in modo che sia copiato solo pippo e non pluto (che rimane quindi null).

 Se una collection/mappa deve essere clonata anche nel contenuto, i suoi elementi devono implementare CloneableDeep (vedi LocalString).
 Per esempio se l'attributo pippo è una collection di oggetti Cane che non implementa ClonableDeep, nella copia verrà creata una nuova collection
 di oggetti Cane che saranno gli stessi della collection di partenza. Se invcece Cane implementa ClonableDeep, allora gli oggetti Cane contenuti
 nella copia di pippo sono essi stessi delle copie che seguono le stesse regole qui indicate.

 ATTENZIONE:
 - da verificare se gli attributi dei parent sono duplicati pure loro</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - </dd>
<dd><code>classObject</code> - class to use to create the new clone when the source is inside a HibernateProxy</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="copyEntity(net.yadaframework.core.CloneableFiltered,java.lang.Class,boolean)">
<h3>copyEntity</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">copyEntity</span><wbr><span class="parameters">(<a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a>&nbsp;source,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;classObject,
 boolean&nbsp;setFieldDirectly)</span></div>
</section>
</li>
<li>
<section class="detail" id="copyEntity(net.yadaframework.core.CloneableFiltered,java.lang.Class,boolean,net.yadaframework.components.YadaAttachedFileCloneSet)">
<h3>copyEntity</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">copyEntity</span><wbr><span class="parameters">(<a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a>&nbsp;source,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;classObject,
 boolean&nbsp;setFieldDirectly,
 <a href="YadaAttachedFileCloneSet.html" title="class in net.yadaframework.components">YadaAttachedFileCloneSet</a>&nbsp;yadaAttachedFileCloneSet)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - the instance to copy</dd>
<dd><code>classObject</code> - class to use to create the new clone when the source is inside a HibernateProxy</dd>
<dd><code>setFieldDirectly</code> - false to use getter/setter, true to access the Field directly</dd>
<dd><code>yadaAttachedFileCloneSet</code> - when not null, all files are copied to a temp folder.
              This is useful when the final path depends on the id
                  of a cloned object so it can't be determined during cloning.
                  The method yadaAttachedFileCloneSet.moveAll() will have to be called after the clone has been persisted.</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isType(java.lang.Class,java.lang.Class)">
<h3>isType</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;fieldType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;requiredType)</span></div>
<div class="block">Check if a class is of a given type, considering superclasses and interfaces (of superclasses)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fieldType</code> - </dd>
<dd><code>requiredType</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="dateWithin(java.util.Calendar,int,int,int,int)">
<h3>dateWithin</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">dateWithin</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;dateToCheck,
 int&nbsp;fromMonth,
 int&nbsp;fromDayInclusive,
 int&nbsp;toMonth,
 int&nbsp;toDayExcluded)</span></div>
<div class="block">Check if a date is within two dates expressed as month/day, regardless of the year and of the validity of such dates.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dateToCheck</code> - for example new GregorianCalendar()</dd>
<dd><code>fromMonth</code> - 0-based, better use Calendar.JANUARY etc.</dd>
<dd><code>fromDayInclusive</code> - 1-based</dd>
<dd><code>toMonth</code> - 0-based, better use Calendar.JANUARY etc.</dd>
<dd><code>toDayExcluded</code> - 1-based</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="roundBackToHour(java.util.Date)">
<h3>roundBackToHour</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">roundBackToHour</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Ritorna l'ora più vicina nel passato alla data specificata</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="roundForwardToHour(java.util.Calendar)">
<h3>roundForwardToHour</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">roundForwardToHour</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</span></div>
<div class="block">Returns the same calendar object aligned to the next hour</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>calendar</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="roundBackToHour(java.util.Calendar)">
<h3>roundBackToHour</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">roundBackToHour</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</span></div>
<div class="block">Returns the same calendar object aligned to the previous hour</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>calendar</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="roundBackToHour(java.util.Date,java.util.TimeZone)">
<h3>roundBackToHour</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">roundBackToHour</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</span></div>
<div class="block">Resets minutes to zero</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>date</code> - </dd>
<dd><code>timezone</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="daysBetween(java.time.ZonedDateTime,java.time.ZonedDateTime)">
<h3>daysBetween</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">daysBetween</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/ZonedDateTime.html" title="class or interface in java.time" class="external-link">ZonedDateTime</a>&nbsp;olderDate,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/ZonedDateTime.html" title="class or interface in java.time" class="external-link">ZonedDateTime</a>&nbsp;earlierDate)</span></div>
<div class="block">Returns the days between two dates.
 It doesn't take into consideration the time component, so the difference between some time yesterday and any other time today
 will always be 1</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>olderDate</code> - </dd>
<dd><code>earlierDate</code> - </dd>
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/time/ZonedDateTime.html#until(java.time.temporal.Temporal,java.time.temporal.TemporalUnit)" title="class or interface in java.time" class="external-link"><code>ZonedDateTime.until(java.time.temporal.Temporal, java.time.temporal.TemporalUnit)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="daysDifference(java.util.Date,java.util.Date)">
<h3>daysDifference</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">daysDifference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date1,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date2)</span></div>
<div class="block">Counts the days interval between two dates. Time component is ignored.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>date1</code> - </dd>
<dd><code>date2</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="minutesDifference(java.util.Date,java.util.Date)">
<h3>minutesDifference</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">minutesDifference</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;recentDate,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;oldDate)</span></div>
<div class="block">Returns the minutes between two dates.
 It is negative when the first argument is earlier than the second.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>recentDate</code> - </dd>
<dd><code>oldDate</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="minutesDifferenceAbs(java.util.Date,java.util.Date)">
<h3>minutesDifferenceAbs</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">minutesDifferenceAbs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;firstDate,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;secondDate)</span></div>
<div class="block">Returns the absolute value of the minutes between two dates.
 It will always be positive.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>firstDate</code> - </dd>
<dd><code>secondDate</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="millisSinceMidnight(java.util.Calendar)">
<h3>millisSinceMidnight</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">millisSinceMidnight</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</span></div>
<div class="block">Returns the number of milliseconds since midnight</div>
</section>
</li>
<li>
<section class="detail" id="roundBackToMidnight(java.util.Date,java.util.TimeZone)">
<h3>roundBackToMidnight</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">roundBackToMidnight</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</span></div>
</section>
</li>
<li>
<section class="detail" id="roundBackToMidnightClone(java.util.Calendar)">
<h3>roundBackToMidnightClone</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">roundBackToMidnightClone</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;source)</span></div>
<div class="block">Create a new calendar rounded back to the start of the day.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>calendar</code> - the calendar to copy</dd>
<dt>Returns:</dt>
<dd>a new calendar</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="roundBackToMidnight(java.util.Calendar)">
<h3>roundBackToMidnight</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">roundBackToMidnight</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</span></div>
<div class="block">Rounds back the calendar to the start of the day.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>calendar</code> - the calendar to change: the parameter will be modified by this method</dd>
<dt>Returns:</dt>
<dd>the input calendar modified.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="roundForwardToAlmostMidnight(java.util.Calendar)">
<h3>roundForwardToAlmostMidnight</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">roundForwardToAlmostMidnight</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</span></div>
<div class="block">Rounds forward the calendar to the end of the day at 23:59:59.999</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>calendar</code> - the calendar to change: the parameter will be modified by this method</dd>
<dt>Returns:</dt>
<dd>the input calendar modified.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLastMidnight()">
<h3>getLastMidnight</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">getLastMidnight</span>()</div>
<div class="block">Returns the last midnight</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="addDaysClone(java.util.Calendar,int)">
<h3>addDaysClone</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">addDaysClone</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;source,
 int&nbsp;days)</span></div>
<div class="block">Adds or removes the days. The original object is cloned.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>days</code> - </dd>
<dd><code>calendar</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="addDays(java.util.Calendar,int)">
<h3>addDays</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">addDays</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar,
 int&nbsp;days)</span></div>
<div class="block">Adds or removes the days. The original object is modified.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>calendar</code> - </dd>
<dd><code>days</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="addMinutes(java.util.Calendar,int)">
<h3>addMinutes</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">addMinutes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar,
 int&nbsp;minutes)</span></div>
<div class="block">Adds or removes the minutes. The original object is modified.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>calendar</code> - </dd>
<dd><code>minutes</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="addMinutes(java.util.Date,int)">
<h3>addMinutes</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">addMinutes</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 int&nbsp;minutes)</span></div>
<div class="block">Aggiunge (o rimuove) i minuti indicati dalla data</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>date</code> - </dd>
<dd><code>hours</code> - numero di minuti, può essere negativo</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="addHours(java.util.Date,int)">
<h3>addHours</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">addHours</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 int&nbsp;hours)</span></div>
<div class="block">Aggiunge (o rimuove) le ore indicate dalla data</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>date</code> - </dd>
<dd><code>hours</code> - numero di ore, può essere negativo</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="addDays(java.util.Date,int)">
<h3>addDays</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">addDays</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 int&nbsp;days)</span></div>
<div class="block">Aggiunge (o rimuove) i giorni indicati dalla data</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>date</code> - </dd>
<dd><code>days</code> - numero di giorni, può essere negativo</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="addYears(java.util.Date,int)">
<h3>addYears</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">addYears</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 int&nbsp;years)</span></div>
<div class="block">Aggiunge (o rimuove) gli anni indicati dalla data (approssimato)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>date</code> - </dd>
<dd><code>years</code> - numero di 365 giorni, può essere negativo</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="sameDay(java.util.Date,java.util.Date)">
<h3>sameDay</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">sameDay</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;a,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;b)</span></div>
<div class="block">Returns true if the two dates are on the same day</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>a</code> - </dd>
<dd><code>b</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="createZipProcess(java.io.File,java.io.File[],java.lang.String[],boolean)">
<h3>createZipProcess</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">createZipProcess</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;sourceFiles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;filenames,
 boolean&nbsp;fixNames)</span>
                         throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Create a zip of a set of files using an external process. The process must be configured as "config/shell/zipWithRename"
 and should use zip and zipnote (for renaming). See the /YadaWeb/scripts folder for an example.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>zipFile</code> - the zip file that has to be created</dd>
<dd><code>sourceFiles</code> - the files to add to the zip</dd>
<dd><code>filenames</code> - optional names to give to each added file, in order</dd>
<dd><code>fixNames</code> - when true, any repeated name will be given an incremental number (regardless or renaming)
                  and if filenames is provided, the renamed file will be forced to have the same
                  extension of the source file (existing extensions will be removed).</dd>
<dt>Returns:</dt>
<dd>true if the zip file has been created</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
<dd><code><a href="../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - when the length of filenames is greater than zero but different from the length of sourceFiles</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createZipFileFromFolders(java.io.File,java.io.File[])">
<h3>createZipFileFromFolders</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">createZipFileFromFolders</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;foldersToZip)</span>
                              throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Create a zip file of a folder</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>zipFile</code> - the target zip file</dd>
<dd><code>foldersToZip</code> - the folders to zip</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createZipFile(java.io.File,java.io.File[],java.lang.String[])">
<h3>createZipFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">createZipFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;sourceFiles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;filenamesNoExtension)</span></div>
<div class="block">Create a zip of a list of files.
 An exception is thrown when a source file is not readable.
 Adapted from http://www.exampledepot.com/egs/java.util.zip/CreateZip.html</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>zipFile</code> - zip file to create</dd>
<dd><code>sourceFiles</code> - files to zip</dd>
<dd><code>filenamesNoExtension</code> - optional list of names to give to zip entries. The name extension is also optional: it will be taken from the source file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createZipFile(java.io.File,java.io.File[],java.lang.String[],boolean)">
<h3>createZipFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">createZipFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;zipFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;sourceFiles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;filenamesNoExtension,
 boolean&nbsp;ignoreErrors)</span></div>
<div class="block">Create a zip of a list of files.
 Adapted from http://www.exampledepot.com/egs/java.util.zip/CreateZip.html</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>zipFile</code> - zip file to create</dd>
<dd><code>sourceFiles</code> - files to zip</dd>
<dd><code>filenamesNoExtension</code> - optional list of names to give to zip entries. The name extension is also optional: it will be taken from the source file</dd>
<dd><code>ignoreErrors</code> - true to ignore a file error and keep going with the next file</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="roundBackToLastMonthStart(java.util.Calendar)">
<h3>roundBackToLastMonthStart</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">roundBackToLastMonthStart</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</span></div>
</section>
</li>
<li>
<section class="detail" id="roundBackToMonth(java.util.Calendar)">
<h3>roundBackToMonth</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a></span>&nbsp;<span class="element-name">roundBackToMonth</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Calendar.html" title="class or interface in java.util" class="external-link">Calendar</a>&nbsp;calendar)</span></div>
</section>
</li>
<li>
<section class="detail" id="roundBackToMonth(java.util.Date,java.util.TimeZone)">
<h3>roundBackToMonth</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">roundBackToMonth</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</span></div>
</section>
</li>
<li>
<section class="detail" id="roundFowardToMonth(java.util.Date,java.util.TimeZone)">
<h3>roundFowardToMonth</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">roundFowardToMonth</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;date,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a>&nbsp;timezone)</span></div>
</section>
</li>
<li>
<section class="detail" id="normalizzaCellulareItaliano(java.lang.String)">
<h3>normalizzaCellulareItaliano</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">normalizzaCellulareItaliano</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cellulare)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</section>
</li>
<li>
<section class="detail" id="validaCellulare(java.lang.String)">
<h3>validaCellulare</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">validaCellulare</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cellulare)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</section>
</li>
<li>
<section class="detail" id="sortByValue(java.util.Map)">
<h3>sortByValue</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/SortedSet.html" title="class or interface in java.util" class="external-link">SortedSet</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.Entry.html" title="class or interface in java.util" class="external-link">Map.Entry</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&gt;</span>&nbsp;<span class="element-name">sortByValue</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="sortByKey(java.util.Map)">
<h3>sortByKey</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/SortedSet.html" title="class or interface in java.util" class="external-link">SortedSet</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.Entry.html" title="class or interface in java.util" class="external-link">Map.Entry</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&gt;</span>&nbsp;<span class="element-name">sortByKey</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&nbsp;data)</span></div>
</section>
</li>
<li>
<section class="detail" id="getApplicationContext()">
<h3>getApplicationContext</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">org.springframework.context.ApplicationContext</span>&nbsp;<span class="element-name">getApplicationContext</span>()</div>
<div class="block">Ritorna un riferimento all'ApplicationContext per l'utilizzo fuori dal Container (ad esempio negli Entity)</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="setApplicationContext(org.springframework.context.ApplicationContext)">
<h3>setApplicationContext</h3>
<div class="member-signature"><span class="annotations">@Autowired
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setApplicationContext</span><wbr><span class="parameters">(org.springframework.context.ApplicationContext&nbsp;applicationContext)</span></div>
</section>
</li>
<li>
<section class="detail" id="reduceToSafeFilename(java.lang.String)">
<h3>reduceToSafeFilename</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">reduceToSafeFilename</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;originalFilename)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Does not produce the same results of all OS</div>
</div>
<div class="block">Converte un filename in modo che sia valido sia per il filesystem (unix/dos) sia per il browser.
 E' molto distruttiva in quanto i caratteri non previsti vengono eliminati. Per questo si chiama "reduce" :-)
 Converte anche a lowercase.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>originalFilename</code> - </dd>
<dt>Returns:</dt>
<dd>un filename safe, dove i caratteri speciali sono scomparsi</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#ensureSafeFilename(java.lang.String)"><code>ensureSafeFilename(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="reduceToSafeFilename(java.lang.String,boolean)">
<h3>reduceToSafeFilename</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">reduceToSafeFilename</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;originalFilename,
 boolean&nbsp;toLowercase)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Does not produce the same results of all OS</div>
</div>
<div class="block">Converte un filename in modo che sia valido sia per il filesystem (unix/dos) sia per il browser.
 E' molto distruttiva in quanto i caratteri non previsti vengono eliminati. Per questo si chiama "reduce" :-)
 Converte anche a lowercase.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>originalFilename</code> - </dd>
<dd><code>toLowercase</code> - true for a lowercase name</dd>
<dt>Returns:</dt>
<dd>un filename safe, dove i caratteri speciali sono scomparsi</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#ensureSafeFilename(java.lang.String,boolean)"><code>ensureSafeFilename(String, boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ensureSafeFilename(java.lang.String)">
<h3>ensureSafeFilename</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ensureSafeFilename</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;originalFilename)</span></div>
<div class="block">Converts a candidate filename so that it is valid on all operating systems and browsers, if needed, and also to lowercase.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>originalFilename</code> - the name to process</dd>
<dt>Returns:</dt>
<dd>either the lowercase original string or something similar. It returns "noname" when the originalFilename is blank.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ensureSafeFilename(java.lang.String,boolean)">
<h3>ensureSafeFilename</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ensureSafeFilename</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;originalFilename,
 boolean&nbsp;toLowercase)</span></div>
<div class="block">Converts a candidate filename so that it is valid on all operating systems and browsers, if needed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>originalFilename</code> - the name to process</dd>
<dd><code>toLowercase</code> - true to convert to lowercase</dd>
<dt>Returns:</dt>
<dd>either the original string or something similar. It returns "noname" when the originalFilename is blank.</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
