<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaDTOptions (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.datatables.options, class: YadaDTOptions">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.datatables.options</a></div>
<h1 title="Class YadaDTOptions" class="title">Class YadaDTOptions</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">net.yadaframework.core.YadaFluentBase</a>&lt;<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&gt;
<div class="inheritance">net.yadaframework.web.datatables.options.YadaDTOptions</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../proxy/YadaDTOptionsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTOptionsProxy</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaDTOptions</span>
<span class="extends-implements">extends <a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&gt;</span></div>
<div class="block">Class representing options for configuring DataTables.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/">DataTables Reference</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#autoWidth" class="member-name-link">autoWidth</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#caption" class="member-name-link">caption</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#columnDefs" class="member-name-link">columnDefs</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#columns" class="member-name-link">columns</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#createdRow" class="member-name-link">createdRow</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#data" class="member-name-link">data</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#dataTableExtErrMode" class="member-name-link">dataTableExtErrMode</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color"><code><a href="#deferLoading" class="member-name-link">deferLoading</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#deferRender" class="member-name-link">deferRender</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#destroy" class="member-name-link">destroy</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color"><code><a href="#displayStart" class="member-name-link">displayStart</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#drawCallback" class="member-name-link">drawCallback</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#footerCallback" class="member-name-link">footerCallback</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#formatNumber" class="member-name-link">formatNumber</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#headerCallback" class="member-name-link">headerCallback</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#info" class="member-name-link">info</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#infoCallback" class="member-name-link">infoCallback</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#initComplete" class="member-name-link">initComplete</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color"><code><a href="#layout" class="member-name-link">layout</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#lengthChange" class="member-name-link">lengthChange</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected int[]</code></div>
<div class="col-second even-row-color"><code><a href="#lengthMenu" class="member-name-link">lengthMenu</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="YadaDTOrder.html" title="class in net.yadaframework.web.datatables.options">YadaDTOrder</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#order" class="member-name-link">order</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#orderClasses" class="member-name-link">orderClasses</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#orderDescReverse" class="member-name-link">orderDescReverse</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color"><code><a href="#orderFixed" class="member-name-link">orderFixed</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#ordering" class="member-name-link">ordering</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#orderMulti" class="member-name-link">orderMulti</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second odd-row-color"><code><a href="#pageLength" class="member-name-link">pageLength</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#paging" class="member-name-link">paging</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#preDrawCallback" class="member-name-link">preDrawCallback</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#processing" class="member-name-link">processing</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#renderer" class="member-name-link">renderer</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#responsive" class="member-name-link">responsive</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#rowCallback" class="member-name-link">rowCallback</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#rowId" class="member-name-link">rowId</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#scrollCollapse" class="member-name-link">scrollCollapse</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#scrollX" class="member-name-link">scrollX</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#scrollY" class="member-name-link">scrollY</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color"><code><a href="#searchDelay" class="member-name-link">searchDelay</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#searching" class="member-name-link">searching</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#serverSide" class="member-name-link">serverSide</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second odd-row-color"><code><a href="#stateDuration" class="member-name-link">stateDuration</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#stateLoadCallback" class="member-name-link">stateLoadCallback</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#stateLoaded" class="member-name-link">stateLoaded</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#stateLoadParams" class="member-name-link">stateLoadParams</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#stateSave" class="member-name-link">stateSave</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#stateSaveCallback" class="member-name-link">stateSaveCallback</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#stateSaveParams" class="member-name-link">stateSaveParams</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color"><code><a href="#tabIndex" class="member-name-link">tabIndex</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#typeDetect" class="member-name-link">typeDetect</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="YadaDTResponsive.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsive</a></code></div>
<div class="col-second even-row-color"><code><a href="#yadaDTResponsive" class="member-name-link">yadaDTResponsive</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.core.YadaFluentBase">Fields inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#parent">parent</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(net.yadaframework.web.datatables.YadaDataTable)" class="member-name-link">YadaDTOptions</a><wbr>(<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&nbsp;parent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtAutoWidthOff()" class="member-name-link">dtAutoWidthOff</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disable automatic column width calculation as an optimisation.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtCaption(java.lang.String)" class="member-name-link">dtCaption</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;caption)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `caption` option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtColumnDefsObj()" class="member-name-link">dtColumnDefsObj</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtColumnsObj()" class="member-name-link">dtColumnsObj</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtCreatedRow(java.lang.String)" class="member-name-link">dtCreatedRow</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;createdRow)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `createdRow` option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtData(java.util.List)" class="member-name-link">dtData</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `data` option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDataTableExtErrMode(java.lang.String)" class="member-name-link">dtDataTableExtErrMode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dataTableExtErrMode)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `dataTableExtErrMode` option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDeferLoading(int)" class="member-name-link">dtDeferLoading</a><wbr>(int&nbsp;totItems)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enables deferred loading, and instructs DataTables as to how many items are in the full data set</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDeferLoading(int,int)" class="member-name-link">dtDeferLoading</a><wbr>(int&nbsp;filteredItems,
 int&nbsp;totItems)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enables deferred loading, where the first data index tells DataTables how many rows are in 
 the filtered result set, and the second how many in the full data set without filtering applied.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDeferRenderOff()" class="member-name-link">dtDeferRenderOff</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Do not wait to create HTML elements when they are needed        for display.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDestroy()" class="member-name-link">dtDestroy</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise a new DataTable as usual, but if there is an existing DataTable 
 which matches the selector, it will be destroyed and replaced with the new table.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDisplayStart(java.lang.Integer)" class="member-name-link">dtDisplayStart</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;displayStart)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `displayStart` option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDrawCallback(java.lang.String)" class="member-name-link">dtDrawCallback</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;drawCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `drawCallback` option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtFooterCallback(java.lang.String)" class="member-name-link">dtFooterCallback</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;footerCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `footerCallback` option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtFormatNumber(java.lang.String)" class="member-name-link">dtFormatNumber</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;formatNumber)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `formatNumber` option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtHeaderCallback(java.lang.String)" class="member-name-link">dtHeaderCallback</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;headerCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `headerCallback` option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtInfoCallback(java.lang.String)" class="member-name-link">dtInfoCallback</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;infoCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `infoCallback` option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtInfoOff()" class="member-name-link">dtInfoOff</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disables showing details about the table including information about 
 filtered data if that action is being performed.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtInitComplete(java.lang.String)" class="member-name-link">dtInitComplete</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;initComplete)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `initComplete` option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtLayout(java.lang.String)" class="member-name-link">dtLayout</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layout)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `layout` option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtLengthChangeOff()" class="member-name-link">dtLengthChangeOff</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disables user's ability to change the paging display length of the table.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtLengthMenu(int...)" class="member-name-link">dtLengthMenu</a><wbr>(int...&nbsp;lengthChoice)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `lengthMenu` option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrder(int,java.lang.String)" class="member-name-link">dtOrder</a><wbr>(int&nbsp;idx,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `order` option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderClassesOff()" class="member-name-link">dtOrderClassesOff</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disable the addition of ordering classes to the columns
 when performance is an issue.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderDescReverseOff()" class="member-name-link">dtOrderDescReverseOff</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disable the default reverse.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderFixed(java.lang.Object)" class="member-name-link">dtOrderFixed</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;orderFixed)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `orderFixed` option.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderingOff()" class="member-name-link">dtOrderingOff</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disables ordering (sorting) abilities</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderMultiOff()" class="member-name-link">dtOrderMultiOff</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disables multiple column ordering ability</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtPageLength(java.lang.Integer)" class="member-name-link">dtPageLength</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;pageLength)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `pageLength` option that tells how many rows should be visible.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtPagingOff()" class="member-name-link">dtPagingOff</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disable table pagination.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtPreDrawCallback(java.lang.String)" class="member-name-link">dtPreDrawCallback</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;preDrawCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `preDrawCallback` option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtProcessingOn()" class="member-name-link">dtProcessingOn</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enable the display of a 'processing' indicator when the table is being processed (e.g.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtRenderer(java.lang.String)" class="member-name-link">dtRenderer</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;renderer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `renderer` option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTResponsive.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsive</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtResponsiveObj()" class="member-name-link">dtResponsiveObj</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Turns on responsive behaviour and provides access to the responsive configuration options for DataTables.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtResponsiveOn()" class="member-name-link">dtResponsiveOn</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the responsive option so that the table will adapt for different screen sizes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtRowCallback(java.lang.String)" class="member-name-link">dtRowCallback</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;rowCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This callback allows you to 'post process' each row after it have been generated 
 for each table draw, but before it is rendered into the document.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtRowId(java.lang.String)" class="member-name-link">dtRowId</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;rowId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">DataTables will attempt to automatically read an id value from the data 
 source for each row using the property defined by this option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtScrollCollapseOn()" class="member-name-link">dtScrollCollapseOn</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Allow the table to reduce in height when a limited number of rows are shown.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtScrollXOn()" class="member-name-link">dtScrollXOn</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enable horizontal scrolling.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtScrollY(java.lang.String)" class="member-name-link">dtScrollY</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssHeight)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enable vertical scrolling.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtSearchDelay(java.lang.Integer)" class="member-name-link">dtSearchDelay</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;searchDelayMillis)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a delay for search operations.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtSearchingOff()" class="member-name-link">dtSearchingOff</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disable searching abilities in DataTables.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtServerSideOff()" class="member-name-link">dtServerSideOff</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disable server-side processing.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtStateDuration(java.lang.Integer)" class="member-name-link">dtStateDuration</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;stateDuration)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Duration for which the saved state information is considered valid.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtStateLoadCallback(java.lang.String)" class="member-name-link">dtStateLoadCallback</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stateLoadCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Callback that defines where and how a saved state should be loaded.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtStateLoaded(java.lang.String)" class="member-name-link">dtStateLoaded</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stateLoaded)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Callback that is fired once the state has been loaded 
 and the saved data manipulated.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtStateLoadParams(java.lang.String)" class="member-name-link">dtStateLoadParams</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stateLoadParams)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Callback which allows modification of the saved state prior to loading that state.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtStateSaveCallback(java.lang.String)" class="member-name-link">dtStateSaveCallback</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stateSaveCallback)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Callback that defines how the table state is stored and where.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtStateSaveOn()" class="member-name-link">dtStateSaveOn</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enable state saving such as pagination position, display length, filtering and sorting.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtStateSaveParams(java.lang.String)" class="member-name-link">dtStateSaveParams</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stateSaveParams)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Callback which allows modification of the parameters to be saved for 
 the DataTables state saving prior to the data actually being saved.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTabIndex(java.lang.Integer)" class="member-name-link">dtTabIndex</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;tabIndex)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the `tabIndex` overrule option.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTypeDetectOff()" class="member-name-link">dtTypeDetectOff</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disable the auto type detection that DataTables performs.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-net.yadaframework.core.YadaFluentBase">Methods inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#back()">back</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="dataTableExtErrMode">
<h3>dataTableExtErrMode</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">dataTableExtErrMode</span></div>
</section>
</li>
<li>
<section class="detail" id="autoWidth">
<h3>autoWidth</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">autoWidth</span></div>
</section>
</li>
<li>
<section class="detail" id="caption">
<h3>caption</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">caption</span></div>
</section>
</li>
<li>
<section class="detail" id="columnDefs">
<h3>columnDefs</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a>&gt;</span>&nbsp;<span class="element-name">columnDefs</span></div>
</section>
</li>
<li>
<section class="detail" id="columns">
<h3>columns</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a>&gt;</span>&nbsp;<span class="element-name">columns</span></div>
</section>
</li>
<li>
<section class="detail" id="createdRow">
<h3>createdRow</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">createdRow</span></div>
</section>
</li>
<li>
<section class="detail" id="data">
<h3>data</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">data</span></div>
</section>
</li>
<li>
<section class="detail" id="deferLoading">
<h3>deferLoading</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">deferLoading</span></div>
</section>
</li>
<li>
<section class="detail" id="deferRender">
<h3>deferRender</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">deferRender</span></div>
</section>
</li>
<li>
<section class="detail" id="destroy">
<h3>destroy</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">destroy</span></div>
</section>
</li>
<li>
<section class="detail" id="typeDetect">
<h3>typeDetect</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">typeDetect</span></div>
</section>
</li>
<li>
<section class="detail" id="displayStart">
<h3>displayStart</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">displayStart</span></div>
</section>
</li>
<li>
<section class="detail" id="drawCallback">
<h3>drawCallback</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">drawCallback</span></div>
</section>
</li>
<li>
<section class="detail" id="footerCallback">
<h3>footerCallback</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">footerCallback</span></div>
</section>
</li>
<li>
<section class="detail" id="formatNumber">
<h3>formatNumber</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">formatNumber</span></div>
</section>
</li>
<li>
<section class="detail" id="headerCallback">
<h3>headerCallback</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">headerCallback</span></div>
</section>
</li>
<li>
<section class="detail" id="info">
<h3>info</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">info</span></div>
</section>
</li>
<li>
<section class="detail" id="infoCallback">
<h3>infoCallback</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">infoCallback</span></div>
</section>
</li>
<li>
<section class="detail" id="initComplete">
<h3>initComplete</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">initComplete</span></div>
</section>
</li>
<li>
<section class="detail" id="layout">
<h3>layout</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">layout</span></div>
</section>
</li>
<li>
<section class="detail" id="lengthChange">
<h3>lengthChange</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">lengthChange</span></div>
</section>
</li>
<li>
<section class="detail" id="lengthMenu">
<h3>lengthMenu</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int[]</span>&nbsp;<span class="element-name">lengthMenu</span></div>
</section>
</li>
<li>
<section class="detail" id="order">
<h3>order</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="YadaDTOrder.html" title="class in net.yadaframework.web.datatables.options">YadaDTOrder</a>&gt;</span>&nbsp;<span class="element-name">order</span></div>
</section>
</li>
<li>
<section class="detail" id="orderClasses">
<h3>orderClasses</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">orderClasses</span></div>
</section>
</li>
<li>
<section class="detail" id="orderDescReverse">
<h3>orderDescReverse</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">orderDescReverse</span></div>
</section>
</li>
<li>
<section class="detail" id="orderFixed">
<h3>orderFixed</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">orderFixed</span></div>
</section>
</li>
<li>
<section class="detail" id="orderMulti">
<h3>orderMulti</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">orderMulti</span></div>
</section>
</li>
<li>
<section class="detail" id="ordering">
<h3>ordering</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">ordering</span></div>
</section>
</li>
<li>
<section class="detail" id="pageLength">
<h3>pageLength</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">pageLength</span></div>
</section>
</li>
<li>
<section class="detail" id="paging">
<h3>paging</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">paging</span></div>
</section>
</li>
<li>
<section class="detail" id="preDrawCallback">
<h3>preDrawCallback</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">preDrawCallback</span></div>
</section>
</li>
<li>
<section class="detail" id="processing">
<h3>processing</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">processing</span></div>
</section>
</li>
<li>
<section class="detail" id="renderer">
<h3>renderer</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">renderer</span></div>
</section>
</li>
<li>
<section class="detail" id="responsive">
<h3>responsive</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">responsive</span></div>
</section>
</li>
<li>
<section class="detail" id="yadaDTResponsive">
<h3>yadaDTResponsive</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="YadaDTResponsive.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsive</a></span>&nbsp;<span class="element-name">yadaDTResponsive</span></div>
</section>
</li>
<li>
<section class="detail" id="rowCallback">
<h3>rowCallback</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">rowCallback</span></div>
</section>
</li>
<li>
<section class="detail" id="rowId">
<h3>rowId</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">rowId</span></div>
</section>
</li>
<li>
<section class="detail" id="scrollCollapse">
<h3>scrollCollapse</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">scrollCollapse</span></div>
</section>
</li>
<li>
<section class="detail" id="scrollX">
<h3>scrollX</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">scrollX</span></div>
</section>
</li>
<li>
<section class="detail" id="scrollY">
<h3>scrollY</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">scrollY</span></div>
</section>
</li>
<li>
<section class="detail" id="searchDelay">
<h3>searchDelay</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">searchDelay</span></div>
</section>
</li>
<li>
<section class="detail" id="searching">
<h3>searching</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">searching</span></div>
</section>
</li>
<li>
<section class="detail" id="serverSide">
<h3>serverSide</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">serverSide</span></div>
</section>
</li>
<li>
<section class="detail" id="stateDuration">
<h3>stateDuration</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">stateDuration</span></div>
</section>
</li>
<li>
<section class="detail" id="stateLoadCallback">
<h3>stateLoadCallback</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">stateLoadCallback</span></div>
</section>
</li>
<li>
<section class="detail" id="stateLoadParams">
<h3>stateLoadParams</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">stateLoadParams</span></div>
</section>
</li>
<li>
<section class="detail" id="stateLoaded">
<h3>stateLoaded</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">stateLoaded</span></div>
</section>
</li>
<li>
<section class="detail" id="stateSave">
<h3>stateSave</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">stateSave</span></div>
</section>
</li>
<li>
<section class="detail" id="stateSaveCallback">
<h3>stateSaveCallback</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">stateSaveCallback</span></div>
</section>
</li>
<li>
<section class="detail" id="stateSaveParams">
<h3>stateSaveParams</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">stateSaveParams</span></div>
</section>
</li>
<li>
<section class="detail" id="tabIndex">
<h3>tabIndex</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">tabIndex</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(net.yadaframework.web.datatables.YadaDataTable)">
<h3>YadaDTOptions</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaDTOptions</span><wbr><span class="parameters">(<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&nbsp;parent)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="dtData(java.util.List)">
<h3>dtData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtData</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;data)</span></div>
<div class="block">Sets the `data` option.
 This is normally not used as data is fetched from the server in Yada Framework.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>data</code> - the data to display in the table. Each object of the list must be serializable by Jackson</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/data">data</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtDataTableExtErrMode(java.lang.String)">
<h3>dtDataTableExtErrMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtDataTableExtErrMode</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dataTableExtErrMode)</span></div>
<div class="block">Sets the `dataTableExtErrMode` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dataTableExtErrMode</code> - the error mode for the DataTable</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/">dataTableExtErrMode</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtAutoWidthOff()">
<h3>dtAutoWidthOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtAutoWidthOff</span>()</div>
<div class="block">Disable automatic column width calculation as an optimisation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/autoWidth">autoWidth</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtCaption(java.lang.String)">
<h3>dtCaption</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtCaption</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;caption)</span></div>
<div class="block">Sets the `caption` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>caption</code> - the table caption</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/caption">caption</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtCreatedRow(java.lang.String)">
<h3>dtCreatedRow</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtCreatedRow</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;createdRow)</span></div>
<div class="block">Sets the `createdRow` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>createdRow</code> - a callback function to manipulate the row after it has been created</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/createdRow">createdRow</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtDeferLoading(int)">
<h3>dtDeferLoading</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtDeferLoading</span><wbr><span class="parameters">(int&nbsp;totItems)</span></div>
<div class="block">Enables deferred loading, and instructs DataTables as to how many items are in the full data set</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>totItems</code> - items in the full data set</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/deferLoading">deferLoading</a></li>
<li><a href="#dtDeferLoading(int,int)"><code>dtDeferLoading(int,int)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtDeferLoading(int,int)">
<h3>dtDeferLoading</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtDeferLoading</span><wbr><span class="parameters">(int&nbsp;filteredItems,
 int&nbsp;totItems)</span></div>
<div class="block">Enables deferred loading, where the first data index tells DataTables how many rows are in 
 the filtered result set, and the second how many in the full data set without filtering applied.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>filteredItems</code> - items in the filtered data set</dd>
<dd><code>totItems</code> - items in the full data set</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/deferLoading">deferLoading</a></li>
<li><a href="#dtDeferLoading(int)"><code>dtDeferLoading(int)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtDeferRenderOff()">
<h3>dtDeferRenderOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtDeferRenderOff</span>()</div>
<div class="block">Do not wait to create HTML elements when they are needed        for display.
 The only reason to use this option is if you must have all DOM elements available, 
 even those currently not in the document.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/deferRender">deferRender</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtDestroy()">
<h3>dtDestroy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtDestroy</span>()</div>
<div class="block">Initialise a new DataTable as usual, but if there is an existing DataTable 
 which matches the selector, it will be destroyed and replaced with the new table.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/destroy">destroy</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtTypeDetectOff()">
<h3>dtTypeDetectOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtTypeDetectOff</span>()</div>
<div class="block">Disable the auto type detection that DataTables performs.
 This might be useful if you are using server-side processing where data can change between 
 pages and the client-side is unable to automatically reliably determine a column's data type.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/typeDetection">typeDetect</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtDisplayStart(java.lang.Integer)">
<h3>dtDisplayStart</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtDisplayStart</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;displayStart)</span></div>
<div class="block">Sets the `displayStart` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>displayStart</code> - the initial page to be displayed</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/displayStart">displayStart</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtDrawCallback(java.lang.String)">
<h3>dtDrawCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtDrawCallback</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;drawCallback)</span></div>
<div class="block">Sets the `drawCallback` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>drawCallback</code> - a callback function to execute on each draw event</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/drawCallback">drawCallback</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtFooterCallback(java.lang.String)">
<h3>dtFooterCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtFooterCallback</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;footerCallback)</span></div>
<div class="block">Sets the `footerCallback` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>footerCallback</code> - a callback function to manipulate the table footer</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/footerCallback">footerCallback</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtFormatNumber(java.lang.String)">
<h3>dtFormatNumber</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtFormatNumber</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;formatNumber)</span></div>
<div class="block">Sets the `formatNumber` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>formatNumber</code> - define the number format to use for DataTables</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/formatNumber">formatNumber</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtHeaderCallback(java.lang.String)">
<h3>dtHeaderCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtHeaderCallback</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;headerCallback)</span></div>
<div class="block">Sets the `headerCallback` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>headerCallback</code> - a callback function to manipulate the table header</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/headerCallback">headerCallback</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtInfoOff()">
<h3>dtInfoOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtInfoOff</span>()</div>
<div class="block">Disables showing details about the table including information about 
 filtered data if that action is being performed.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/info">info</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtInfoCallback(java.lang.String)">
<h3>dtInfoCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtInfoCallback</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;infoCallback)</span></div>
<div class="block">Sets the `infoCallback` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>infoCallback</code> - a callback function to customize the table information display</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/infoCallback">infoCallback</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtInitComplete(java.lang.String)">
<h3>dtInitComplete</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtInitComplete</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;initComplete)</span></div>
<div class="block">Sets the `initComplete` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>initComplete</code> - a callback function executed when the table initialization is complete</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/initComplete">initComplete</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtLayout(java.lang.String)">
<h3>dtLayout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtLayout</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;layout)</span></div>
<div class="block">Sets the `layout` option.
 Fluent api not implemented yet.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>layout</code> - the layout for the table</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/layout">layout</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtLengthChangeOff()">
<h3>dtLengthChangeOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtLengthChangeOff</span>()</div>
<div class="block">Disables user's ability to change the paging display length of the table.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/lengthChange">lengthChange</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtLengthMenu(int...)">
<h3>dtLengthMenu</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtLengthMenu</span><wbr><span class="parameters">(int...&nbsp;lengthChoice)</span></div>
<div class="block">Sets the `lengthMenu` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>lengthChoice</code> - the list of page length options to show the user, like 10, 25, 50, 100</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/lengthMenu">lengthMenu</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderClassesOff()">
<h3>dtOrderClassesOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtOrderClassesOff</span>()</div>
<div class="block">Disable the addition of ordering classes to the columns
 when performance is an issue.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>orderClasses</code> - enable or disable the addition of ordering classes to the columns</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/orderClasses">orderClasses</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderDescReverseOff()">
<h3>dtOrderDescReverseOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtOrderDescReverseOff</span>()</div>
<div class="block">Disable the default reverse.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/orderDescReverse">orderDescReverse</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderFixed(java.lang.Object)">
<h3>dtOrderFixed</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtOrderFixed</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;orderFixed)</span></div>
<div class="block">Sets the `orderFixed` option. Fluent api not implemented yet.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>orderFixed</code> - define fixed ordering of columns</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/orderFixed">orderFixed</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderMultiOff()">
<h3>dtOrderMultiOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtOrderMultiOff</span>()</div>
<div class="block">Disables multiple column ordering ability</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/orderMulti">orderMulti</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderingOff()">
<h3>dtOrderingOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtOrderingOff</span>()</div>
<div class="block">Disables ordering (sorting) abilities</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/ordering">ordering</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrder(int,java.lang.String)">
<h3>dtOrder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtOrder</span><wbr><span class="parameters">(int&nbsp;idx,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;dir)</span></div>
<div class="block">Sets the `order` option. Can be called multiple times to set multiple orders.
 There should be no need to use this directly because this functionality is implemented by 
 the dtColumnObj() fluent interface.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>idx</code> - column index</dd>
<dd><code>dir</code> - "asc" or "desc"</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="../config/YadaDataTableColumn.html#dtOrderAsc()"><code>YadaDataTableColumn.dtOrderAsc()</code></a></li>
<li><a href="https://datatables.net/reference/type/DataTables.Order">order</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtPageLength(java.lang.Integer)">
<h3>dtPageLength</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtPageLength</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;pageLength)</span></div>
<div class="block">Sets the `pageLength` option that tells how many rows should be visible.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pageLength</code> - the number of rows per page</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/pageLength">pageLength</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtPagingOff()">
<h3>dtPagingOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtPagingOff</span>()</div>
<div class="block">Disable table pagination.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/paging">paging</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtPreDrawCallback(java.lang.String)">
<h3>dtPreDrawCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtPreDrawCallback</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;preDrawCallback)</span></div>
<div class="block">Sets the `preDrawCallback` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>preDrawCallback</code> - a callback function before the table is redrawn</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/preDrawCallback">preDrawCallback</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtProcessingOn()">
<h3>dtProcessingOn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtProcessingOn</span>()</div>
<div class="block">Enable the display of a 'processing' indicator when the table is being processed (e.g. a sort) 
 for server-side processing. This is particularly useful for tables with large amounts of data where it 
 can take a noticeable amount of time to sort the entries.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/processing">processing</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtRenderer(java.lang.String)">
<h3>dtRenderer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtRenderer</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;renderer)</span></div>
<div class="block">Sets the `renderer` option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>renderer</code> - define the renderer for table elements</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/renderer">renderer</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtRowCallback(java.lang.String)">
<h3>dtRowCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtRowCallback</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;rowCallback)</span></div>
<div class="block">This callback allows you to 'post process' each row after it have been generated 
 for each table draw, but before it is rendered into the document.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rowCallback</code> - a callback function to manipulate a row</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/rowCallback">rowCallback</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtRowId(java.lang.String)">
<h3>dtRowId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtRowId</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;rowId)</span></div>
<div class="block">DataTables will attempt to automatically read an id value from the data 
 source for each row using the property defined by this option. 
 By default it is DT_RowId but can be set to any other name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rowId</code> - define the row ID field</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/rowId">rowId</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtScrollCollapseOn()">
<h3>dtScrollCollapseOn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtScrollCollapseOn</span>()</div>
<div class="block">Allow the table to reduce in height when a limited number of rows are shown.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtScrollY(java.lang.String)"><code>dtScrollY(String)</code></a></li>
<li><a href="https://datatables.net/reference/option/scrollCollapse">scrollCollapse</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtScrollXOn()">
<h3>dtScrollXOn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtScrollXOn</span>()</div>
<div class="block">Enable horizontal scrolling.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/scrollX">scrollX</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtScrollY(java.lang.String)">
<h3>dtScrollY</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtScrollY</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssHeight)</span></div>
<div class="block">Enable vertical scrolling. Vertical scrolling will constrain the DataTable to the given height.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cssHeight</code> - define the table's vertical scroll height as a CSS string, e.g. "200px"</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtScrollCollapseOn()"><code>dtScrollCollapseOn()</code></a></li>
<li><a href="https://datatables.net/reference/option/scrollY">scrollY</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtSearchDelay(java.lang.Integer)">
<h3>dtSearchDelay</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtSearchDelay</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;searchDelayMillis)</span></div>
<div class="block">Set a delay for search operations.
 This can be of particular use when using server-side processing 
 and you don't want every keystroke to trigger an Ajax request for data.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>searchDelay</code> - the delay in milliseconds before a search is performed</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/searchDelay">searchDelay</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtSearchingOff()">
<h3>dtSearchingOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtSearchingOff</span>()</div>
<div class="block">Disable searching abilities in DataTables.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/searching">searching</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtServerSideOff()">
<h3>dtServerSideOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtServerSideOff</span>()</div>
<div class="block">Disable server-side processing. This will prevent DataTables from making an Ajax request for data.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/serverSide">serverSide</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtStateDuration(java.lang.Integer)">
<h3>dtStateDuration</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtStateDuration</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;stateDuration)</span></div>
<div class="block">Duration for which the saved state information is considered valid. 
 After this period has elapsed the state will be returned to the default.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stateDuration</code> - the duration for which the saved state is retained</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtStateSaveOn()"><code>dtStateSaveOn()</code></a></li>
<li><a href="https://datatables.net/reference/option/stateDuration">stateDuration</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtStateLoadCallback(java.lang.String)">
<h3>dtStateLoadCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtStateLoadCallback</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stateLoadCallback)</span></div>
<div class="block">Callback that defines where and how a saved state should be loaded.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stateLoadCallback</code> - a callback function to load the state of the table</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtStateSaveOn()"><code>dtStateSaveOn()</code></a></li>
<li><a href="https://datatables.net/reference/option/stateLoadCallback">stateLoadCallback</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtStateLoadParams(java.lang.String)">
<h3>dtStateLoadParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtStateLoadParams</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stateLoadParams)</span></div>
<div class="block">Callback which allows modification of the saved state prior to loading that state.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stateLoadParams</code> - a callback function to modify the loaded state</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtStateSaveOn()"><code>dtStateSaveOn()</code></a></li>
<li><a href="https://datatables.net/reference/option/stateLoadParams">stateLoadParams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtStateLoaded(java.lang.String)">
<h3>dtStateLoaded</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtStateLoaded</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stateLoaded)</span></div>
<div class="block">Callback that is fired once the state has been loaded 
 and the saved data manipulated.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stateLoaded</code> - a callback function when the state is loaded</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtStateSaveOn()"><code>dtStateSaveOn()</code></a></li>
<li><a href="https://datatables.net/reference/option/stateLoaded">stateLoaded</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtStateSaveOn()">
<h3>dtStateSaveOn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtStateSaveOn</span>()</div>
<div class="block">Enable state saving such as pagination position, display length, filtering and sorting.
 When this initialisation option is active and the end user reloads the page the table's 
 state will be altered to match what they had previously set up.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtStateDuration(java.lang.Integer)"><code>dtStateDuration(Integer)</code></a></li>
<li><a href="https://datatables.net/reference/option/stateSave">stateSave</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtStateSaveCallback(java.lang.String)">
<h3>dtStateSaveCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtStateSaveCallback</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stateSaveCallback)</span></div>
<div class="block">Callback that defines how the table state is stored and where.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stateSaveCallback</code> - a callback function to save the state of the table</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtStateSaveOn()"><code>dtStateSaveOn()</code></a></li>
<li><a href="https://datatables.net/reference/option/stateSaveCallback">stateSaveCallback</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtStateSaveParams(java.lang.String)">
<h3>dtStateSaveParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtStateSaveParams</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;stateSaveParams)</span></div>
<div class="block">Callback which allows modification of the parameters to be saved for 
 the DataTables state saving prior to the data actually being saved.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stateSaveParams</code> - a callback function to modify the saved state</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtStateSaveOn()"><code>dtStateSaveOn()</code></a></li>
<li><a href="https://datatables.net/reference/option/stateSaveParams">stateSaveParams</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtTabIndex(java.lang.Integer)">
<h3>dtTabIndex</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtTabIndex</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;tabIndex)</span></div>
<div class="block">Sets the `tabIndex` overrule option.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tabIndex</code> - define the tab index of the table, default is 0 and -1 disables tabbing</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/tabIndex">tabIndex</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtColumnDefsObj()">
<h3>dtColumnDefsObj</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></span>&nbsp;<span class="element-name">dtColumnDefsObj</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>A new column definition for DataTables.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/columnDefs">DataTables columnDefs option</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtColumnsObj()">
<h3>dtColumnsObj</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtColumnsObj</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>A new column configuration for DataTables.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/columns">DataTables columns option</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtResponsiveOn()">
<h3>dtResponsiveOn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtResponsiveOn</span>()</div>
<div class="block">Sets the responsive option so that the table will adapt for different screen sizes.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/responsive">DataTables responsive Reference</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtResponsiveObj()">
<h3>dtResponsiveObj</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTResponsive.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsive</a></span>&nbsp;<span class="element-name">dtResponsiveObj</span>()</div>
<div class="block">Turns on responsive behaviour and provides access to the responsive configuration options for DataTables.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the instance of YadaDTResponsive for further configuration</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/responsive">DataTables responsive Reference</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
