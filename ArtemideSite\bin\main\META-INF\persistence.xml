<persistence xmlns="http://xmlns.jcp.org/xml/ns/persistence" version="2.2">
             
   <!-- This file is only needed to generate the db schema with the ant task -->
   
    <persistence-unit name="yadaPersistenceUnit">
    	<!-- <provider>org.hibernate.jpa.HibernatePersistenceProvider</provider> -->
    	<!-- These are needed for the legacy Hibernate Tools
    	<jar-file>file:../../ArtemideCommon/bin</jar-file>
    	<jar-file>file:../../../yadaframework/YadaWeb/bin</jar-file>
    	<jar-file>file:../../../yadaframework/YadaWebSecurity/bin</jar-file>
    	-->
   		<!-- These are needed for schema generation. You can remove the ones you don't need -->
  		<class>net.yadaframework.persistence.entity.YadaBrowserId</class>
  		<class>net.yadaframework.persistence.entity.YadaClause</class>
  		<class>net.yadaframework.persistence.entity.YadaPersistentEnum</class>
  		<class>net.yadaframework.persistence.entity.YadaJob</class>
  		<class>net.yadaframework.persistence.entity.YadaAttachedFile</class>
		<class>net.yadaframework.persistence.entity.YadaManagedFile</class>
  		<class>net.yadaframework.cms.persistence.entity.YadaGallerySlide</class>
  		<class>net.yadaframework.security.persistence.entity.YadaUserCredentials</class>
  		<class>net.yadaframework.security.persistence.entity.YadaUserProfile</class>
  		<class>net.yadaframework.security.persistence.entity.YadaRegistrationRequest</class>
  		<class>net.yadaframework.security.persistence.entity.YadaSocialCredentials</class>
  		<class>net.yadaframework.security.persistence.entity.YadaAutoLoginToken</class>
  		<!-- list all ArtemideCommon entity classes -->
  		<class>com.artemide.common.persistence.entity.AmdRegistrationRequest</class>
  		<class>com.artemide.common.persistence.entity.CatalogConfig</class>
  		<class>com.artemide.common.persistence.entity.Configurator</class>
  		<class>com.artemide.common.persistence.entity.ConfiguratorShape</class>
  		<class>com.artemide.common.persistence.entity.FamilyImages</class>
  		<class>com.artemide.common.persistence.entity.HomeModule</class>
  		<class>com.artemide.common.persistence.entity.HomePage2</class>
  		<class>com.artemide.common.persistence.entity.MyCollection</class>
  		<class>com.artemide.common.persistence.entity.NewsJournal</class>
  		<class>com.artemide.common.persistence.entity.NewsModule</class>
  		<class>com.artemide.common.persistence.entity.NewsTag</class>
  		<class>com.artemide.common.persistence.entity.PageModule</class>
  		<class>com.artemide.common.persistence.entity.PdfData</class>
  		<class>com.artemide.common.persistence.entity.PrenotaSlot</class>
  		<class>com.artemide.common.persistence.entity.PrenotaStore</class>
  		<class>com.artemide.common.persistence.entity.PrenotaUser</class>
  		<class>com.artemide.common.persistence.entity.PressKit</class>
  		<class>com.artemide.common.persistence.entity.ProductConfiguration</class>
  		<class>com.artemide.common.persistence.entity.ProductFile</class>
  		<class>com.artemide.common.persistence.entity.Project</class>
  		<class>com.artemide.common.persistence.entity.ProjectModule</class>
  		<class>com.artemide.common.persistence.entity.ProjectTag</class>
  		<class>com.artemide.common.persistence.entity.Publication</class>
  		<class>com.artemide.common.persistence.entity.UserProfile</class>
  		<!-- list all ArtemideCommon legacy entity classes -->
		<class>com.yr.entity.localstring.ArticoloDescrizione</class>
		<class>com.yr.entity.localstring.CustomButtonLink</class>
		<class>com.yr.entity.localstring.CustomButtonName</class>
		<class>com.yr.entity.localstring.DesignerDescription</class>
		<class>com.yr.entity.localstring.FamigliaNome</class>
		<class>com.yr.entity.localstring.FilterVarianteDescription</class>
		<class>com.yr.entity.localstring.GalleryDescriptionText</class>
		<!-- 
		<class>com.yr.entity.localstring.HomeGalleryTitle</class>
		<class>com.yr.entity.localstring.HomeGalleryTitleTwo</class>
		<class>com.yr.entity.localstring.HomeGalleryTitleThree</class>
		<class>com.yr.entity.localstring.HomeGallerySubtitle</class>
		<class>com.yr.entity.localstring.HomeProjectTitle</class>
		<class>com.yr.entity.localstring.HomeNewsTitle</class>
		<class>com.yr.entity.localstring.HomeNewsTitleTwo</class>
		<class>com.yr.entity.localstring.HomeNewsTitleThree</class>
		<class>com.yr.entity.localstring.HomeProjectTitleMobile</class>
		<class>com.yr.entity.localstring.HomeNewsTitleMobile</class>
		<class>com.yr.entity.localstring.HomeNewsTitleTwoMobile</class>
		<class>com.yr.entity.localstring.HomeNewsTitleThreeMobile</class>
		<class>com.yr.entity.localstring.HomeMicroBannerTitle</class>
		<class>com.yr.entity.localstring.HomeMicroBannerLink</class>
		<class>com.yr.entity.localstring.HomeLongBannerTitle</class>
		<class>com.yr.entity.localstring.HomeLongBannerTitleTwo</class>
		<class>com.yr.entity.localstring.HomeLongBannerSubtitle</class>
		<class>com.yr.entity.localstring.HomeLongBannerSubtitleTwo</class>
		<class>com.yr.entity.localstring.HomeLongBannerLink</class>
		<class>com.yr.entity.localstring.HomeLongBannerLinkTwo</class>
		<class>com.yr.entity.localstring.HomeLongBannerExtraLine</class>
		<class>com.yr.entity.localstring.HomeLongBannerExtraLineTwo</class>
		<class>com.yr.entity.localstring.HomeLongBannerEventDate</class>
		<class>com.yr.entity.localstring.HomeLongBannerEventDateTwo</class>
		<class>com.yr.entity.localstring.HomeLongBannerEventLocation</class>
		<class>com.yr.entity.localstring.HomeLongBannerEventLocationTwo</class>
		<class>com.yr.entity.localstring.NewsContent</class>
		<class>com.yr.entity.localstring.NewsTitle</class>
		-->
		<class>com.yr.entity.localstring.PublicationsLink</class>
		<class>com.yr.entity.localstring.PublicationsTitle</class>
		<class>com.yr.entity.localstring.PublicationsSubtitle</class>
		<class>com.yr.entity.localstring.ProdottoBallast</class>
		<class>com.yr.entity.localstring.ProdottoElectricalBallast</class>
		<class>com.yr.entity.localstring.ProdottoBaseColor</class>
		<class>com.yr.entity.localstring.ProdottoColor</class>
		<class>com.yr.entity.localstring.ProdottoDescription</class>
		<class>com.yr.entity.localstring.ProdottoEmergency</class>
		<class>com.yr.entity.localstring.ProdottoElectricalEmergency</class>
		<class>com.yr.entity.localstring.ProdottoMaterial</class>
		<class>com.yr.entity.localstring.ProdottoName</class>
		<class>com.yr.entity.localstring.ProdottoSubName</class>
		<class>com.yr.entity.localstring.ElectricalName</class>
		<class>com.yr.entity.localstring.ProdottoElectricalName</class>
		<class>com.yr.entity.localstring.ProdottoNote</class>
		<class>com.yr.entity.localstring.ProdottoRemoteControl</class>
		<class>com.yr.entity.localstring.ProdottoElectricalRemoteControl</class>
		<class>com.yr.entity.localstring.ProdottoShortName</class>
		<class>com.yr.entity.localstring.ProdottoTransformer</class>
		<class>com.yr.entity.localstring.ProdottoElectricalTransformer</class>
		<class>com.yr.entity.localstring.SubfamilyConfigText</class>
		<class>com.yr.entity.localstring.SubfamilyCutoutShape</class>
		<class>com.yr.entity.localstring.SubfamilyEmission</class>
		<class>com.yr.entity.localstring.SubfamilyEnvironment</class>
		<class>com.yr.entity.localstring.SubfamilyName</class>
		<class>com.yr.entity.localstring.VarianteDescription</class>

  		<class>com.yr.entity.Articolo</class>
		<class>com.yr.entity.CodiceProdotto</class>
		<class>com.yr.entity.Country</class>
		<class>com.yr.entity.CountryProdottoList</class>
		<class>com.yr.entity.CurvaFotometrica</class>
		<class>com.yr.entity.Designer</class>
		<class>com.yr.entity.Etichetta</class>
		<class>com.yr.entity.Evento</class>
		<class>com.yr.entity.EventoNews</class>
		<class>com.yr.entity.Famiglia</class>
		<class>com.yr.entity.FilterVariante</class>
		<class>com.yr.entity.localstring.FilterVarianteDescription</class>
		<class>com.yr.entity.GalleryDescription</class>
		<!-- 
		<class>com.yr.entity.GalleryNews</class> 
		<class>com.yr.entity.HomePage</class>
		<class>com.yr.entity.HomePageGallery</class>
		-->
		<class>com.yr.entity.Lampadina</class>
		<class>com.yr.entity.Led</class>
		<class>com.yr.entity.News</class>
		<class>com.yr.entity.PrezzoSap</class>
		<class>com.yr.entity.Prodotto</class>
		<class>com.yr.entity.ProdottoPdfTimestamp</class>
		<class>com.yr.entity.ProdottoElectrical</class>
		<class>com.yr.entity.Publications</class>
		<class>com.yr.entity.Responsible</class>
		<class>com.yr.entity.Sellability</class>
		<class>com.yr.entity.Store</class>
		<class>com.yr.entity.Subfamily</class>
		<class>com.yr.entity.VarianteDescriptionInSubfamily</class>
  		<class>com.yr.babka37.entity.Address</class>
		<class>com.yr.babka37.entity.CloneableDeep</class>
		<class>com.yr.babka37.entity.CloneableFiltered</class>
		<class>com.yr.babka37.entity.LocalEntity</class>
		<class>com.yr.babka37.entity.LocalString</class>
		<class>com.yr.babka37.entity.Property</class>
		<class>com.yr.babka37.entity.SortedUploadedFiles</class>
		<class>com.yr.babka37.entity.UploadedAttributes</class>
		<class>com.yr.babka37.entity.UploadedFile</class>
		<class>com.yr.babka37.entity.localstring.UploadedFileDescription</class>
		<class>com.yr.babka37.entity.localstring.UploadedFileTitle</class>
      <properties>
      		<!-- 
		     <property name="jakarta.persistence.schema-generation.database.action" value="none" />
		     <property name="jakarta.persistence.schema-generation.scripts.action" value="drop-and-create" />
		     <property name="jakarta.persistence.schema-generation.create-database-schemas" value="true" />
		     <property name="jakarta.persistence.schema-generation.create-source" value="metadata"/>
             <property name="jakarta.persistence.schema-generation.drop-source" value="metadata"/>
		     <property name="jakarta.persistence.schema-generation.scripts.create-target" value="schema/amds.sql" />
		     <property name="jakarta.persistence.schema-generation.scripts.drop-target" value="schema/amds_drop.sql" />
		     <property name="jakarta.persistence.schema-generation.connection" value="***************************************************************************************************"/>
      		 -->

            <property name="jakarta.persistence.jdbc.driver" value="com.mysql.cj.jdbc.Driver"/>
            <property name="jakarta.persistence.jdbc.user" value="amduserdev"/>
            <property name="jakarta.persistence.jdbc.password" value="qweqwe"/>
            <property name="jakarta.persistence.jdbc.url" value="**************************************************************************************************************************************************"/>
         	<property name="jakarta.persistence.schema-generation.scripts.action" value="create"/>
         	<property name="jakarta.persistence.schema-generation.scripts.create-target" value="schema/amd.sql"/>
         	<property name="jakarta.persistence.schema-generation.create-source" value="metadata"/>
      </properties>
   		 
   </persistence-unit>
</persistence>