<?xml version="1.0" encoding="UTF-8" ?>
<config>
	<info>
		<env>dev</env>
		<appName>ArtemideSite</appName>
		<version>1.0</version>
		<build>${build}</build> <!-- TODO iniettare tramite gradle -->
		<date>@@RELEASEDATE_PLACEHOLDER@@</date> <!-- Injected by gradle -->
	</info>
	<paths>
		<basePath>/srv/amddev</basePath> 
		<!-- path per le pagine esterne - where to find the external "static" html files -->
		<!-- Make a junction with something like mklink /D C:\srv\amddev\external C:\work\git-artemide-internal\Responsive\ArtemideSiteExternal\external -->
		<!-- Il folder "external" serve solo per sapere se una pagina localizzata esiste, perché se non esiste viene presa la versione inglese. -->
		<externalViewPath>/srv/amddev/external</externalViewPath>
		<serverAddress>http://amdtest.it:8080</serverAddress>
		<instructionsBasepath>/srv/amddev/importExport/productInstructions</instructionsBasepath>
		<!-- path to import product files -->
		<basepathImportFiles>/srv/amddev/importExport/productFiles</basepathImportFiles>
		<configuratorRegressionTest>
			<enabled>true</enabled>
		</configuratorRegressionTest>
	</paths>

	<!-- LEGACY SECTION: for new values use paths instead -->
	<resources>
		<!-- Path relativo ai documenti -->
		<basepathdocs>/srv/amddev/contents/documenti</basepathdocs>
		<!-- Path dove il sito salva i pdf -->
		<basepathpdf>/srv/amddev/contents/pdfcache</basepathpdf>
		<!-- Path dove il sito salva i pdf creati dagli utenti -->
		<basepathMyPdf>/srv/amddev/contents/mypdf</basepathMyPdf>
		<!-- path per le etichette -->
		<energyLabelPath>/srv/amddev/energylabels</energyLabelPath>
	</resources>

	<application name="sito" version="v4.1-dev">
		<promotion>
			<netflix>
				<!-- ISO format: yyyy-mm-dd -->
				<showFrom time="00:01">2024-06-17</showFrom>
				<showTo time="23:59">2026-06-17</showTo>
			</netflix>
		</promotion>
	</application>

	<configurator version="v0.7" beta="true">
		<a24v1>false</a24v1>
	</configurator>

	<cache>
		<!-- When the cache is disabled, it could still be enabled via dashboard -->
		<subfamily enabled="true">
			<!-- Numero massimo di prodotti (righe di subfamily) messi in cache.
				Un valore troppo basso vanifica la cache.
				Un valore troppo alto causa OutOfMemory.
				In tutto il DB ci sono 15k prodotti in 900 sottofamiglie ma la metà non sono pubblicati.
				Le sottofamiglie con più di 50 prodotti sono 60 e raccolgono 8k prodotti.
				ATTENZIONE: il criterio con cui eliminare una sottofamiglia dalla cache è indipendente dalla
				sua grandezza ma considera solo età e utilizzo.
			-->
			<!-- 47653 elementi occupano circa 744MB -->
			<maxweight>40000</maxweight>
			<!-- Dopo 60 minuti un'entry diventa stale e ricaricata alla successiva richiesta -->
			<staleAfterSeconds>3600</staleAfterSeconds>
		</subfamily>
	</cache>
		
	<rest>
		<pivotal>
			<!-- Questa è la versione di test -->
			<!-- Per disabilitare la connessione cancellare l'utente -->
			<url>https://crm.artemide.com/TPVTApi/lead/</url>
			<user>artemide</user>
			<password>344eUkRT9g8w</password>
		</pivotal>
	</rest>

	<email>
		<enabled>true</enabled>
		<from><EMAIL></from>
		<publications>
			<notifyEdits>
				<enabled>false</enabled>
			</notifyEdits>
		</publications>
		<ideas> 
			<to><EMAIL></to>
		</ideas>
		<contactUs>
			<!-- Email di default quando nel database non esiste una mail per il country selezionato dall'utente.
			Usato anche per il configuratore -->
			<default><EMAIL></default>
			<!-- Per le richieste "Replacement request" e "Technical assistance request" -->
			<italianRequest><EMAIL></italianRequest>
			<globalRequest><EMAIL></globalRequest>
			<!-- Email usata per le richieste di progetto illuminotecnico -->
			<lightingProject><EMAIL></lightingProject>
		</contactUs>
		<!-- Addresses to which a support request is sent -->
		<!-- 
		<support>
			<to><EMAIL></to>
		</support>
		 -->
		<!-- Remove this list to enable email to everyone -->
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<validEmail><EMAIL></validEmail>
		<!-- Email patterns of invalid emails (rejected on registration) -->
		<blacklistPattern>.*invalid.*</blacklistPattern>
		<blacklistPattern>.*@mailinator.com</blacklistPattern>
	</email>

	<setup>
		<users>
			<user>
				<email><EMAIL></email>
				<password><EMAIL></password>
				<locale>it_IT</locale>
				<timezone>Europe/Rome</timezone>
				<role>USER</role>
			</user>
			<user>
				<email><EMAIL></email>
				<password><EMAIL></password>
				<locale>it_IT</locale>
				<timezone>Europe/Rome</timezone>
				<role>USER</role>
				<role>MANAGER</role>
			</user>
			<user>
				<email><EMAIL></email>
				<password><EMAIL></password>
				<locale>it_IT</locale>
				<timezone>Europe/Rome</timezone>
				<role>USER</role>
				<role>SALESFORCE</role>
			</user>
			<user>
				<email><EMAIL></email>
				<password><EMAIL></password>
				<locale>it_IT</locale>
				<timezone>Europe/Rome</timezone>
				<role>USER</role>
				<role>DEVELOPER</role>
			</user>
			<user>
				<email><EMAIL></email>
				<password><EMAIL>@example.com</password>
				<locale>it_IT</locale>
				<timezone>Europe/Rome</timezone>
				<role>USER</role>
				<role>ADMIN</role>
			</user>
		</users>
	</setup>

	<database>
		<jndiname>java:comp/env/jdbc/amddev</jndiname>
		<showSql>false</showSql> <!-- This can conveniently be changed in conf.webapp.localdev.xml -->
	</database>
	
	<shell>
		<risolutore>
			<executable>${config/paths/basePath}/bin/risolutore-funi/bin/runRisolutore.bat</executable>
		</risolutore>
		<zipWithRename>
			<executable>${config/paths/basePath}/bin/zipRename.bat</executable>
			<arg>${ZIPFILE}</arg>
			<arg>${ZIPNOTEFILE}</arg>
			<arg>${FILES}</arg>
		</zipWithRename>
		<headerLabelPdf>
			<executable>${config/paths/basePath}/bin/addPdfHeader.bat</executable>
			<arg>${TEXT}</arg>
			<arg>${FILENAME}</arg>
		</headerLabelPdf>
		<zipPdfProdotti>
			<executable>${config/paths/basePath}/bin/zipFolder.bat</executable>
			<arg>${ZIPFILENAME}</arg>
			<arg>${FOLDERTOZIP}</arg>
		</zipPdfProdotti>
		<pdf-chrome>
			<executable>/usr/bin/node</executable>
			<executable windows="true">node</executable>
			<arg>${BASEPATH}/bin/puppeteer/makepdf.js</arg>
			<arg>${URL}</arg>
			<arg>${URLPDFFOOTER}</arg>
			<arg>50</arg>
			<arg>50</arg>
			<arg>71</arg>
			<arg>50</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-chrome>
		<convert>
			<executable windows="true">magick</executable>
			<executable mac="true" linux="true">/usr/local/bin/magick</executable>
			<arg>convert</arg>
			<arg>-auto-orient</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>${FILENAMEOUT}</arg>
		</convert>
		<resize>
			<executable windows="true">magick</executable>
			<executable mac="true" linux="true">/usr/local/bin/magick</executable>
			<arg>convert</arg>
			<arg>-auto-orient</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-resize</arg>
			<arg><![CDATA[${W}x${H}]]></arg>
			<arg>${FILENAMEOUT}</arg>
		</resize>
		<dxf-export>
			<executable>${config/paths/basePath}\bin\dxfExporter\DxfExporter.exe</executable>
            <arg>\srv\amddev\contents\configurator</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>${FILENAMEOUT}</arg>
		</dxf-export>
		<blender-conversion> <!-- To be deleted -->
			<executable>${config/paths/basePath}\bin\blenderConvert.bat</executable>
			<arg>${FILENAMEIN}</arg>
			<arg>${FILENAMEOUT}</arg>
		</blender-conversion>
		<!-- Comando che ritaglia un'immagine a XxY, eventualmente ingrandendola -->
		<!-- convert image.jpg -thumbnail "200x200^" -gravity center -extent "200x200" image.jpg -->
		<!-- extent è come crop ma se l'immagine è piccola aggiunge i bordi. Nel mio caso non serve per via del "^" credo -->
		<!-- OLD: convert image.jpg -resize "200x200^" -crop "200x200+0+0" image.jpg -->
		<!-- {FILENAMEIN} viene rimpiazzato col nome del file di input -->
		<!-- {FILENAMEOUT} viene rimpiazzato col nome del file di output -->
		<!-- {DIMENSIONS} viene rimpiazzato con le dimensioni volute, per esempio 200x200 -->
		<!--  -->
		<pdf-configurator timeoutseconds="300"> <!-- Cinque minuti di timeout -->
			<executable>C:\Program Files\wkhtmltopdf-01251\bin\wkhtmltopdf.exe</executable>
			<arg>-q</arg>
			<arg>--no-outline</arg>
			<arg>--footer-html</arg>
			<arg>${URLPDFFOOTER}</arg>
			<arg>${URL}</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-configurator>
		<pdf-scheda>
			<executable mac="true" linux="true">/usr/local/bin/wkhtmltopdf</executable> <!-- Renat, change as needed -->
			<executable>C:\Program Files\wkhtmltopdf-01251\bin\wkhtmltopdf.exe</executable>
			<arg>-q</arg>
			<arg>--no-outline</arg>
			<arg>${URL}</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-scheda>

		<deleteSchedaPdf> <!-- Cancella il pdf quando cambiano journal o project -->
			<executable windows="true">${config/paths/basePath}/bin/deleteSchedaPdf.bat</executable>
			<executable mac="true" linux="true">${config/paths/basePath}/bin/deleteSchedaPdf.sh</executable>
			<arg>${FILENAME}</arg>
		</deleteSchedaPdf>
	
		<!-- Comando che toglie il bordo bianco e scala l'immagine lasciando libera la larghezza -->
		<!-- convert image.jpg -trim -resize x210 image.jpg -->
		<!-- ${FILENAMEIN} viene rimpiazzato col nome del file di input -->
		<!-- ${FILENAMEOUT} viene rimpiazzato col nome del file di output -->
		<!-- ${HEIGHT} viene rimpiazzato col l'altezza voluta -->
		<!--  -->
		<!-- Versione "fake" per chi non ha installato Imagemagick -->
		<!-- 
		<processTunableWhiteImage>
			<executable>cmd</executable>
			<arg>/C</arg>
			<arg>echo</arg>
			<arg>fakeConvert</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-trim</arg>
			<arg>-resize</arg>
			<arg>x${HEIGHT}</arg>
			<arg>${FILENAMEOUT}</arg>
		</processTunableWhiteImage>
		 -->
		<!-- Versione vera per windows (not used anymore)
		<processTunableWhiteImage>
			<executable>C:\Program Files\ImageMagick\convert.exe</executable>
			<arg>${FILENAMEIN}</arg>
			<arg>-trim</arg>
			<arg>-resize</arg>
			<arg>x${HEIGHT}</arg>
			<arg>${FILENAMEOUT}</arg>
		</processTunableWhiteImage>
		 -->
		<!-- Comando che toglie il bordo bianco e scala l'immagine lasciando libera la larghezza -->
		<!-- convert image.jpg -trim -resize x210 image.jpg -->
		<!-- ${FILENAMEIN} viene rimpiazzato col nome del file di input -->
		<!-- ${FILENAMEOUT} viene rimpiazzato col nome del file di output -->
		<!-- ${HEIGHT} viene rimpiazzato col l'altezza voluta -->
		<!--  -->
		<!-- Versione "fake" per chi non ha installato Imagemagick -->
		<!-- 
		<processCurvaFotometrica>
			<executable>cmd</executable>
			<arg>/C</arg>
			<arg>echo</arg>
			<arg>fakeConvert</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-trim</arg>
			<arg>-resize</arg>
			<arg>x${HEIGHT}</arg>
			<arg>${FILENAMEOUT}</arg>
		</processCurvaFotometrica>
		<processCurvaFotometrica>
			<executable>C:\Program Files\ImageMagick\convert.exe</executable>
			<arg>-auto-orient</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-trim</arg>
			<arg>-resize</arg>
			<arg>x${HEIGHT}</arg>
			<arg>${FILENAMEOUT}</arg>
		</processCurvaFotometrica>
		 -->
		<pdf-etichette>
			<executable>C:/srv/amddev/bin/cpdf.exe</executable>
			<arg>-add-text</arg>
			<arg>${TEXT}</arg>
			<arg>-pos-left</arg>
			<arg>"${TEXTPOS}"</arg>
			<arg>-font</arg>
			<arg>Arial</arg>
			<arg>-font-size</arg>
			<arg>7</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-o</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-etichette>
		<pdf-favourites-merge>
			<executable>C:/srv/amddev/bin/cpdf.exe</executable>
			<arg>${FILENAMEIN}</arg>
			<arg>-o</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-favourites-merge>
		<pdf-favourites-cover>
			<executable>C:/srv/amddev/bin/cpdf.exe</executable>
			<arg>-utf8</arg>
			<arg>-add-text</arg>
			<arg>${TITLESUBTITLE}</arg>
			<arg>-pos-left</arg>
			<arg>"45 97"</arg>
			<arg>-line-spacing</arg>
			<arg>1.3</arg>
			<arg>-font</arg>
			<arg>Helvetica</arg>
			<arg>-color</arg>
			<arg>0.23 0.23 0.23</arg> <!-- #e3 (227) #25 (37) #1b (27) -->
			<arg>-font-size</arg>
			<arg>40</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-o</arg>
			<arg>${FILENAMEOUT}</arg>
		</pdf-favourites-cover>
		<!-- Comando che ritaglia un'immagine specificando esattamente la posizione e la dimensione del ritaglio, e poi riducendo alla dimensione finale. -->
		<yadaCropAndResize timeoutseconds="120">
			<executable windows="true">magick</executable>
			<executable mac="true" linux="true">/usr/local/bin/magick</executable>
			<arg>convert</arg>
			<arg>-auto-orient</arg>
			<arg>${FILENAMEIN}</arg>
			<arg>-coalesce</arg>
			<arg>-repage</arg>
			<arg>0x0</arg>
			<arg>-crop</arg>
			<arg>${w}x${h}+${x}+${y}</arg>
			<arg>-resize</arg>
			<arg>${resizew}x${resizeh}&gt;</arg>
			<arg>+repage</arg>
			<arg>${FILENAMEOUT}</arg>
		</yadaCropAndResize>
		
	</shell>	
</config>