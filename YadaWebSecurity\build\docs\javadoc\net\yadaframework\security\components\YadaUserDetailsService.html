<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaUserDetailsService (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.security.components, class: YadaUserDetailsService">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.security.components</a></div>
<h1 title="Class YadaUserDetailsService" class="title">Class YadaUserDetailsService</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.security.components.YadaUserDetailsService</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code>org.springframework.security.core.userdetails.UserDetailsService</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="annotations">@Component
@DependsOn("passwordEncoder")
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaUserDetailsService</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements org.springframework.security.core.userdetails.UserDetailsService</span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaUserDetailsService</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>org.springframework.security.core.Authentication</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">authenticateAs</a><wbr>(<a href="../persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a>&nbsp;userCredentials)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">because for Spring 5</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>org.springframework.security.core.Authentication</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,boolean)" class="member-name-link">authenticateAs</a><wbr>(<a href="../persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a>&nbsp;userCredentials,
 boolean&nbsp;setTimestamp)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">because for Spring 5 we need Request and Response</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.springframework.security.core.Authentication</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,boolean,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">authenticateAs</a><wbr>(<a href="../persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a>&nbsp;userCredentials,
 boolean&nbsp;setTimestamp,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Manual authentication for Spring Security 6.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.springframework.security.core.Authentication</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">authenticateAs</a><wbr>(<a href="../persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a>&nbsp;userCredentials,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Manual authentication for Spring Security 6 without setting the login timestamp.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#changeCurrentRoles(org.springframework.security.core.Authentication,int%5B%5D)" class="member-name-link">changeCurrentRoles</a><wbr>(org.springframework.security.core.Authentication&nbsp;authentication,
 int[]&nbsp;roleIds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Change the roles of the currently authenticated user, but not on the database</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#changePasswordIfAuthenticated(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">changePasswordIfAuthenticated</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;username,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;passwordTyped,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newPassword)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Change the old password with the new password, but only if the old password is valid.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.springframework.security.core.userdetails.UserDetails</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadUserByUsername(java.lang.String)" class="member-name-link">loadUserByUsername</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;username)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#passwordMatch(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">passwordMatch</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;passwordTyped,
 <a href="../persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a>&nbsp;yadaUserCredentials)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if some string matches the user password</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#validatePasswordSyntax(java.lang.String)" class="member-name-link">validatePasswordSyntax</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;password)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#validatePasswordSyntax(java.lang.String,int,int)" class="member-name-link">validatePasswordSyntax</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;password,
 int&nbsp;minLen,
 int&nbsp;maxLen)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaUserDetailsService</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaUserDetailsService</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="changeCurrentRoles(org.springframework.security.core.Authentication,int[])">
<h3>changeCurrentRoles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">changeCurrentRoles</span><wbr><span class="parameters">(org.springframework.security.core.Authentication&nbsp;authentication,
 int[]&nbsp;roleIds)</span></div>
<div class="block">Change the roles of the currently authenticated user, but not on the database</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>authentication</code> - the current Authentication object</dd>
<dd><code>roleIds</code> - the database ids of the needed roles</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loadUserByUsername(java.lang.String)">
<h3>loadUserByUsername</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.security.core.userdetails.UserDetails</span>&nbsp;<span class="element-name">loadUserByUsername</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;username)</span>
                                                                             throws <span class="exceptions">org.springframework.security.core.userdetails.UsernameNotFoundException,
<a href="../exceptions/InternalAuthenticationException.html" title="class in net.yadaframework.security.exceptions">InternalAuthenticationException</a>,
<a href="../TooManyFailedAttemptsException.html" title="class in net.yadaframework.security">TooManyFailedAttemptsException</a></span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>loadUserByUsername</code>&nbsp;in interface&nbsp;<code>org.springframework.security.core.userdetails.UserDetailsService</code></dd>
<dt>Throws:</dt>
<dd><code>org.springframework.security.core.userdetails.UsernameNotFoundException</code></dd>
<dd><code><a href="../exceptions/InternalAuthenticationException.html" title="class in net.yadaframework.security.exceptions">InternalAuthenticationException</a></code></dd>
<dd><code><a href="../TooManyFailedAttemptsException.html" title="class in net.yadaframework.security">TooManyFailedAttemptsException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)">
<h3>authenticateAs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.security.core.Authentication</span>&nbsp;<span class="element-name">authenticateAs</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a>&nbsp;userCredentials,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span></div>
<div class="block">Manual authentication for Spring Security 6 without setting the login timestamp.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userCredentials</code> - </dd>
<dd><code>request</code> - </dd>
<dd><code>response</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,boolean,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)">
<h3>authenticateAs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.security.core.Authentication</span>&nbsp;<span class="element-name">authenticateAs</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a>&nbsp;userCredentials,
 boolean&nbsp;setTimestamp,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span></div>
<div class="block">Manual authentication for Spring Security 6. Also sets the login timestamp and clears the failed attempts counter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userCredentials</code> - </dd>
<dd><code>setTimestamp</code> - true to set the lastSuccessfulLogin timestamp</dd>
<dd><code>request</code> - </dd>
<dd><code>response</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials)">
<h3>authenticateAs</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.security.core.Authentication</span>&nbsp;<span class="element-name">authenticateAs</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a>&nbsp;userCredentials)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">because for Spring 5</div>
</div>
<div class="block">Authenticate the user without setting the lastSuccessfulLogin timestamp</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userCredentials</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,boolean)">
<h3>authenticateAs</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.security.core.Authentication</span>&nbsp;<span class="element-name">authenticateAs</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a>&nbsp;userCredentials,
 boolean&nbsp;setTimestamp)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">because for Spring 5 we need Request and Response</div>
</div>
<div class="block">Authenticate the user</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userCredentials</code> - </dd>
<dd><code>setTimestamp</code> - true to set the lastSuccessfulLogin timestamp</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,boolean,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"><code>authenticateAs(YadaUserCredentials, boolean, HttpServletRequest, HttpServletResponse)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="changePasswordIfAuthenticated(java.lang.String,java.lang.String,java.lang.String)">
<h3>changePasswordIfAuthenticated</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">changePasswordIfAuthenticated</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;username,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;passwordTyped,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newPassword)</span>
                                   throws <span class="exceptions">org.springframework.security.core.userdetails.UsernameNotFoundException,
<a href="../exceptions/InternalAuthenticationException.html" title="class in net.yadaframework.security.exceptions">InternalAuthenticationException</a>,
org.springframework.security.authentication.BadCredentialsException</span></div>
<div class="block">Change the old password with the new password, but only if the old password is valid.
 If the change is successful, no exception is thrown.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>username</code> - user that needs to change password</dd>
<dd><code>passwordTyped</code> - current password</dd>
<dd><code>newPassword</code> - new password</dd>
<dt>Throws:</dt>
<dd><code>org.springframework.security.core.userdetails.UsernameNotFoundException</code> - if the username does not exist</dd>
<dd><code>org.springframework.security.authentication.BadCredentialsException</code> - if the supplied password is not valid for the user</dd>
<dd><code><a href="../exceptions/InternalAuthenticationException.html" title="class in net.yadaframework.security.exceptions">InternalAuthenticationException</a></code> - in any other error occurs</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="passwordMatch(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials)">
<h3>passwordMatch</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">passwordMatch</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;passwordTyped,
 <a href="../persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a>&nbsp;yadaUserCredentials)</span></div>
<div class="block">Check if some string matches the user password</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>passwordTyped</code> - the string to check</dd>
<dd><code>yadaUserCredentials</code> - credentials of the user</dd>
<dt>Returns:</dt>
<dd>true if passwordTyped is the user password</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="validatePasswordSyntax(java.lang.String,int,int)">
<h3>validatePasswordSyntax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">validatePasswordSyntax</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;password,
 int&nbsp;minLen,
 int&nbsp;maxLen)</span></div>
</section>
</li>
<li>
<section class="detail" id="validatePasswordSyntax(java.lang.String)">
<h3>validatePasswordSyntax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">validatePasswordSyntax</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;password)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
