package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.Lampadina;

@Repository
@Transactional(readOnly = true)
public class LampadinaDao {

    @PersistenceContext EntityManager em;

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Lampadina", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Lampadina save(Lampadina entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Lampadina> findById(Long entityId) {
        Lampadina result = em.find(Lampadina.class, entityId);
        return Optional.ofNullable(result);
    }

    public Lampadina findOne(Long entityId) {
        return em.find(Lampadina.class, entityId);
    }

    public List<Lampadina> findAll() {
        return em.createQuery("from Lampadina", Lampadina.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Lampadina> batchToSave) {
        for (Lampadina entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Lampadina entity) {
        em.remove(entity);
    }
}
