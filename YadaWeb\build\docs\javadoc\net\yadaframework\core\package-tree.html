<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.core Class Hierarchy (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="tree: package: net.yadaframework.core">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package net.yadaframework.core</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.springframework.web.context.AbstractContextLoaderInitializer (implements org.springframework.web.WebApplicationInitializer)
<ul>
<li class="circle">org.springframework.web.servlet.support.AbstractDispatcherServletInitializer
<ul>
<li class="circle">org.springframework.web.servlet.support.AbstractAnnotationConfigDispatcherServletInitializer
<ul>
<li class="circle">net.yadaframework.core.<a href="YadaWebApplicationInitializer.html" class="type-name-link" title="class in net.yadaframework.core">YadaWebApplicationInitializer</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.thymeleaf.linkbuilder.AbstractLinkBuilder (implements org.thymeleaf.linkbuilder.ILinkBuilder)
<ul>
<li class="circle">org.thymeleaf.linkbuilder.StandardLinkBuilder
<ul>
<li class="circle">net.yadaframework.core.<a href="YadaLinkBuilder.html" class="type-name-link" title="class in net.yadaframework.core">YadaLinkBuilder</a></li>
<li class="circle">net.yadaframework.core.<a href="YadaLocalePathLinkBuilder.html" class="type-name-link" title="class in net.yadaframework.core">YadaLocalePathLinkBuilder</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">net.yadaframework.core.<a href="YadaAjaxInterceptor.html" class="type-name-link" title="class in net.yadaframework.core">YadaAjaxInterceptor</a> (implements org.springframework.web.servlet.HandlerInterceptor)</li>
<li class="circle">net.yadaframework.core.<a href="YadaAppConfig.html" class="type-name-link" title="class in net.yadaframework.core">YadaAppConfig</a></li>
<li class="circle">net.yadaframework.core.<a href="YadaConfiguration.html" class="type-name-link" title="class in net.yadaframework.core">YadaConfiguration</a></li>
<li class="circle">net.yadaframework.core.<a href="YadaDummyDatasource.html" class="type-name-link" title="class in net.yadaframework.core">YadaDummyDatasource</a> (implements javax.sql.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.sql/javax/sql/DataSource.html" title="class or interface in javax.sql" class="external-link">DataSource</a>)</li>
<li class="circle">net.yadaframework.core.<a href="YadaDummyEntityManagerFactory.html" class="type-name-link" title="class in net.yadaframework.core">YadaDummyEntityManagerFactory</a> (implements jakarta.persistence.EntityManagerFactory)</li>
<li class="circle">net.yadaframework.core.<a href="YadaDummyJpaConfig.html" class="type-name-link" title="class in net.yadaframework.core">YadaDummyJpaConfig</a></li>
<li class="circle">net.yadaframework.core.<a href="YadaFluentBase.html" class="type-name-link" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;T&gt;</li>
<li class="circle">net.yadaframework.core.<a href="YadaJpaConfig.html" class="type-name-link" title="class in net.yadaframework.core">YadaJpaConfig</a></li>
<li class="circle">net.yadaframework.core.<a href="YadaLocalePathChangeInterceptor.html" class="type-name-link" title="class in net.yadaframework.core">YadaLocalePathChangeInterceptor</a> (implements org.springframework.web.servlet.HandlerInterceptor)</li>
<li class="circle">net.yadaframework.core.<a href="YadaTomcatServer.html" class="type-name-link" title="class in net.yadaframework.core">YadaTomcatServer</a></li>
<li class="circle">net.yadaframework.core.<a href="YadaWebConfig.html" class="type-name-link" title="class in net.yadaframework.core">YadaWebConfig</a> (implements org.springframework.web.servlet.config.annotation.WebMvcConfigurer)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">net.yadaframework.core.<a href="CloneableFiltered.html" class="type-name-link" title="interface in net.yadaframework.core">CloneableFiltered</a>
<ul>
<li class="circle">net.yadaframework.core.<a href="CloneableDeep.html" class="type-name-link" title="interface in net.yadaframework.core">CloneableDeep</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.core.<a href="YadaConstants.html" class="type-name-link" title="interface in net.yadaframework.core">YadaConstants</a></li>
<li class="circle">net.yadaframework.core.<a href="YadaLocalEnum.html" class="type-name-link" title="interface in net.yadaframework.core">YadaLocalEnum</a>&lt;E&gt;</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="class or interface in java.lang">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/constant/Constable.html" title="class or interface in java.lang.constant" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">net.yadaframework.core.<a href="YadaRegistrationType.html" class="type-name-link" title="enum class in net.yadaframework.core">YadaRegistrationType</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
