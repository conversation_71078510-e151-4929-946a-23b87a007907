<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.components (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.components">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li>Related Packages</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li>Related Packages&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package net.yadaframework.components" class="title">Package net.yadaframework.components</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">net.yadaframework.components</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button><button id="class-summary-tab6" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab6', 2)" class="table-tab">Annotation Interfaces</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaAttachedFileCloneSet.html" title="class in net.yadaframework.components">YadaAttachedFileCloneSet</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Used when cloning some object that contains instances of YadaAttachedFile at any level.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab6"><a href="YadaCopyNot.html" title="annotation interface in net.yadaframework.components">YadaCopyNot</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab6">
<div class="block">Fields marked by this annotation are not copied at all when cloning with YadaUtil, and the value is not initialized (e.g.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab6"><a href="YadaCopyShallow.html" title="annotation interface in net.yadaframework.components">YadaCopyShallow</a></div>
<div class="col-last even-row-color class-summary class-summary-tab6">
<div class="block">Fields marked by this annotation are not recursively copied when cloning with YadaUtil, but copied by value,
 unless their value has been cloned in an ancestor object, in which case the cloned version will be used as a value.<br>
 <br>
 For example, if ProjectA contains ModuleA that has a project field referring to ProjectA with this annotation, then when
 cloning ModuleA the new ModuleB will keep a reference to ProjectA (shallow copy); when
 cloning ProjectA (the parent of ModuleA) the new ProjectB will contain a ModuleB with a reference to ProjectB (not
 to ProjectA because a clone of it has been made already).<br>
 <br>
 If this annotation is not used, it makes no difference for the latter case because ModuleB would get a reference to ProjectB
 that has already been cloned (there's a check to prevent infinite loops).</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaDataTableFactory.html" title="class in net.yadaframework.components">YadaDataTableFactory</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Returns YadaDataTable instances, either creating new ones or reusing existing ones.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaDateFormatter.html" title="class in net.yadaframework.components">YadaDateFormatter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">Deprecated.</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaEmailBuilder.html" title="class in net.yadaframework.components">YadaEmailBuilder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Api for building and sending emails using Thymeleaf templates.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaEmailService.html" title="class in net.yadaframework.components">YadaEmailService</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaFileManager.html" title="class in net.yadaframework.components">YadaFileManager</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The File Manager handles uploaded files.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaFormHelper.html" title="class in net.yadaframework.components">YadaFormHelper</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Utility methods to be used in thymeleaf forms</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaJobManager.html" title="class in net.yadaframework.components">YadaJobManager</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Job handling.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaJsonMapper.html" title="class in net.yadaframework.components">YadaJsonMapper</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaKeyRateLimiter.html" title="class in net.yadaframework.components">YadaKeyRateLimiter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A rate limiter by key.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaLocalePathVariableFilter.html" title="class in net.yadaframework.components">YadaLocalePathVariableFilter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Locale in the path
 See https://stackoverflow.com/a/23847484/587641
 This must be added to the WebApplicationInitializer.getServletFilters() method in each webapp (see yada docs)</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaLongRunningExclusive.html" title="class in net.yadaframework.components">YadaLongRunningExclusive</a>&lt;T&gt;</div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Class that performs a long running operation that should not be invoked again before it completes.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaMariaDB.html" title="class in net.yadaframework.components">YadaMariaDB</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Allows reopening of an existing MariaDB data folder</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaMariaDBServer.html" title="class in net.yadaframework.components">YadaMariaDBServer</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Build a notification dialog to be shown on the response page or after a redirect.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaSecurityUtilStub.html" title="class in net.yadaframework.components">YadaSecurityUtilStub</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">SecurityUtils methods that are used in YadaWeb only when YadaWebSecurity is present.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaSetup.html" title="class in net.yadaframework.components">YadaSetup</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaSimpleRateLimiter.html" title="class in net.yadaframework.components">YadaSimpleRateLimiter</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A simple rate limiter that just counts and resets at intervals.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaSleepingRateLimiter.html" title="class in net.yadaframework.components">YadaSleepingRateLimiter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A rate limiter that sleeps to distribute calls in a time interval.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaUtil.html" title="class in net.yadaframework.components">YadaUtil</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaWebUtil.html" title="class in net.yadaframework.components">YadaWebUtil</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Utility methods to be used for web development</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
