drop table YadaUserProfile_Famiglia;
drop table YadaUserProfile_Prodotto;
drop table YadaUserProfile_Project;
drop table YadaUserProfile_Subfamily;

create table UserProfile_favouriteFamilies (UserProfile_id bigint not null, favouriteFamilies bigint) engine=InnoDB;
create table UserProfile_favouriteProducts (UserProfile_id bigint not null, favouriteProducts bigint) engine=InnoDB;
create table UserProfile_favouriteProjects (UserProfile_id bigint not null, favouriteProjects bigint) engine=InnoDB;
create table UserProfile_favouriteSubfamilies (UserProfile_id bigint not null, favouriteSubfamilies bigint) engine=InnoDB;

alter table UserProfile_favouriteFamilies add constraint FKkech94kinruid1jy8cnu3ljb0 foreign key (UserProfile_id) references YadaUserProfile (id);
alter table UserProfile_favouriteProducts add constraint FKr4il7s2f2432qyl6lviql6m03 foreign key (UserProfile_id) references YadaUserProfile (id);
alter table UserProfile_favouriteProjects add constraint FKjtu1aiq3fc4sfj9tn1co8kqb1 foreign key (UserProfile_id) references YadaUserProfile (id);
alter table UserProfile_favouriteSubfamilies add constraint FKirbno55yq5w3imncd2tai2fy8 foreign key (UserProfile_id) references YadaUserProfile (id);
