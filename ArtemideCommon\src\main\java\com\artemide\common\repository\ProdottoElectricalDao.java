package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.ProdottoElectrical;

@Repository
@Transactional(readOnly = true)
public class ProdottoElectricalDao {

    @PersistenceContext EntityManager em;

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from ProdottoElectrical", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public ProdottoElectrical save(ProdottoElectrical entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<ProdottoElectrical> findById(Long entityId) {
        ProdottoElectrical result = em.find(ProdottoElectrical.class, entityId);
        return Optional.ofNullable(result);
    }

    public ProdottoElectrical findOne(Long entityId) {
        return em.find(ProdottoElectrical.class, entityId);
    }

    public List<ProdottoElectrical> findAll() {
        return em.createQuery("from ProdottoElectrical", ProdottoElectrical.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<ProdottoElectrical> batchToSave) {
        for (ProdottoElectrical entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(ProdottoElectrical entity) {
        em.remove(entity);
    }
}
