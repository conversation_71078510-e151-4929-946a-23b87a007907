package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.repository.DesignerRepoDao;
import com.artemide.common.repository.DesignerRepository;
import com.yr.entity.Designer;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies DesignerRepoDao behaves exactly like DesignerRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class DesignerRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private DesignerRepository designerRepository;

    @Autowired
    private DesignerRepoDao designerRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    Designer entity = entityManager.find(Designer.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            entityManager.createQuery("DELETE FROM Designer d WHERE d.surname LIKE 'TEST_%' OR d.surname LIKE 'INTEGRATION_%'")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private Designer createTestEntity(String surname, String name) {
        Designer entity = new Designer();
        entity.setSurname(surname);
        entity.setName(name);
        return entity;
    }

    private void trackEntity(Designer entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = designerRepository.count();
            long initialDaoCount = designerRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            Designer testEntity = createTestEntity("INTEGRATION_SAVE_001", "Test");

            Designer repoSaved = designerRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            Designer testEntity2 = createTestEntity("INTEGRATION_SAVE_002", "Test2");
            Designer daoSaved = designerRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }
            if (!testEntity.getSurname().equals(repoSaved.getSurname())) {
                return "FAIL: Repository should preserve surname";
            }
            if (!testEntity2.getSurname().equals(daoSaved.getSurname())) {
                return "FAIL: DAO should preserve surname";
            }

            Optional<Designer> repoFound = designerRepository.findById(repoSaved.getId());
            Optional<Designer> daoFoundOptional = designerRepoDao.findById(daoSaved.getId());
            Designer daoFoundDirect = designerRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }

            if (!repoSaved.getId().equals(repoFound.get().getId())) {
                return "FAIL: Repository should find correct entity";
            }
            if (!daoSaved.getId().equals(daoFoundOptional.get().getId())) {
                return "FAIL: DAO findById should find correct entity";
            }
            if (!daoSaved.getId().equals(daoFoundDirect.getId())) {
                return "FAIL: DAO findOne should find correct entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<Designer> repoResults = designerRepository.findAll();
            List<Designer> daoResults = designerRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            Designer entityForRepo = createTestEntity("INTEGRATION_DELETE_001", "Test1");
            Designer entityForDao = createTestEntity("INTEGRATION_DELETE_002", "Test2");
            
            Designer savedForRepo = designerRepository.save(entityForRepo);
            Designer savedForDao = designerRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!designerRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!designerRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            designerRepository.delete(savedForRepo);
            designerRepoDao.delete(savedForDao);
            entityManager.flush();

            if (designerRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (designerRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testGetAllPublished() {
        setUp();
        try {
            List<Designer> repoResultsPreview = designerRepository.getAllPublished(true);
            List<Designer> daoResultsPreview = designerRepoDao.getAllPublished(true);
            
            List<Designer> repoResultsNoPreview = designerRepository.getAllPublished(false);
            List<Designer> daoResultsNoPreview = designerRepoDao.getAllPublished(false);

            if (repoResultsPreview.size() != daoResultsPreview.size()) {
                return "FAIL: Preview results don't match - Repository: " + repoResultsPreview.size() + ", DAO: " + daoResultsPreview.size();
            }
            if (repoResultsNoPreview.size() != daoResultsNoPreview.size()) {
                return "FAIL: No-preview results don't match - Repository: " + repoResultsNoPreview.size() + ", DAO: " + daoResultsNoPreview.size();
            }
            
            return "PASS: getAllPublished() test successful";
        } finally {
            tearDown();
        }
    }

    public String testGetAllDesigners() {
        setUp();
        try {
            List<Designer> repoResults = designerRepository.getAllDesigners();
            List<Designer> daoResults = designerRepoDao.getAllDesigners();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of designers - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: getAllDesigners() test successful";
        } finally {
            tearDown();
        }
    }

    public String testGetDesignerByFamiglia() {
        setUp();
        try {
            // Get a real Famiglia ID from the database
            Optional<Long> famigliaIdOpt = DbUtil.findFirstId(entityManager, com.yr.entity.Famiglia.class);
            if (!famigliaIdOpt.isPresent()) {
                return "SKIP: No Famiglia records found in database";
            }
            
            // Load the actual Famiglia entity
            com.yr.entity.Famiglia famiglia = entityManager.find(com.yr.entity.Famiglia.class, famigliaIdOpt.get());
            if (famiglia == null) {
                return "SKIP: Could not load Famiglia entity";
            }
            
            List<Designer> repoResults = designerRepository.getDesignerByFamiglia(famiglia);
            List<Designer> daoResults = designerRepoDao.getDesignerByFamiglia(famiglia);

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of designers for famiglia ID " + famiglia.getId() + " - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: getDesignerByFamiglia() test successful with famiglia ID " + famiglia.getId();
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== DesignerRepository vs DesignerRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String getAllPublishedResult = testGetAllPublished();
            report.append("5. GetAllPublished Test: ").append(getAllPublishedResult).append("\n");
            
            String getAllDesignersResult = testGetAllDesigners();
            report.append("6. GetAllDesigners Test: ").append(getAllDesignersResult).append("\n");
            
            String getDesignerByFamigliaResult = testGetDesignerByFamiglia();
            report.append("7. GetDesignerByFamiglia Test: ").append(getDesignerByFamigliaResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - DesignerRepoDao behaves exactly like DesignerRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
