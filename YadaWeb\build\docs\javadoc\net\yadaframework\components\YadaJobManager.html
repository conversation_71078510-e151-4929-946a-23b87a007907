<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Yada<PERSON>obManager (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.components, class: YadaJobManager">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.components</a></div>
<h1 title="Class YadaJobManager" class="title">Class YadaJobManager</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.components.YadaJobManager</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Service
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaJobManager</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Job handling.
 Use this class to add/delete/remove jobs. Do not use the YadaJobScheduler directly.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaJobManager</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#changeJobPriority(net.yadaframework.persistence.entity.YadaJob,int)" class="member-name-link">changeJobPriority</a><wbr>(<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob,
 int&nbsp;priority)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#completeJob(java.lang.Long)" class="member-name-link">completeJob</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a job to COMPLETED</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#countActiveOrRunningJobs(java.lang.String)" class="member-name-link">countActiveOrRunningJobs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the number of jobs for the given group that are in the ACTIVE/RUNNING state</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteJob(net.yadaframework.persistence.entity.YadaJob)" class="member-name-link">deleteJob</a><wbr>(<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Removes the job from the database.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#disableAndInterruptJob(java.lang.Long)" class="member-name-link">disableAndInterruptJob</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a job to DISABLED then interrupt its thread</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#disableAndInterruptJobs(java.util.List)" class="member-name-link">disableAndInterruptJobs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;&nbsp;yadaJobs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a list of jobs to DISABLED then interrupt the threads.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAllActiveOrRunningJobs(java.lang.String)" class="member-name-link">getAllActiveOrRunningJobs</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns all jobs for the given group that are in the ACTIVE/RUNNING state, ordered by state and scheduled time.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJobInstance(java.lang.Long)" class="member-name-link">getJobInstance</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;id)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns an instance of a YadaJob.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init(org.springframework.context.event.ContextRefreshedEvent)" class="member-name-link">init</a><wbr>(org.springframework.context.event.ContextRefreshedEvent&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Start the scheduler.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isJobGroupPaused(java.lang.String)" class="member-name-link">isJobGroupPaused</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the job group has been paused</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pauseAndInterruptJob(java.lang.Long)" class="member-name-link">pauseAndInterruptJob</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a job to PAUSED then interrupt its thread</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#pauseJobGroup(java.lang.String,boolean)" class="member-name-link">pauseJobGroup</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup,
 boolean&nbsp;interrupt)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Pause all jobs in the given group</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#replaceWithCached(net.yadaframework.persistence.entity.YadaJob)" class="member-name-link">replaceWithCached</a><wbr>(<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Call this method after fetching a job instance from the database to replace it with a cached running instance if any.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#reschedule(net.yadaframework.persistence.entity.YadaJob,java.util.Date)" class="member-name-link">reschedule</a><wbr>(<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;newScheduling)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resumeJobGroup(java.lang.String)" class="member-name-link">resumeJobGroup</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Resume jobs of the given group</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startJob(net.yadaframework.persistence.entity.YadaJob)" class="member-name-link">startJob</a><wbr>(<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Activate the job so that it becomes available for the scheduler to start it.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toggleDisabledAndPaused(java.lang.Long)" class="member-name-link">toggleDisabledAndPaused</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Toggle between paused and disabled.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaJobManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaJobManager</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="init(org.springframework.context.event.ContextRefreshedEvent)">
<h3>init</h3>
<div class="member-signature"><span class="annotations">@EventListener
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span><wbr><span class="parameters">(org.springframework.context.event.ContextRefreshedEvent&nbsp;event)</span></div>
<div class="block">Start the scheduler.
 Method called after Spring has finished initializing the Application Context, so that YadaPersistentEnums have been initialized.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>event</code> - </dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="startJob(net.yadaframework.persistence.entity.YadaJob)">
<h3>startJob</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">startJob</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob)</span></div>
<div class="block">Activate the job so that it becomes available for the scheduler to start it. 
 The scheduled time is left unchanged unless null, when it is set to NOW (start ASAP).
 This method does nothing to a job that is already scheduled and in the ACTIVE state.
 Any errors are reset.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJob</code> - the job to start</dd>
<dt>Returns:</dt>
<dd>true if the job has been activated, false if it doesn't exist in the database</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isJobGroupPaused(java.lang.String)">
<h3>isJobGroupPaused</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isJobGroupPaused</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</span></div>
<div class="block">Check if the job group has been paused</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="pauseJobGroup(java.lang.String,boolean)">
<h3>pauseJobGroup</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pauseJobGroup</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup,
 boolean&nbsp;interrupt)</span></div>
<div class="block">Pause all jobs in the given group</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - </dd>
<dd><code>interrupt</code> - true to interrupt a job that is in execution, false to let it complete before pausing</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="resumeJobGroup(java.lang.String)">
<h3>resumeJobGroup</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">resumeJobGroup</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</span></div>
<div class="block">Resume jobs of the given group</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteJob(net.yadaframework.persistence.entity.YadaJob)">
<h3>deleteJob</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">deleteJob</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob)</span></div>
<div class="block">Removes the job from the database.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJob</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="countActiveOrRunningJobs(java.lang.String)">
<h3>countActiveOrRunningJobs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">countActiveOrRunningJobs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</span></div>
<div class="block">Returns the number of jobs for the given group that are in the ACTIVE/RUNNING state</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAllActiveOrRunningJobs(java.lang.String)">
<h3>getAllActiveOrRunningJobs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;</span>&nbsp;<span class="element-name">getAllActiveOrRunningJobs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</span></div>
<div class="block">Returns all jobs for the given group that are in the ACTIVE/RUNNING state, ordered by state and scheduled time.
 The running jobs are the actual cached instances present in the YadaJobScheduler.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="disableAndInterruptJobs(java.util.List)">
<h3>disableAndInterruptJobs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">disableAndInterruptJobs</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;&nbsp;yadaJobs)</span></div>
<div class="block">Set a list of jobs to DISABLED then interrupt the threads.
 All jobs are first set to disabled, then they are all interrupted.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJob</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="completeJob(java.lang.Long)">
<h3>completeJob</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">completeJob</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</span></div>
<div class="block">Set a job to COMPLETED</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJob</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toggleDisabledAndPaused(java.lang.Long)">
<h3>toggleDisabledAndPaused</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">toggleDisabledAndPaused</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</span></div>
<div class="block">Toggle between paused and disabled.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJob</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="disableAndInterruptJob(java.lang.Long)">
<h3>disableAndInterruptJob</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">disableAndInterruptJob</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</span></div>
<div class="block">Set a job to DISABLED then interrupt its thread</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJob</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="pauseAndInterruptJob(java.lang.Long)">
<h3>pauseAndInterruptJob</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">pauseAndInterruptJob</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</span></div>
<div class="block">Set a job to PAUSED then interrupt its thread</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJob</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="changeJobPriority(net.yadaframework.persistence.entity.YadaJob,int)">
<h3>changeJobPriority</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></span>&nbsp;<span class="element-name">changeJobPriority</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob,
 int&nbsp;priority)</span></div>
</section>
</li>
<li>
<section class="detail" id="reschedule(net.yadaframework.persistence.entity.YadaJob,java.util.Date)">
<h3>reschedule</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></span>&nbsp;<span class="element-name">reschedule</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;newScheduling)</span></div>
</section>
</li>
<li>
<section class="detail" id="getJobInstance(java.lang.Long)">
<h3>getJobInstance</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></span>&nbsp;<span class="element-name">getJobInstance</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;id)</span></div>
<div class="block">Returns an instance of a YadaJob. The instance is freshly loaded from the database if no other thread
 already holds a reference to it, otherwise the instance is shared among threads.
 It is thus possible for concurrent threads to modify the same entity (= table row) without incurring in a
 concurrent modification exception. The instance is removed from the cache when no one holds a reference to it anymore.
 The typical scenario is when a job is already running (an instance has been cached by the scheduler) and a user 
 from the web interface changes its name.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>id</code> - the job id</dd>
<dt>Returns:</dt>
<dd>a YadaJob instance that can be freely modified and saved</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="replaceWithCached(net.yadaframework.persistence.entity.YadaJob)">
<h3>replaceWithCached</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></span>&nbsp;<span class="element-name">replaceWithCached</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob)</span></div>
<div class="block">Call this method after fetching a job instance from the database to replace it with a cached running instance if any.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJob</code> - </dd>
<dt>Returns:</dt>
<dd>the cached instance or the original argument</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
