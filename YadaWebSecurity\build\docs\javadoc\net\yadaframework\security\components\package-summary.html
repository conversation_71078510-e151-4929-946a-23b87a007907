<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.security.components (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.security.components">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package net.yadaframework.security.components" class="title">Package net.yadaframework.security.components</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">net.yadaframework.security.components</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">net.yadaframework.security</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../exceptions/package-summary.html">net.yadaframework.security.exceptions</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../web/package-summary.html">net.yadaframework.security.web</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="caption"><span>Classes</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Questa classe aggiunge un pò di informazioni in request quando il login fallisce.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaAuthenticationSuccessFilter.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessFilter</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Converts the AJAX_LOGGEDIN_PARAM request parameter into a AJAX_LOGGEDIN_HEADER response header
 so that the ajax target, after login, knows it has to close the login modal somehow (with a reload)</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaLogoutSuccessHandler.html" title="class in net.yadaframework.security.components">YadaLogoutSuccessHandler</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Handler called during logout</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaSecurityBeans.html" title="class in net.yadaframework.security.components">YadaSecurityBeans</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Handles autologin links: creation and parsing.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaUserSetup.html" title="class in net.yadaframework.security.components">YadaUserSetup</a>&lt;T extends <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&gt;</div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Convenience method to create configured application users.</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
