create table ScenarioImage (id bigint not null auto_increment, imageId bigint not null, plusPositionx float not null, plusPositiony float not null, scenario integer, version bigint not null, project_id bigint, primary key (id)) engine=InnoDB;
alter table ScenarioImage add constraint UKrani1fwmonwfexxciw6oj3d3 unique (project_id, scenario);
alter table ScenarioImage add constraint UK23hl153awuklgd6m6wh3hra7o unique (project_id, scenario, imageId);
alter table ScenarioImage add constraint FKnt68g3lbt6i1wojfl0fdbw22g foreign key (project_id) references Project (id);
