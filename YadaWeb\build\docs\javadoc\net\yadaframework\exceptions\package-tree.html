<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.exceptions Class Hierarchy (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="tree: package: net.yadaframework.exceptions">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package net.yadaframework.exceptions</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Throwable.html" class="type-name-link external-link" title="class or interface in java.lang">Throwable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Exception.html" class="type-name-link external-link" title="class or interface in java.lang">Exception</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" class="type-name-link external-link" title="class or interface in java.lang">RuntimeException</a>
<ul>
<li class="circle">net.yadaframework.exceptions.<a href="InternalException.html" class="type-name-link" title="class in net.yadaframework.exceptions">InternalException</a></li>
<li class="circle">net.yadaframework.exceptions.<a href="InvalidValueException.html" class="type-name-link" title="class in net.yadaframework.exceptions">InvalidValueException</a></li>
<li class="circle">net.yadaframework.exceptions.<a href="SystemException.html" class="type-name-link" title="class in net.yadaframework.exceptions">SystemException</a></li>
<li class="circle">net.yadaframework.exceptions.<a href="YadaConfigurationException.html" class="type-name-link" title="class in net.yadaframework.exceptions">YadaConfigurationException</a></li>
<li class="circle">net.yadaframework.exceptions.<a href="YadaEmailException.html" class="type-name-link" title="class in net.yadaframework.exceptions">YadaEmailException</a></li>
<li class="circle">net.yadaframework.exceptions.<a href="YadaInternalException.html" class="type-name-link" title="class in net.yadaframework.exceptions">YadaInternalException</a></li>
<li class="circle">net.yadaframework.exceptions.<a href="YadaInvalidUsageException.html" class="type-name-link" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></li>
<li class="circle">net.yadaframework.exceptions.<a href="YadaInvalidValueException.html" class="type-name-link" title="class in net.yadaframework.exceptions">YadaInvalidValueException</a></li>
<li class="circle">net.yadaframework.exceptions.<a href="YadaJobFailedException.html" class="type-name-link" title="class in net.yadaframework.exceptions">YadaJobFailedException</a></li>
<li class="circle">net.yadaframework.exceptions.<a href="YadaSystemException.html" class="type-name-link" title="class in net.yadaframework.exceptions">YadaSystemException</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.exceptions.<a href="YadaAlreadyRunningException.html" class="type-name-link" title="class in net.yadaframework.exceptions">YadaAlreadyRunningException</a></li>
<li class="circle">net.yadaframework.exceptions.<a href="YadaSocialException.html" class="type-name-link" title="class in net.yadaframework.exceptions">YadaSocialException</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
