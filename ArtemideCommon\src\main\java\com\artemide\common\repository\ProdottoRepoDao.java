package com.artemide.common.repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.Prodotto;
import com.yr.entity.Subfamily;

@Repository
@Transactional(readOnly = true)
public class ProdottoRepoDao {

    @PersistenceContext EntityManager em;

    public Prodotto findByCodice(String codice) {
        String sql = "from Prodotto where codice = :codice";
        try {
            return em.createQuery(sql, Prodotto.class)
                .setMaxResults(1)
                .setParameter("codice", codice)
                .getSingleResult();
        } catch (NonUniqueResultException | NoResultException e) {
            return null;
        }
    }

    /**
     * Cerca, tra un insieme di prodotti, se ce ne sono che appartengono alla stessa famiglia di una data sottofamiglia e stanno in un certo insieme
     * @param subfamilyId
     * @param productIds
     * @return gli id dei prodotti dell'insieme che sono nella stessa famiglia della sottofamiglia
     */
    @SuppressWarnings("unchecked")
    public Set<Long> findProductIdsForFamilyOfSubfamily(Long subfamilyId, Set<Long> productIds) {
        String sql = "select p.id from Prodotto p left join p.subfamily s left join s.famiglia f where f.id = " +
                "(select distinct f2.id from Subfamily s2 left join s2.famiglia f2 where s2.id=:subfamilyId) " +
                "and p.id in :productIds";
        return (Set<Long>) em.createQuery(sql)
            .setParameter("subfamilyId", subfamilyId)
            .setParameter("productIds", productIds)
            .getResultList();
    }

    /**
     * Cerca, tra un insieme di prodotti, quelli che appartengono a una data famiglia
     * @param familyId
     * @param productIds
     * @return
     */
    @SuppressWarnings("unchecked")
    public Set<Long> findProductIdsForFamily(Long familyId, Set<Long> productIds) {
        String sql = "select p.id from Prodotto p left join p.subfamily s left join s.famiglia f where f.id = :familyId and p.id in :productIds";
        return (Set<Long>) em.createQuery(sql)
            .setParameter("familyId", familyId)
            .setParameter("productIds", productIds)
            .getResultList();
    }

    /**
     * Cerca, tra un insieme di prodotti, quelli che appartengono a una data sottofamiglia
     * @param subfamilyId
     * @param productIds
     * @return
     */
    @SuppressWarnings("unchecked")
    public Set<Long> findProductIdsForSubfamily(Long subfamilyId, Collection<Long> productIds) {
        String sql = "select p.id from Prodotto p left join p.subfamily s where s.id = :subfamilyId and p.id in :productIds";
        return (Set<Long>) em.createQuery(sql)
            .setParameter("subfamilyId", subfamilyId)
            .setParameter("productIds", productIds)
            .getResultList();
    }

    /**
     * Trova tutti i prodotti che hanno un id tra quelle indicate, prelevando anche gli article in modo da avere l'id completo
     * @param ids
     * @return
     */
    public List<Prodotto> findAllWithArticle(Collection<Long> ids) {
        String sql = "select distinct p from Prodotto p left join fetch p.componenti where p.id in :ids";
        return em.createQuery(sql, Prodotto.class)
            .setParameter("ids", ids)
            .getResultList();
    }

    /**
     * Ritorna 1 se i prodotti passati appartengono alla famiglia indicata, altrimenti null
     */
    public Integer productsInFamily(Long idFamily, Collection<Long> idProdotti) {
        String sql = "select 1 from Prodotto p join p.subfamily s join s.famiglia f where f.id=:idFamily and p.id in :idProdotti";
        List<Integer> resultList = em.createQuery(sql, Integer.class)
            .setParameter("idFamily", idFamily)
            .setParameter("idProdotti", idProdotti)
            .setMaxResults(1)
            .getResultList();
        return resultList.isEmpty() ? null : resultList.get(0);
    }

    /**
     * Ritorna 1 se i prodotti passati appartengono alla sottofamiglia indicata, altrimenti null
     */
    public Integer productsInSubfamily(Long idSubfamily, Collection<Long> idProdotti) {
        String sql = "select 1 from Prodotto p join p.subfamily s where s.id=:idSubfamily and p.id in :idProdotti";
        List<Integer> resultList = em.createQuery(sql, Integer.class)
            .setParameter("idSubfamily", idSubfamily)
            .setParameter("idProdotti", idProdotti)
            .setMaxResults(1)
            .getResultList();
        return resultList.isEmpty() ? null : resultList.get(0);
    }

    /**
     * Data l'id del prodotto ritorna l'id della sua famiglia e sottofamiglia
     * @param idProdotto
     * @return
     */
    @SuppressWarnings("unchecked")
    public Object[][] getFamilySubfamilyId(Long idProdotto) {
        String sql = "select f.id, s.id from Prodotto p join p.subfamily s join s.famiglia f where p.id=:idProdotto";
        return (Object[][]) em.createQuery(sql)
            .setParameter("idProdotto", idProdotto)
            .getResultList().toArray(new Object[0][]);
    }

    /**
     * Ritorna il nome short del prodotto, localizzato
     * @param id
     * @param locale
     * @return
     */
    public String getProductShortName(Long id, String locale) {
        String sql = "select sn.value from Prodotto p join p.shortNames sn where p.id=:id and KEY(sn)=:locale";
        List<String> resultList = em.createQuery(sql, String.class)
            .setParameter("id", id)
            .setParameter("locale", locale)
            .setMaxResults(1)
            .getResultList();
        return resultList.isEmpty() ? null : resultList.get(0);
    }

    /**
     * Ritorna il nome del prodotto, localizzato
     * @param id
     * @param locale
     * @return
     */
    public String getProductName(Long id, String locale) {
        String sql = "select n.value from Prodotto p join p.names n where p.id=:id and KEY(n)=:locale";
        List<String> resultList = em.createQuery(sql, String.class)
            .setParameter("id", id)
            .setParameter("locale", locale)
            .setMaxResults(1)
            .getResultList();
        return resultList.isEmpty() ? null : resultList.get(0);
    }

    /**
     * Prende i prodotti dalla subfamily, ordinate come impostato nel CMS
     * @param subfamily
     * @param preview se true, prende anche le published a false
     * @return
     */
    public List<Prodotto> findBySubFamily(Subfamily subfamily, boolean preview) {
        String sql = "select p from Prodotto p join p.subfamily sf where sf = :subfamily and (:preview=true or (sf.published=true and p.stato = com.yr.entity.Prodotto.STATUS_PRODOTTO_PUBLISHED)) and p.countryCode is null order by p.pos";
        return em.createQuery(sql, Prodotto.class)
            .setParameter("subfamily", subfamily)
            .setParameter("preview", preview)
            .getResultList();
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Prodotto", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Prodotto save(Prodotto entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Prodotto> findById(Long entityId) {
        Prodotto result = em.find(Prodotto.class, entityId);
        return Optional.ofNullable(result);
    }

    public Prodotto findOne(Long entityId) {
        return em.find(Prodotto.class, entityId);
    }

    public List<Prodotto> findAll() {
        return em.createQuery("from Prodotto", Prodotto.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Prodotto> batchToSave) {
        for (Prodotto entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Prodotto entity) {
        em.remove(entity);
    }
}
