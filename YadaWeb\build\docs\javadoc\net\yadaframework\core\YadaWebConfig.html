<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaWebConfig (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.core, class: YadaWebConfig">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.core</a></div>
<h1 title="Class YadaWebConfig" class="title">Class YadaWebConfig</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.core.YadaWebConfig</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code>org.springframework.web.servlet.config.annotation.WebMvcConfigurer</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="annotations">@EnableWebMvc
@EnableScheduling
@EnableAsync
@ComponentScan(basePackages="net.yadaframework.web")
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaWebConfig</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements org.springframework.web.servlet.config.annotation.WebMvcConfigurer</span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected org.springframework.context.ApplicationContext</code></div>
<div class="col-second even-row-color"><code><a href="#applicationContext" class="member-name-link">applicationContext</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="YadaConfiguration.html" title="class in net.yadaframework.core">YadaConfiguration</a></code></div>
<div class="col-second odd-row-color"><code><a href="#config" class="member-name-link">config</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#MILLIS_IN_MINUTE" class="member-name-link">MILLIS_IN_MINUTE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#MILLIS_IN_SECOND" class="member-name-link">MILLIS_IN_SECOND</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#SECONDS_IN_MINUTE" class="member-name-link">SECONDS_IN_MINUTE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#STATIC_FILE_FOLDER" class="member-name-link">STATIC_FILE_FOLDER</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#STATIC_RESOURCES_FOLDER" class="member-name-link">STATIC_RESOURCES_FOLDER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected static final <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#STATIC_YADARESOURCES_FOLDER" class="member-name-link">STATIC_YADARESOURCES_FOLDER</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaWebConfig</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addExtraDialect(org.thymeleaf.spring6.SpringTemplateEngine)" class="member-name-link">addExtraDialect</a><wbr>(org.thymeleaf.spring6.SpringTemplateEngine&nbsp;engine)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">To be overridden when a new dialect has to be added, e.g.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addInterceptors(org.springframework.web.servlet.config.annotation.InterceptorRegistry)" class="member-name-link">addInterceptors</a><wbr>(org.springframework.web.servlet.config.annotation.InterceptorRegistry&nbsp;registry)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addResourceHandlers(org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry)" class="member-name-link">addResourceHandlers</a><wbr>(org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry&nbsp;registry)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tutti i file dentro a /res vengono indicati come cacheabili lato browser per 1 anno (tramite l'header expires).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addYadaDialect(org.thymeleaf.spring6.SpringTemplateEngine)" class="member-name-link">addYadaDialect</a><wbr>(org.thymeleaf.spring6.SpringTemplateEngine&nbsp;engine)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#configureAsyncSupport(org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer)" class="member-name-link">configureAsyncSupport</a><wbr>(org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer&nbsp;configurer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNotLocalizedResourcePattern()" class="member-name-link">getNotLocalizedResourcePattern</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a string pattern to match urls that should not be localised when using a language path variable
 i.e.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getResourceFolder()" class="member-name-link">getResourceFolder</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Name of the folder where versioned static files are to be found, starting with /</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getStaticFileFolder()" class="member-name-link">getStaticFileFolder</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Name of the folder where non-versioned static files are to be found, starting with /</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getYadaResourceFolder()" class="member-name-link">getYadaResourceFolder</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Name of the folder where versioned static yada files are to be found, starting with /</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#init()" class="member-name-link">init</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.thymeleaf.spring6.SpringTemplateEngine</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#javascriptTemplateEngine()" class="member-name-link">javascriptTemplateEngine</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.thymeleaf.templateresolver.ITemplateResolver</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#javascriptTemplateResolver()" class="member-name-link">javascriptTemplateResolver</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.springframework.web.servlet.ViewResolver</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#javascriptViewResolver(org.thymeleaf.spring6.SpringTemplateEngine)" class="member-name-link">javascriptViewResolver</a><wbr>(org.thymeleaf.spring6.SpringTemplateEngine&nbsp;javascriptTemplateEngine)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">View resolver for js files that can be anywhere in the views folder.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.springframework.web.servlet.i18n.LocaleChangeInterceptor</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#localeChangeInterceptor()" class="member-name-link">localeChangeInterceptor</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.springframework.web.servlet.LocaleResolver</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#localeResolver()" class="member-name-link">localeResolver</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.thymeleaf.templateresolver.ITemplateResolver</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#mailPreviewTemplateResolver()" class="member-name-link">mailPreviewTemplateResolver</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Template resolver for mail preview pages that include email templates (maybe I could just use emailTemplateResolver())</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.springframework.format.support.FormattingConversionService</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#mvcConversionService()" class="member-name-link">mvcConversionService</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Register the configured DateFormatter or the default DefaultFormattingConversionService</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.thymeleaf.spring6.SpringTemplateEngine</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#templateEngine()" class="member-name-link">templateEngine</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.springframework.web.servlet.ViewResolver</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#viewResolver(org.thymeleaf.spring6.SpringTemplateEngine)" class="member-name-link">viewResolver</a><wbr>(org.thymeleaf.spring6.SpringTemplateEngine&nbsp;templateEngine)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">View resolver for html pages.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.thymeleaf.templateresolver.ITemplateResolver</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#webTemplateResolver()" class="member-name-link">webTemplateResolver</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.thymeleaf.spring6.SpringTemplateEngine</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#xmlTemplateEngine()" class="member-name-link">xmlTemplateEngine</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.thymeleaf.templateresolver.ITemplateResolver</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#xmlTemplateResolver()" class="member-name-link">xmlTemplateResolver</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.springframework.web.servlet.ViewResolver</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#xmlViewResolver(org.thymeleaf.spring6.SpringTemplateEngine)" class="member-name-link">xmlViewResolver</a><wbr>(org.thymeleaf.spring6.SpringTemplateEngine&nbsp;xmlTemplateEngine)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">View resolver for xml files that are inside the /xml folder.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaLocalePathChangeInterceptor.html" title="class in net.yadaframework.core">YadaLocalePathChangeInterceptor</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#yadaLocalePathChangeInterceptor()" class="member-name-link">yadaLocalePathChangeInterceptor</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.thymeleaf.templateresolver.ClassLoaderTemplateResolver</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#yadaTemplateResolver()" class="member-name-link">yadaTemplateResolver</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.web.servlet.config.annotation.WebMvcConfigurer">Methods inherited from interface&nbsp;org.springframework.web.servlet.config.annotation.WebMvcConfigurer</h3>
<code>addArgumentResolvers, addCorsMappings, addErrorResponseInterceptors, addFormatters, addReturnValueHandlers, addViewControllers, configureContentNegotiation, configureDefaultServletHandling, configureHandlerExceptionResolvers, configureMessageConverters, configurePathMatch, configureViewResolvers, extendHandlerExceptionResolvers, extendMessageConverters, getMessageCodesResolver, getValidator</code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="MILLIS_IN_SECOND">
<h3>MILLIS_IN_SECOND</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MILLIS_IN_SECOND</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.core.YadaWebConfig.MILLIS_IN_SECOND">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="SECONDS_IN_MINUTE">
<h3>SECONDS_IN_MINUTE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">SECONDS_IN_MINUTE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.core.YadaWebConfig.SECONDS_IN_MINUTE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="MILLIS_IN_MINUTE">
<h3>MILLIS_IN_MINUTE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">MILLIS_IN_MINUTE</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.core.YadaWebConfig.MILLIS_IN_MINUTE">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="STATIC_RESOURCES_FOLDER">
<h3>STATIC_RESOURCES_FOLDER</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">STATIC_RESOURCES_FOLDER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.core.YadaWebConfig.STATIC_RESOURCES_FOLDER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="STATIC_YADARESOURCES_FOLDER">
<h3>STATIC_YADARESOURCES_FOLDER</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">STATIC_YADARESOURCES_FOLDER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.core.YadaWebConfig.STATIC_YADARESOURCES_FOLDER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="STATIC_FILE_FOLDER">
<h3>STATIC_FILE_FOLDER</h3>
<div class="member-signature"><span class="modifiers">protected static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">STATIC_FILE_FOLDER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.core.YadaWebConfig.STATIC_FILE_FOLDER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="config">
<h3>config</h3>
<div class="member-signature"><span class="annotations">@Autowired
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="YadaConfiguration.html" title="class in net.yadaframework.core">YadaConfiguration</a></span>&nbsp;<span class="element-name">config</span></div>
</section>
</li>
<li>
<section class="detail" id="applicationContext">
<h3>applicationContext</h3>
<div class="member-signature"><span class="annotations">@Autowired
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">org.springframework.context.ApplicationContext</span>&nbsp;<span class="element-name">applicationContext</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaWebConfig</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaWebConfig</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="init()">
<h3>init</h3>
<div class="member-signature"><span class="annotations">@PostConstruct
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">init</span>()</div>
</section>
</li>
<li>
<section class="detail" id="configureAsyncSupport(org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer)">
<h3>configureAsyncSupport</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">configureAsyncSupport</span><wbr><span class="parameters">(org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer&nbsp;configurer)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>configureAsyncSupport</code>&nbsp;in interface&nbsp;<code>org.springframework.web.servlet.config.annotation.WebMvcConfigurer</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNotLocalizedResourcePattern()">
<h3>getNotLocalizedResourcePattern</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getNotLocalizedResourcePattern</span>()</div>
<div class="block">Return a string pattern to match urls that should not be localised when using a language path variable
 i.e. the language code will not be added when using @{} in thymeleaf</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>"(?:/res|/yadares|/static|/contents)"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getYadaResourceFolder()">
<h3>getYadaResourceFolder</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getYadaResourceFolder</span>()</div>
<div class="block">Name of the folder where versioned static yada files are to be found, starting with /</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResourceFolder()">
<h3>getResourceFolder</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getResourceFolder</span>()</div>
<div class="block">Name of the folder where versioned static files are to be found, starting with /</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getStaticFileFolder()">
<h3>getStaticFileFolder</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStaticFileFolder</span>()</div>
<div class="block">Name of the folder where non-versioned static files are to be found, starting with /</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="addInterceptors(org.springframework.web.servlet.config.annotation.InterceptorRegistry)">
<h3>addInterceptors</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addInterceptors</span><wbr><span class="parameters">(org.springframework.web.servlet.config.annotation.InterceptorRegistry&nbsp;registry)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>addInterceptors</code>&nbsp;in interface&nbsp;<code>org.springframework.web.servlet.config.annotation.WebMvcConfigurer</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="localeChangeInterceptor()">
<h3>localeChangeInterceptor</h3>
<div class="member-signature"><span class="annotations">@Bean
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.web.servlet.i18n.LocaleChangeInterceptor</span>&nbsp;<span class="element-name">localeChangeInterceptor</span>()</div>
</section>
</li>
<li>
<section class="detail" id="yadaLocalePathChangeInterceptor()">
<h3>yadaLocalePathChangeInterceptor</h3>
<div class="member-signature"><span class="annotations">@Bean
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaLocalePathChangeInterceptor.html" title="class in net.yadaframework.core">YadaLocalePathChangeInterceptor</a></span>&nbsp;<span class="element-name">yadaLocalePathChangeInterceptor</span>()</div>
</section>
</li>
<li>
<section class="detail" id="localeResolver()">
<h3>localeResolver</h3>
<div class="member-signature"><span class="annotations">@Bean(name="localeResolver")
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.web.servlet.LocaleResolver</span>&nbsp;<span class="element-name">localeResolver</span>()</div>
</section>
</li>
<li>
<section class="detail" id="mvcConversionService()">
<h3>mvcConversionService</h3>
<div class="member-signature"><span class="annotations">@Bean
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.format.support.FormattingConversionService</span>&nbsp;<span class="element-name">mvcConversionService</span>()</div>
<div class="block">Register the configured DateFormatter or the default DefaultFormattingConversionService</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="addResourceHandlers(org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry)">
<h3>addResourceHandlers</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addResourceHandlers</span><wbr><span class="parameters">(org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry&nbsp;registry)</span></div>
<div class="block">Tutti i file dentro a /res vengono indicati come cacheabili lato browser per 1 anno (tramite l'header expires).
 Per evitare che nuove versioni non vengano mai prese, si usa il "trucco" di indicare il numero di build nell'url, così cambiando
 la build cambia l'url e la cache del browser va in miss la prima volta.
 I file dentro a /static, invece, non cambiano mai nemmeno alle nuove release (anche se in cache stanno solo 100 giorni). Però non è per questo che si usa static, ma per il fatto che dentro ai commenti condizionali
 non si possono usare i tag thymeleaf, per cui ad esempio html5shiv.js viene messo in /static</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>addResourceHandlers</code>&nbsp;in interface&nbsp;<code>org.springframework.web.servlet.config.annotation.WebMvcConfigurer</code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaTemplateResolver()">
<h3>yadaTemplateResolver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.thymeleaf.templateresolver.ClassLoaderTemplateResolver</span>&nbsp;<span class="element-name">yadaTemplateResolver</span>()</div>
</section>
</li>
<li>
<section class="detail" id="mailPreviewTemplateResolver()">
<h3>mailPreviewTemplateResolver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.thymeleaf.templateresolver.ITemplateResolver</span>&nbsp;<span class="element-name">mailPreviewTemplateResolver</span>()</div>
<div class="block">Template resolver for mail preview pages that include email templates (maybe I could just use emailTemplateResolver())</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="webTemplateResolver()">
<h3>webTemplateResolver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.thymeleaf.templateresolver.ITemplateResolver</span>&nbsp;<span class="element-name">webTemplateResolver</span>()</div>
</section>
</li>
<li>
<section class="detail" id="javascriptTemplateResolver()">
<h3>javascriptTemplateResolver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.thymeleaf.templateresolver.ITemplateResolver</span>&nbsp;<span class="element-name">javascriptTemplateResolver</span>()</div>
</section>
</li>
<li>
<section class="detail" id="xmlTemplateResolver()">
<h3>xmlTemplateResolver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.thymeleaf.templateresolver.ITemplateResolver</span>&nbsp;<span class="element-name">xmlTemplateResolver</span>()</div>
</section>
</li>
<li>
<section class="detail" id="templateEngine()">
<h3>templateEngine</h3>
<div class="member-signature"><span class="annotations">@Bean
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.thymeleaf.spring6.SpringTemplateEngine</span>&nbsp;<span class="element-name">templateEngine</span>()</div>
</section>
</li>
<li>
<section class="detail" id="addExtraDialect(org.thymeleaf.spring6.SpringTemplateEngine)">
<h3>addExtraDialect</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addExtraDialect</span><wbr><span class="parameters">(org.thymeleaf.spring6.SpringTemplateEngine&nbsp;engine)</span></div>
<div class="block">To be overridden when a new dialect has to be added, e.g. engine.addDialect(new LayoutDialect());</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>engine</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addYadaDialect(org.thymeleaf.spring6.SpringTemplateEngine)">
<h3>addYadaDialect</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">addYadaDialect</span><wbr><span class="parameters">(org.thymeleaf.spring6.SpringTemplateEngine&nbsp;engine)</span></div>
</section>
</li>
<li>
<section class="detail" id="javascriptTemplateEngine()">
<h3>javascriptTemplateEngine</h3>
<div class="member-signature"><span class="annotations">@Bean
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.thymeleaf.spring6.SpringTemplateEngine</span>&nbsp;<span class="element-name">javascriptTemplateEngine</span>()</div>
</section>
</li>
<li>
<section class="detail" id="xmlTemplateEngine()">
<h3>xmlTemplateEngine</h3>
<div class="member-signature"><span class="annotations">@Bean
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.thymeleaf.spring6.SpringTemplateEngine</span>&nbsp;<span class="element-name">xmlTemplateEngine</span>()</div>
</section>
</li>
<li>
<section class="detail" id="javascriptViewResolver(org.thymeleaf.spring6.SpringTemplateEngine)">
<h3>javascriptViewResolver</h3>
<div class="member-signature"><span class="annotations">@Bean
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.web.servlet.ViewResolver</span>&nbsp;<span class="element-name">javascriptViewResolver</span><wbr><span class="parameters">(@Qualifier("javascriptTemplateEngine")
 org.thymeleaf.spring6.SpringTemplateEngine&nbsp;javascriptTemplateEngine)</span></div>
<div class="block">View resolver for js files that can be anywhere in the views folder.
 Contrary to usual practice, the view name MUST include the .js extension: return "/some/path/myfile.js".</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="xmlViewResolver(org.thymeleaf.spring6.SpringTemplateEngine)">
<h3>xmlViewResolver</h3>
<div class="member-signature"><span class="annotations">@Bean
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.web.servlet.ViewResolver</span>&nbsp;<span class="element-name">xmlViewResolver</span><wbr><span class="parameters">(@Qualifier("xmlTemplateEngine")
 org.thymeleaf.spring6.SpringTemplateEngine&nbsp;xmlTemplateEngine)</span></div>
<div class="block">View resolver for xml files that are inside the /xml folder.</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="viewResolver(org.thymeleaf.spring6.SpringTemplateEngine)">
<h3>viewResolver</h3>
<div class="member-signature"><span class="annotations">@Bean
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">org.springframework.web.servlet.ViewResolver</span>&nbsp;<span class="element-name">viewResolver</span><wbr><span class="parameters">(@Qualifier("templateEngine")
 org.thymeleaf.spring6.SpringTemplateEngine&nbsp;templateEngine)</span></div>
<div class="block">View resolver for html pages. It handles web pages, mail preview pages from classpath, yada snippets from classpath</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
