 # Nuove tabelle per homepage gestita da CMS
 
create table HomePage (id bigint not null auto_increment, primary key (id)) engine=InnoDB;
create table HomePage_SortedUploadedFiles (HomePage_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (HomePage_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
alter table HomePage_SortedUploadedFiles add constraint UK_56rjbtbsb2vvvfdk0qf5fox6k unique (sortedUploadedFilesMap_id);
alter table HomePage_SortedUploadedFiles add constraint FKh6odmienldqul3nf6y1tbww97 foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table HomePage_SortedUploadedFiles add constraint FKfx59od6ddwaflke53wo7q6q5u foreign key (HomePage_id) references HomePage (id);

 

