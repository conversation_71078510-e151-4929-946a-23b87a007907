package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.repository.ProdottoRepoDao;
import com.artemide.common.repository.ProdottoRepository;
import com.yr.entity.Famiglia;
import com.yr.entity.Prodotto;
import com.yr.entity.Subfamily;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies ProdottoRepoDao behaves exactly like ProdottoRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class ProdottoRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private ProdottoRepository prodottoRepository;

    @Autowired
    private ProdottoRepoDao prodottoRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    Prodotto entity = entityManager.find(Prodotto.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            entityManager.createQuery("DELETE FROM Prodotto p WHERE p.codice LIKE 'TEST_%' OR p.codice LIKE 'INTEGRATION_%'")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private Prodotto createTestEntity(String codice) {
        Prodotto entity = new Prodotto();
        entity.setCodice(codice);
        return entity;
    }

    private void trackEntity(Prodotto entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = prodottoRepository.count();
            long initialDaoCount = prodottoRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            Prodotto testEntity = createTestEntity("INTEGRATION_SAVE_001");

            Prodotto repoSaved = prodottoRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            Prodotto testEntity2 = createTestEntity("INTEGRATION_SAVE_002");
            Prodotto daoSaved = prodottoRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<Prodotto> repoFound = prodottoRepository.findById(repoSaved.getId());
            Optional<Prodotto> daoFoundOptional = prodottoRepoDao.findById(daoSaved.getId());
            Prodotto daoFoundDirect = prodottoRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<Prodotto> repoResults = prodottoRepository.findAll();
            List<Prodotto> daoResults = prodottoRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            Prodotto entityForRepo = createTestEntity("INTEGRATION_DELETE_001");
            Prodotto entityForDao = createTestEntity("INTEGRATION_DELETE_002");
            
            Prodotto savedForRepo = prodottoRepository.save(entityForRepo);
            Prodotto savedForDao = prodottoRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!prodottoRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!prodottoRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            prodottoRepository.delete(savedForRepo);
            prodottoRepoDao.delete(savedForDao);
            entityManager.flush();

            if (prodottoRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (prodottoRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindByCodice() {
        setUp();
        try {
            Prodotto testEntity = createTestEntity("INTEGRATION_CODICE_001");
            Prodotto saved = prodottoRepository.save(testEntity);
            trackEntity(saved);
            entityManager.flush();

            Prodotto repoResult = prodottoRepository.findByCodice("INTEGRATION_CODICE_001");
            Prodotto daoResult = prodottoRepoDao.findByCodice("INTEGRATION_CODICE_001");

            if (repoResult == null && daoResult == null) {
                return "PASS: findByCodice() test successful - both return null";
            }
            if (repoResult == null || daoResult == null) {
                return "FAIL: Repository and DAO should return same result - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            if (!repoResult.getId().equals(daoResult.getId())) {
                return "FAIL: Repository and DAO should return same entity - Repository ID: " + repoResult.getId() + ", DAO ID: " + daoResult.getId();
            }
            
            return "PASS: findByCodice() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindProductIdsForFamilyOfSubfamily() {
        setUp();
        try {
            // Get first available subfamily ID using DbUtil
            java.util.Optional<Long> subfamilyIdOpt = DbUtil.findFirstId(entityManager, Subfamily.class);
            if (!subfamilyIdOpt.isPresent()) {
                return "SKIP: No subfamily records found in database for testing";
            }
            Long testSubfamilyId = subfamilyIdOpt.get();
            
            // Get first 3 product IDs using DbUtil
            List<Long> productIds = DbUtil.findMultipleIds(entityManager, Prodotto.class, 3);
            if (productIds.size() < 3) {
                return "SKIP: Need at least 3 products for testing, found: " + productIds.size();
            }
            Set<Long> testProductIds = new java.util.HashSet<>(productIds);
            
            Set<Long> repoResult = prodottoRepository.findProductIdsForFamilyOfSubfamily(testSubfamilyId, testProductIds);
            Set<Long> daoResult = prodottoRepoDao.findProductIdsForFamilyOfSubfamily(testSubfamilyId, testProductIds);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findProductIdsForFamilyOfSubfamily results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findProductIdsForFamilyOfSubfamily() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindProductIdsForFamily() {
        setUp();
        try {
            // Get first available family ID using DbUtil
            java.util.Optional<Long> familyIdOpt = DbUtil.findFirstId(entityManager, Famiglia.class);
            if (!familyIdOpt.isPresent()) {
                return "SKIP: No famiglia records found in database for testing";
            }
            Long testFamilyId = familyIdOpt.get();
            
            // Get first 3 product IDs using DbUtil
            List<Long> productIds = DbUtil.findMultipleIds(entityManager, Prodotto.class, 3);
            if (productIds.size() < 3) {
                return "SKIP: Need at least 3 products for testing, found: " + productIds.size();
            }
            Set<Long> testProductIds = new java.util.HashSet<>(productIds);
            
            Set<Long> repoResult = prodottoRepository.findProductIdsForFamily(testFamilyId, testProductIds);
            Set<Long> daoResult = prodottoRepoDao.findProductIdsForFamily(testFamilyId, testProductIds);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findProductIdsForFamily results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findProductIdsForFamily() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindProductIdsForSubfamily() {
        setUp();
        try {
            // Get first available subfamily ID using DbUtil
            java.util.Optional<Long> subfamilyIdOpt = DbUtil.findFirstId(entityManager, Subfamily.class);
            if (!subfamilyIdOpt.isPresent()) {
                return "SKIP: No subfamily records found in database for testing";
            }
            Long testSubfamilyId = subfamilyIdOpt.get();
            
            // Get first 3 product IDs using DbUtil
            List<Long> productIds = DbUtil.findMultipleIds(entityManager, Prodotto.class, 3);
            if (productIds.size() < 3) {
                return "SKIP: Need at least 3 products for testing, found: " + productIds.size();
            }
            java.util.Collection<Long> testProductIds = productIds;
            
            Set<Long> repoResult = prodottoRepository.findProductIdsForSubfamily(testSubfamilyId, testProductIds);
            Set<Long> daoResult = prodottoRepoDao.findProductIdsForSubfamily(testSubfamilyId, testProductIds);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findProductIdsForSubfamily results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findProductIdsForSubfamily() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAllWithArticle() {
        setUp();
        try {
            // Get first 3 product IDs using DbUtil
            List<Long> productIds = DbUtil.findMultipleIds(entityManager, Prodotto.class, 3);
            if (productIds.size() < 3) {
                return "SKIP: Need at least 3 products for testing, found: " + productIds.size();
            }
            java.util.Collection<Long> testIds = productIds;
            
            List<Prodotto> repoResult = prodottoRepository.findAllWithArticle(testIds);
            List<Prodotto> daoResult = prodottoRepoDao.findAllWithArticle(testIds);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findAllWithArticle results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findAllWithArticle() test successful";
        } finally {
            tearDown();
        }
    }

    public String testProductsInFamily() {
        setUp();
        try {
            // Get first available family ID using DbUtil
            java.util.Optional<Long> familyIdOpt = DbUtil.findFirstId(entityManager, Famiglia.class);
            if (!familyIdOpt.isPresent()) {
                return "SKIP: No famiglia records found in database for testing";
            }
            Long testFamilyId = familyIdOpt.get();
            
            // Get first 3 product IDs using DbUtil
            List<Long> productIds = DbUtil.findMultipleIds(entityManager, Prodotto.class, 3);
            if (productIds.size() < 3) {
                return "SKIP: Need at least 3 products for testing, found: " + productIds.size();
            }
            java.util.Collection<Long> testProductIds = productIds;
            
            Integer repoResult = prodottoRepository.productsInFamily(testFamilyId, testProductIds);
            Integer daoResult = prodottoRepoDao.productsInFamily(testFamilyId, testProductIds);

            if ((repoResult == null && daoResult != null) || (repoResult != null && !repoResult.equals(daoResult))) {
                return "FAIL: productsInFamily results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: productsInFamily() test successful";
        } finally {
            tearDown();
        }
    }

    public String testProductsInSubfamily() {
        setUp();
        try {
            // Get first available subfamily ID using DbUtil
            java.util.Optional<Long> subfamilyIdOpt = DbUtil.findFirstId(entityManager, Subfamily.class);
            if (!subfamilyIdOpt.isPresent()) {
                return "SKIP: No subfamily records found in database for testing";
            }
            Long testSubfamilyId = subfamilyIdOpt.get();
            
            // Get first 3 product IDs using DbUtil
            List<Long> productIds = DbUtil.findMultipleIds(entityManager, Prodotto.class, 3);
            if (productIds.size() < 3) {
                return "SKIP: Need at least 3 products for testing, found: " + productIds.size();
            }
            java.util.Collection<Long> testProductIds = productIds;
            
            Integer repoResult = prodottoRepository.productsInSubfamily(testSubfamilyId, testProductIds);
            Integer daoResult = prodottoRepoDao.productsInSubfamily(testSubfamilyId, testProductIds);

            if ((repoResult == null && daoResult != null) || (repoResult != null && !repoResult.equals(daoResult))) {
                return "FAIL: productsInSubfamily results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: productsInSubfamily() test successful";
        } finally {
            tearDown();
        }
    }

    public String testGetFamilySubfamilyId() {
        setUp();
        try {
            // Get first available product ID using DbUtil
            java.util.Optional<Long> productIdOpt = DbUtil.findFirstId(entityManager, Prodotto.class);
            if (!productIdOpt.isPresent()) {
                return "SKIP: No product records found in database for testing";
            }
            Long testProductId = productIdOpt.get();
            
            Object[][] repoResult = prodottoRepository.getFamilySubfamilyId(testProductId);
            Object[][] daoResult = prodottoRepoDao.getFamilySubfamilyId(testProductId);

            if (repoResult.length != daoResult.length) {
                return "FAIL: getFamilySubfamilyId results don't match - Repository: " + repoResult.length + ", DAO: " + daoResult.length;
            }
            
            return "PASS: getFamilySubfamilyId() test successful";
        } finally {
            tearDown();
        }
    }

    public String testGetProductShortName() {
        setUp();
        try {
            // Get first available product ID using DbUtil
            java.util.Optional<Long> productIdOpt = DbUtil.findFirstId(entityManager, Prodotto.class);
            if (!productIdOpt.isPresent()) {
                return "SKIP: No product records found in database for testing";
            }
            Long testProductId = productIdOpt.get();
            String testLocale = "en_US";
            
            String repoResult = prodottoRepository.getProductShortName(testProductId, testLocale);
            String daoResult = prodottoRepoDao.getProductShortName(testProductId, testLocale);

            if ((repoResult == null && daoResult != null) || (repoResult != null && !repoResult.equals(daoResult))) {
                return "FAIL: getProductShortName results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: getProductShortName() test successful";
        } finally {
            tearDown();
        }
    }

    public String testGetProductName() {
        setUp();
        try {
            // Get first available product ID using DbUtil
            java.util.Optional<Long> productIdOpt = DbUtil.findFirstId(entityManager, Prodotto.class);
            if (!productIdOpt.isPresent()) {
                return "SKIP: No product records found in database for testing";
            }
            Long testProductId = productIdOpt.get();
            String testLocale = "en_US";
            
            String repoResult = prodottoRepository.getProductName(testProductId, testLocale);
            String daoResult = prodottoRepoDao.getProductName(testProductId, testLocale);

            if ((repoResult == null && daoResult != null) || (repoResult != null && !repoResult.equals(daoResult))) {
                return "FAIL: getProductName results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: getProductName() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindBySubFamily() {
        setUp();
        try {
            // Get first available subfamily ID using DbUtil
            java.util.Optional<Long> subfamilyIdOpt = DbUtil.findFirstId(entityManager, Subfamily.class);
            if (!subfamilyIdOpt.isPresent()) {
                return "SKIP: No subfamily records found in database for testing";
            }
            
            Subfamily testSubfamily = new Subfamily();
            testSubfamily.setId(subfamilyIdOpt.get());
            
            List<Prodotto> repoResult = prodottoRepository.findBySubFamily(testSubfamily, false);
            List<Prodotto> daoResult = prodottoRepoDao.findBySubFamily(testSubfamily, false);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findBySubFamily results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findBySubFamily() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== ProdottoRepository vs ProdottoRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String findByCodiceResult = testFindByCodice();
            report.append("5. FindByCodice Test: ").append(findByCodiceResult).append("\n");
            
            String findProductIdsForFamilyOfSubfamilyResult = testFindProductIdsForFamilyOfSubfamily();
            report.append("6. FindProductIdsForFamilyOfSubfamily Test: ").append(findProductIdsForFamilyOfSubfamilyResult).append("\n");
            
            String findProductIdsForFamilyResult = testFindProductIdsForFamily();
            report.append("7. FindProductIdsForFamily Test: ").append(findProductIdsForFamilyResult).append("\n");
            
            String findProductIdsForSubfamilyResult = testFindProductIdsForSubfamily();
            report.append("8. FindProductIdsForSubfamily Test: ").append(findProductIdsForSubfamilyResult).append("\n");
            
            String findAllWithArticleResult = testFindAllWithArticle();
            report.append("9. FindAllWithArticle Test: ").append(findAllWithArticleResult).append("\n");
            
            String productsInFamilyResult = testProductsInFamily();
            report.append("10. ProductsInFamily Test: ").append(productsInFamilyResult).append("\n");
            
            String productsInSubfamilyResult = testProductsInSubfamily();
            report.append("11. ProductsInSubfamily Test: ").append(productsInSubfamilyResult).append("\n");
            
            String getFamilySubfamilyIdResult = testGetFamilySubfamilyId();
            report.append("12. GetFamilySubfamilyId Test: ").append(getFamilySubfamilyIdResult).append("\n");
            
            String getProductShortNameResult = testGetProductShortName();
            report.append("13. GetProductShortName Test: ").append(getProductShortNameResult).append("\n");
            
            String getProductNameResult = testGetProductName();
            report.append("14. GetProductName Test: ").append(getProductNameResult).append("\n");
            
            String findBySubFamilyResult = testFindBySubFamily();
            report.append("15. FindBySubFamily Test: ").append(findBySubFamilyResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - ProdottoRepoDao behaves exactly like ProdottoRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
