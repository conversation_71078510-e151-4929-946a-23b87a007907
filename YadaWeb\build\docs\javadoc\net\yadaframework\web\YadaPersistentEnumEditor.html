<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaPersistentEnumEditor (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web, class: YadaPersistentEnumEditor">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web</a></div>
<h1 title="Class YadaPersistentEnumEditor" class="title">Class YadaPersistentEnumEditor</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html" title="class or interface in java.beans" class="external-link">java.beans.PropertyEditorSupport</a>
<div class="inheritance">net.yadaframework.web.YadaPersistentEnumEditor</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditor.html" title="class or interface in java.beans" class="external-link">PropertyEditor</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaPersistentEnumEditor</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html" title="class or interface in java.beans" class="external-link">PropertyEditorSupport</a></span></div>
<div class="block">Used by thymeleaf to convert between string representation and YadaPersistentEnum.
 In can convert any number of YadaLocalEnum implementations, provided that the enum names don't clash.
 Must be set in a controller using an initBinder:
 <pre></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.Class%5B%5D)" class="member-name-link">YadaPersistentEnumEditor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>[]&nbsp;allEnums)</code></div>
<div class="col-last even-row-color">
<div class="block">Create an editor that can convert the specified enum classes.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAsText()" class="member-name-link">getAsText</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAsText(java.lang.String)" class="member-name-link">setAsText</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;enumName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.beans.PropertyEditorSupport">Methods inherited from class&nbsp;java.beans.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html" title="class or interface in java.beans" class="external-link">PropertyEditorSupport</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#addPropertyChangeListener(java.beans.PropertyChangeListener)" title="class or interface in java.beans" class="external-link">addPropertyChangeListener</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#firePropertyChange()" title="class or interface in java.beans" class="external-link">firePropertyChange</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#getCustomEditor()" title="class or interface in java.beans" class="external-link">getCustomEditor</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#getJavaInitializationString()" title="class or interface in java.beans" class="external-link">getJavaInitializationString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#getSource()" title="class or interface in java.beans" class="external-link">getSource</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#getTags()" title="class or interface in java.beans" class="external-link">getTags</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#getValue()" title="class or interface in java.beans" class="external-link">getValue</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#isPaintable()" title="class or interface in java.beans" class="external-link">isPaintable</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#paintValue(java.awt.Graphics,java.awt.Rectangle)" title="class or interface in java.beans" class="external-link">paintValue</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#removePropertyChangeListener(java.beans.PropertyChangeListener)" title="class or interface in java.beans" class="external-link">removePropertyChangeListener</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#setSource(java.lang.Object)" title="class or interface in java.beans" class="external-link">setSource</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#setValue(java.lang.Object)" title="class or interface in java.beans" class="external-link">setValue</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#supportsCustomEditor()" title="class or interface in java.beans" class="external-link">supportsCustomEditor</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.Class[])">
<h3>YadaPersistentEnumEditor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaPersistentEnumEditor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>[]&nbsp;allEnums)</span></div>
<div class="block">Create an editor that can convert the specified enum classes.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>allEnums</code> - array of enum classes that implement YadaLocalEnum</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setAsText(java.lang.String)">
<h3>setAsText</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAsText</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;enumName)</span>
               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditor.html#setAsText(java.lang.String)" title="class or interface in java.beans" class="external-link">setAsText</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditor.html" title="class or interface in java.beans" class="external-link">PropertyEditor</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#setAsText(java.lang.String)" title="class or interface in java.beans" class="external-link">setAsText</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html" title="class or interface in java.beans" class="external-link">PropertyEditorSupport</a></code></dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/IllegalArgumentException.html" title="class or interface in java.lang" class="external-link">IllegalArgumentException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAsText()">
<h3>getAsText</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getAsText</span>()</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditor.html#getAsText()" title="class or interface in java.beans" class="external-link">getAsText</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditor.html" title="class or interface in java.beans" class="external-link">PropertyEditor</a></code></dd>
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html#getAsText()" title="class or interface in java.beans" class="external-link">getAsText</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.desktop/java/beans/PropertyEditorSupport.html" title="class or interface in java.beans" class="external-link">PropertyEditorSupport</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
