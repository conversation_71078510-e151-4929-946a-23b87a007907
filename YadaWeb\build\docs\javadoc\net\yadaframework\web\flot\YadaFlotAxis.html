<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaFlotAxis (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.flot, class: YadaFlotAxis">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.flot</a></div>
<h1 title="Class YadaFlotAxis" class="title">Class YadaFlotAxis</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.web.flot.YadaFlotAxis</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaFlotAxis</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Class to setup X or Y axis options.
 All variables are default set to null (except for <a href="#setShow(java.lang.Boolean)"><code>setShow(Boolean)</code></a>).
 When a value is set to null the json printer will skip this value and
 have no impact on the behaviour of flot.
 <p>
 Time series data
 ================
 Time series are a bit more difficult than scalar data because
 calendars don't follow a simple base 10 system. For many cases, Flot
 abstracts most of this away, but it can still be a bit difficult to
 get the data into Flot. So we'll first discuss the data format.
 <p>
 The time series support in Flot is based on Javascript timestamps,
 i.e. everywhere a time value is expected or handed over, a Javascript
 timestamp number is used. This is a number, not a Date object. A
 Javascript timestamp is the number of milliseconds since January 1,
 1970 00:00:00 UTC. This is almost the same as Unix timestamps, except it's
 in milliseconds, so remember to multiply by 1000!
 <p>
 You can see a timestamp like this
 <pre>
   alert((new Date()).getTime())
 </pre>
 Normally you want the timestamps to be displayed according to a
 certain time zone, usually the time zone in which the data has been
 produced. However, Flot always displays timestamps according to UTC.
 It has to as the only alternative with core Javascript is to interpret
 the timestamps according to the time zone that the visitor is in,
 which means that the ticks will shift unpredictably with the time zone
 and daylight savings of each visitor.
 <p>
 So given that there's no good support for custom time zones in
 Javascript, you'll have to take care of this server-side.
 <p>
 The easiest way to think about it is to pretend that the data
 production time zone is UTC, even if it isn't. So if you have a
 datapoint at 2002-02-20 08:00, you can generate a timestamp for eight
 o'clock UTC even if it really happened eight o'clock UTC+0200.
 <p>
 In PHP you can get an appropriate timestamp with
 'strtotime("2002-02-20 UTC") * 1000', in Python with
 'calendar.timegm(datetime_object.timetuple()) * 1000', in .NET with
 something like:
 <pre>
   public static int GetJavascriptTimestamp(System.DateTime input)
   {
     System.TimeSpan span = new System.TimeSpan(System.DateTime.Parse("1/1/1970").Ticks);
     System.DateTime time = input.Subtract(span);
     return (long)(time.Ticks / 10000);
   }
 </pre>
 Javascript also has some support for parsing date strings, so it is
 possible to generate the timestamps manually client-side.
 <p>
 If you've already got the real UTC timestamp, it's too late to use the
 pretend trick described above. But you can fix up the timestamps by
 adding the time zone offset, e.g. for UTC+0200 you would add 2 hours
 to the UTC timestamp you got. Then it'll look right on the plot. Most
 programming environments have some means of getting the timezone
 offset for a specific date (note that you need to get the offset for
 each individual timestamp to account for daylight savings).
 <p>
 Once you've gotten the timestamps into the data and specified "time"
 as the axis mode, Flot will automatically generate relevant ticks and
 format them. As always, you can tweak the ticks via the "ticks" option
 - just remember that the values should be timestamps (numbers), not
 Date objects.
 <p>
 Tick generation and formatting can also be controlled separately
 through the following axis options:
 <pre>
   minTickSize: array
   timeformat: null or format string
   monthNames: null or array of size 12 of strings
   twelveHourClock: boolean
 </pre>
 Here "timeformat" is a format string to use. You might use it like
 this:
 <pre>
   xaxis: {
     mode: "time"
     timeformat: "%y/%m/%d"
   }
 </pre>
 This will result in tick labels like "2000/12/24". The following
 specifiers are supported
 <pre>
  %h: hours
  %H: hours (left-padded with a zero)
  %M: minutes (left-padded with a zero)
  %S: seconds (left-padded with a zero)
  %d: day of month (1-31), use %0d for zero-padding
  %m: month (1-12), use %0m for zero-padding
  %y: year (four digits)
  %b: month name (customizable)
  %p: am/pm, additionally switches %h/%H to 12 hour instead of 24
  %P: AM/PM (uppercase version of %p)
 </pre>
 Inserting a zero like %0m or %0d means that the specifier will be
 left-padded with a zero if it's only single-digit. So %y-%0m-%0d
 results in unambigious ISO timestamps like 2007-05-10 (for May 10th).
 <p>
 You can customize the month names with the "monthNames" option. For
 instance, for Danish you might specify:
 <pre>
   monthNames: ["jan", "feb", "mar", "apr", "maj", "jun", "jul", "aug", "sep", "okt", "nov", "dec"]
 </pre>
 If you set "twelveHourClock" to true, the autogenerated timestamps
 will use 12 hour AM/PM timestamps instead of 24 hour.
 <p>
 The format string and month names are used by a very simple built-in
 format function that takes a date object, a format string (and
 optionally an array of month names) and returns the formatted string.
 If needed, you can access it as $.plot.formatDate(date, formatstring,
 monthNames) or even replace it with another more advanced function
 from a date library if you're feeling adventurous.
 <p>
 If everything else fails, you can control the formatting by specifying
 a custom tick formatter function as usual. Here's a simple example
 which will format December 24 as 24/12:
 <pre>
   tickFormatter: function (val, axis) {
     var d = new Date(val);
     return d.getUTCDate() + "/" + (d.getUTCMonth() + 1);
   }
 </pre>
 Note that for the time mode "tickSize" and "minTickSize" are a bit
 special in that they are arrays on the form "[value, unit]" where unit
 is one of "second", "minute", "hour", "day", "month" and "year". So
 you can specify
 <pre>
   minTickSize: [1, "month"]
 </pre>
 to get a tick interval size of at least 1 month and correspondingly,
 if axis.tickSize is [2, "day"] in the tick formatter, the ticks have
 been produced with two days in-between.
 <b>JSON Data format for XAxis/YAxis:</b>
 <pre>
  xaxis, yaxis: {
    show: null or true/false
    position: "bottom" or "top" or "left" or "right"
    mode: null or "time"

    color: null or color spec
    tickColor: null or color spec
    font: null or font spec object

    min: null or number
    max: null or number
    autoscaleMargin: null or number

    transform: null or fn: number -&gt; number
    inverseTransform: null or fn: number -&gt; number

    ticks: null or number or ticks array or (fn: axis -&gt; ticks array)
    tickSize: number or array
    minTickSize: number or array
    tickFormatter: (fn: number, object -&gt; string) or string
    tickDecimals: null or number

    labelWidth: null or number
    labelHeight: null or number
    reserveSpace: null or true

    tickLength: null or number

    alignTicksWithAxis: null or number
 }
 </pre>
 <p>
 This class has been constructed as per flot API documentation.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><A href="http://flot.googlecode.com/svn/trunk/API.txt" target="_blank">Flot API.txt</A></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaFlotAxis</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAlignTicksWithAxis()" class="member-name-link">getAlignTicksWithAxis</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAutoscaleMargin()" class="member-name-link">getAutoscaleMargin</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getColor()" class="member-name-link">getColor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFont()" class="member-name-link">getFont</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInverseTransform()" class="member-name-link">getInverseTransform</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLabelHeight()" class="member-name-link">getLabelHeight</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLabelWidth()" class="member-name-link">getLabelWidth</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMax()" class="member-name-link">getMax</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMin()" class="member-name-link">getMin</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMinTickSize()" class="member-name-link">getMinTickSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMode()" class="member-name-link">getMode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPosition()" class="member-name-link">getPosition</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getReserveSpace()" class="member-name-link">getReserveSpace</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getShow()" class="member-name-link">getShow</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTickColor()" class="member-name-link">getTickColor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTickDecimals()" class="member-name-link">getTickDecimals</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTickFormatter()" class="member-name-link">getTickFormatter</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTickLength()" class="member-name-link">getTickLength</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTicks()" class="member-name-link">getTicks</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTickSize()" class="member-name-link">getTickSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTimeformat()" class="member-name-link">getTimeformat</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTransform()" class="member-name-link">getTransform</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAlignTicksWithAxis(java.lang.Integer)" class="member-name-link">setAlignTicksWithAxis</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;alignTicksWithAxis)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If you set "alignTicksWithAxis" to the number of another axis, e.g.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAutoscaleMargin(java.lang.Double)" class="member-name-link">setAutoscaleMargin</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;autoscaleMargin)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The "autoscaleMargin" is a bit esoteric: it's the fraction of margin
 that the scaling algorithm will add to avoid that the outermost points
 ends up on the grid border.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setColor(java.lang.String)" class="member-name-link">setColor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;color)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The "color" option determines the color of the labels and ticks for
 the axis (default is the grid color).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setFont(java.lang.String)" class="member-name-link">setFont</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;font)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setInverseTransform(java.lang.String)" class="member-name-link">setInverseTransform</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inverseTransform)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLabelHeight(java.lang.Integer)" class="member-name-link">setLabelHeight</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;labelHeight)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLabelSize(java.lang.Integer,java.lang.Integer)" class="member-name-link">setLabelSize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;labelWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;labelHeight)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"labelWidth" and "labelHeight" specifies a fixed size of the tick
 labels in pixels.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLabelWidth(java.lang.Integer)" class="member-name-link">setLabelWidth</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;labelWidth)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMax(java.lang.Double)" class="member-name-link">setMax</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;max)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMin(java.lang.Double)" class="member-name-link">setMin</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;min)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMinTickSize(java.lang.String)" class="member-name-link">setMinTickSize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;minTickSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMode(java.lang.String)" class="member-name-link">setMode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;mode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPosition(java.lang.String)" class="member-name-link">setPosition</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;position)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The "position" option specifies where the axis is placed, bottom or
 top for x axes, left or right for y axes.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRange(java.lang.Double,java.lang.Double)" class="member-name-link">setRange</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;min,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;max)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setRange(java.lang.Double,java.lang.Double,java.lang.Double)" class="member-name-link">setRange</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;min,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;max,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;tickSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The options "min"/"max" are the precise minimum/maximum value on the
 scale.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setReserveSpace(java.lang.Integer)" class="member-name-link">setReserveSpace</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;reserveSpace)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShow(java.lang.Boolean)" class="member-name-link">setShow</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;show)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If you don't set the "show" option (i.e.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTickColor(java.lang.String)" class="member-name-link">setTickColor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tickColor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTickDecimals(java.lang.Integer)" class="member-name-link">setTickDecimals</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;tickDecimals)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">You can control how the ticks look like with "tickDecimals", the
 number of decimals to display (default is auto-detected).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTickFormatter(java.lang.String)" class="member-name-link">setTickFormatter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tickFormatter)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Alternatively, for ultimate control over how ticks are formatted you can
 provide a function to "tickFormatter".</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTickLength(java.lang.Integer)" class="member-name-link">setTickLength</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;tickLength)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"tickLength" is the length of the tick lines in pixels.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTicks(java.lang.String)" class="member-name-link">setTicks</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ticks)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If you don't specify any ticks, a tick generator algorithm will make
 some for you.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTickSize(java.lang.String)" class="member-name-link">setTickSize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tickSize)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTimeformat(java.lang.String)" class="member-name-link">setTimeformat</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;timeformat)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the preferred time format for the axis.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTransform(java.lang.String)" class="member-name-link">setTransform</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;transform)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"transform" and "inverseTransform" are callbacks you can put in to
 change the way the data is drawn.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaFlotAxis</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaFlotAxis</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setTimeformat(java.lang.String)">
<h3>setTimeformat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTimeformat</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;timeformat)</span></div>
<div class="block">Sets the preferred time format for the axis. This setter will automatically
 change the mode to "time".
 Use the following guide to specify the format as a <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link"><code>String</code></a>:
 <pre>
   %h: hours
   %H: hours (left-padded with a zero)
   %M: minutes (left-padded with a zero)
   %S: seconds (left-padded with a zero)
   %d: day of month (1-31), use %0d for zero-padding
   %m: month (1-12), use %0m for zero-padding
   %y: year (four digits)
   %b: month name (customizable)
   %p: am/pm, additionally switches %h/%H to 12 hour instead of 24
   %P: AM/PM (uppercase version of %p)
 </pre>
   For example, specifying timeformat as "%y/%m/%d" will
   result in tick labels like "2000/12/24".
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>timeformat</code> - What time format given axis should be displayed in.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><A href="http://flot.googlecode.com/svn/trunk/API.txt" target="_blank">Flot API.txt</A></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRange(java.lang.Double,java.lang.Double,java.lang.Double)">
<h3>setRange</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRange</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;min,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;max,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;tickSize)</span></div>
<div class="block">The options "min"/"max" are the precise minimum/maximum value on the
 scale. If you don't specify either of them, a value will automatically
 be chosen based on the minimum/maximum data values. Note that Flot
 always examines all the data values you feed to it, even if a
 restriction on another axis may make some of them invisible (this
 makes interactive use more stable).
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>min</code> - null or number</dd>
<dd><code>max</code> - null or number</dd>
<dd><code>tickSize</code> - number</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRange(java.lang.Double,java.lang.Double)">
<h3>setRange</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setRange</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;min,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;max)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>min</code> - null or number</dd>
<dd><code>max</code> - null or number</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li>
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#setRange(Long, Long, Long)</code></pre>
</details>
</li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setShow(java.lang.Boolean)">
<h3>setShow</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShow</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;show)</span></div>
<div class="block">If you don't set the "show" option (i.e. it is null), visibility is
 auto-detected, i.e. the axis will show up if there's data associated
 with it. You can override this by setting the "show" option to true or
 false.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>show</code> - null or true/false</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPosition(java.lang.String)">
<h3>setPosition</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPosition</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;position)</span></div>
<div class="block">The "position" option specifies where the axis is placed, bottom or
 top for x axes, left or right for y axes. The "mode" option determines
 how the data is interpreted, the default of null means as decimal
 numbers. Use "time" for time series data, see the time series data
 section.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>position</code> - "bottom" or "top" or "left" or "right"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMode(java.lang.String)">
<h3>setMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMode</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;mode)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mode</code> - TODO</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setColor(java.lang.String)">
<h3>setColor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setColor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;color)</span></div>
<div class="block">The "color" option determines the color of the labels and ticks for
 the axis (default is the grid color). For more fine-grained control
 you can also set the color of the ticks separately with "tickColor"
 (otherwise it's autogenerated as the base color with some transparency).
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>color</code> - null or color spec</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTickColor(java.lang.String)">
<h3>setTickColor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTickColor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tickColor)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tickColor</code> - the tickColor to set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setFont(java.lang.String)">
<h3>setFont</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setFont</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;font)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>font</code> - the font to set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMin(java.lang.Double)">
<h3>setMin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMin</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;min)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>min</code> - null or number</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setRange(java.lang.Double,java.lang.Double,java.lang.Double)"><code>setRange(java.lang.Double, java.lang.Double, java.lang.Double)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMax(java.lang.Double)">
<h3>setMax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMax</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;max)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>max</code> - null or number</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setRange(java.lang.Double,java.lang.Double,java.lang.Double)"><code>setRange(java.lang.Double, java.lang.Double, java.lang.Double)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAutoscaleMargin(java.lang.Double)">
<h3>setAutoscaleMargin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAutoscaleMargin</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a>&nbsp;autoscaleMargin)</span></div>
<div class="block">The "autoscaleMargin" is a bit esoteric: it's the fraction of margin
 that the scaling algorithm will add to avoid that the outermost points
 ends up on the grid border. Note that this margin is only applied when
 a min or max value is not explicitly set. If a margin is specified,
 the plot will furthermore extend the axis end-point to the nearest
 whole tick. The default value is "null" for the x axes and 0.02 for y
 axes which seems appropriate for most cases.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>autoscaleMargin</code> - null or number</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTransform(java.lang.String)">
<h3>setTransform</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTransform</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;transform)</span></div>
<div class="block">"transform" and "inverseTransform" are callbacks you can put in to
 change the way the data is drawn. You can design a function to
 compress or expand certain parts of the axis non-linearly, e.g.
 suppress weekends or compress far away points with a logarithm or some
 other means. When Flot draws the plot, each value is first put through
 the transform function. Here's an example, the x axis can be turned
 into a natural logarithm axis with the following code:
 <pre>
   xaxis: {
     transform: function (v) { return Math.log(v); },
     inverseTransform: function (v) { return Math.exp(v); }
   }
 </pre>
 Similarly, for reversing the y axis so the values appear in inverse
 order:
 <pre>
   yaxis: {
     transform: function (v) { return -v; },
     inverseTransform: function (v) { return -v; }
   }
 </pre>
 Note that for finding extrema, Flot assumes that the transform
 function does not reorder values (it should be monotone).
 <p>
 The inverseTransform is simply the inverse of the transform function
 (so v == inverseTransform(transform(v)) for all relevant v). It is
 required for converting from canvas coordinates to data coordinates,
 e.g. for a mouse interaction where a certain pixel is clicked. If you
 don't use any interactive features of Flot, you may not need it.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>transform</code> - null or fn: number -&gt; number</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setInverseTransform(java.lang.String)">
<h3>setInverseTransform</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setInverseTransform</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;inverseTransform)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inverseTransform</code> - null or fn: number -&gt; number</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setTransform(java.lang.String)"><code>setTransform(java.lang.String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTicks(java.lang.String)">
<h3>setTicks</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTicks</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ticks)</span></div>
<div class="block">If you don't specify any ticks, a tick generator algorithm will make
 some for you. The algorithm has two passes. It first estimates how
 many ticks would be reasonable and uses this number to compute a nice
 round tick interval size. Then it generates the ticks.
 <p>
 You can specify how many ticks the algorithm aims for by setting
 "ticks" to a number. The algorithm always tries to generate reasonably
 round tick values so even if you ask for three ticks, you might get
 five if that fits better with the rounding. If you don't want any
 ticks at all, set "ticks" to 0 or an empty array.
 <p>
 Another option is to skip the rounding part and directly set the tick
 interval size with "<a href="#setTickSize(java.lang.String)"><code>tickSize</code></a>". If you set
 it to 2, you'll get ticks at 2, 4, 6, etc.
 <p>
 Alternatively, you can specify that you just don't want
 ticks at a size less than a specific tick size with
 "<a href="#setMinTickSize(java.lang.String)"><code>minTickSize</code></a>".
 <p>
 Note that for time series, the format is an array like [2, "month"],
 see the next section.
 <p>
 If you want to completely override the tick algorithm, you can specify
 an array for "ticks", either like this:
 <pre>  ticks: [0, 1.2, 2.4]</pre>
 Or like this where the labels are also customized:
 <pre>  ticks: [[0, "zero"], [1.2, "one mark"], [2.4, "two marks"]]</pre>
 You can mix the two if you like.
 <p>
 For extra flexibility you can specify a function as the "ticks"
 parameter. The function will be called with an object with the axis
 min and max and should return a ticks array. Here's a simplistic tick
 generator that spits out intervals of pi, suitable for use on the x
 axis for trigonometric functions:
 <pre>
   function piTickGenerator(axis) {
     var res = [], i = Math.floor(axis.min / Math.PI);
     do {
       var v = i * Math.PI;
       res.push([v, i + "π"]);
       ++i;
     } while (v <span class="invalid-tag">invalid input: '&lt;'</span> axis.max);

     return res;
   }
 </pre></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ticks</code> - the ticks to set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTickSize(java.lang.String)">
<h3>setTickSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTickSize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tickSize)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tickSize</code> - the tickSize to set</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setTicks(java.lang.String)"><code>setTicks(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMinTickSize(java.lang.String)">
<h3>setMinTickSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMinTickSize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;minTickSize)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minTickSize</code> - the minTickSize to set</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#setTicks(java.lang.String)"><code>setTicks(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTickFormatter(java.lang.String)">
<h3>setTickFormatter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTickFormatter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tickFormatter)</span></div>
<div class="block">Alternatively, for ultimate control over how ticks are formatted you can
 provide a function to "tickFormatter". The function is passed two
 parameters, the tick value and an axis object with information, and
 should return a string. The default formatter looks like this:
 <pre>
   function formatter(val, axis) {
     return val.toFixed(axis.tickDecimals);
   }
 </pre>
 The axis object has "min" and "max" with the range of the axis,
 "tickDecimals" with the number of decimals to round the value to and
 "tickSize" with the size of the interval between ticks as calculated
 by the automatic axis scaling algorithm (or specified by you). Here's
 an example of a custom formatter:
 <pre>
   function suffixFormatter(val, axis) {
     if (val &gt; 1000000)
       return (val / 1000000).toFixed(axis.tickDecimals) + " MB";
     else if (val &gt; 1000)
       return (val / 1000).toFixed(axis.tickDecimals) + " kB";
     else
       return val.toFixed(axis.tickDecimals) + " B";
   }
 </pre></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tickFormatter</code> - the tickFormatter to set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTickDecimals(java.lang.Integer)">
<h3>setTickDecimals</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTickDecimals</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;tickDecimals)</span></div>
<div class="block">You can control how the ticks look like with "tickDecimals", the
 number of decimals to display (default is auto-detected).
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tickDecimals</code> - the tickDecimals to set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLabelSize(java.lang.Integer,java.lang.Integer)">
<h3>setLabelSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLabelSize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;labelWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;labelHeight)</span></div>
<div class="block">"labelWidth" and "labelHeight" specifies a fixed size of the tick
 labels in pixels. They're useful in case you need to align several
 plots. "reserveSpace" means that even if an axis isn't shown, Flot
 should reserve space for it - it is useful in combination with
 labelWidth and labelHeight for aligning multi-axis charts.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>labelWidth</code> - TODO</dd>
<dd><code>labelHeight</code> - TODO</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLabelWidth(java.lang.Integer)">
<h3>setLabelWidth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLabelWidth</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;labelWidth)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>labelWidth</code> - the labelWidth to set</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setLabelSize(java.lang.Integer,java.lang.Integer)"><code>setLabelSize(Integer, Integer)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLabelHeight(java.lang.Integer)">
<h3>setLabelHeight</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLabelHeight</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;labelHeight)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>labelHeight</code> - the labelHeight to set</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#setLabelSize(java.lang.Integer,java.lang.Integer)"><code>setLabelSize(Integer, Integer)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setReserveSpace(java.lang.Integer)">
<h3>setReserveSpace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setReserveSpace</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;reserveSpace)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>reserveSpace</code> - the reserveSpace to set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTickLength(java.lang.Integer)">
<h3>setTickLength</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setTickLength</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;tickLength)</span></div>
<div class="block">"tickLength" is the length of the tick lines in pixels. By default, the
 innermost axes will have ticks that extend all across the plot, while
 any extra axes use small ticks. A value of null means use the default,
 while a number means small ticks of that length - set it to 0 to hide
 the lines completely.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tickLength</code> - the tickLength to set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAlignTicksWithAxis(java.lang.Integer)">
<h3>setAlignTicksWithAxis</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAlignTicksWithAxis</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;alignTicksWithAxis)</span></div>
<div class="block">If you set "alignTicksWithAxis" to the number of another axis, e.g.
 alignTicksWithAxis: 1, Flot will ensure that the autogenerated ticks
 of this axis are aligned with the ticks of the other axis. This may
 improve the looks, e.g. if you have one y axis to the left and one to
 the right, because the grid lines will then match the ticks in both
 ends. The trade-off is that the forced ticks won't necessarily be at
 natural places.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>alignTicksWithAxis</code> - the alignTicksWithAxis to set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getShow()">
<h3>getShow</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getShow</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the show</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPosition()">
<h3>getPosition</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPosition</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the position</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMode()">
<h3>getMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getMode</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the mode</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTimeformat()">
<h3>getTimeformat</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTimeformat</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the timeformat</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getColor()">
<h3>getColor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getColor</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the color</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTickColor()">
<h3>getTickColor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTickColor</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the tickColor</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFont()">
<h3>getFont</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFont</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the font</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMin()">
<h3>getMin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a></span>&nbsp;<span class="element-name">getMin</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the min</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMax()">
<h3>getMax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a></span>&nbsp;<span class="element-name">getMax</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the max</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAutoscaleMargin()">
<h3>getAutoscaleMargin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Double.html" title="class or interface in java.lang" class="external-link">Double</a></span>&nbsp;<span class="element-name">getAutoscaleMargin</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the autoscaleMargin</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTransform()">
<h3>getTransform</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTransform</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the transform</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInverseTransform()">
<h3>getInverseTransform</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getInverseTransform</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the inverseTransform</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTicks()">
<h3>getTicks</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTicks</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the ticks</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTickSize()">
<h3>getTickSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTickSize</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the tickSize</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMinTickSize()">
<h3>getMinTickSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getMinTickSize</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the minTickSize</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTickFormatter()">
<h3>getTickFormatter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTickFormatter</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the tickFormatter</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTickDecimals()">
<h3>getTickDecimals</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getTickDecimals</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the tickDecimals</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLabelWidth()">
<h3>getLabelWidth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getLabelWidth</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the labelWidth</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLabelHeight()">
<h3>getLabelHeight</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getLabelHeight</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the labelHeight</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getReserveSpace()">
<h3>getReserveSpace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getReserveSpace</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the reserveSpace</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTickLength()">
<h3>getTickLength</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getTickLength</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the tickLength</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAlignTicksWithAxis()">
<h3>getAlignTicksWithAxis</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getAlignTicksWithAxis</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the alignTicksWithAxis</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
