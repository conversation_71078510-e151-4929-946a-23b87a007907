# Aggiunta di due nuove classi localstring

create table CustomButtonLink (id bigint not null auto_increment, localeCode varchar(5), value longtext, Subfamily_id bigint, primary key (id)) engine=InnoDB;
create table CustomButtonName (id bigint not null auto_increment, localeCode varchar(5), value longtext, Subfamily_id bigint, primary key (id)) engine=InnoDB;
alter table CustomButtonLink add constraint FK4vd9ifh5mu4eenmw34jkyyspy foreign key (Subfamily_id) references Subfamily (id);
alter table CustomButtonName add constraint FK7r4mloxwwx0nccmgb4jqofo90 foreign key (Subfamily_id) references Subfamily (id);
