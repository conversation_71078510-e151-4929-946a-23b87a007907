# New attributes for PageModule

ALTER TABLE PageModule
ADD COLUMN data1 varchar(255) after id,
ADD COLUMN data2 varchar(255) after data1,
ADD COLUMN data3 varchar(255) after data2,
ADD COLUMN data4 varchar(255) after data3,
ADD COLUMN flag1 bit not null after enabled,
ADD COLUMN flag2 bit not null after flag1,
ADD COLUMN linkUrl varchar(256) after flag2;

create table PageModule_linkText (PageModule_id bigint not null, linkText varchar(256), locale varchar(32) not null, primary key (PageModule_id, locale)) engine=InnoDB;

alter table PageModule_linkText add constraint FKtbvffyg6b6evrpvfm7k0w4qbl foreign key (PageModule_id) references PageModule (id);

