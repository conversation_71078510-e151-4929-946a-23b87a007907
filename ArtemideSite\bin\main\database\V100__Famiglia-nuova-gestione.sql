# modifica per avere la Famiglia con la nuova gestione.

rename table FamigliaNome to Famiglia_name;
alter table Famiglia_name drop column id;
alter table Famiglia_name CHANGE value name varchar(255) not null;
alter table Famiglia_name CHANGE localeCode locale varchar(32) not null;

alter table Famiglia add column  version bigint not null;

create table FamilyImages (id bigint not null auto_increment, version bigint not null, bigImage_id bigint, defaultImage_id bigint, largeImage_id bigint, tallImage_id bigint, Famiglia_id bigint, typology varchar(255), primary key (id)) engine=InnoDB;
alter table FamilyImages add constraint FKkd7ud5f0n90r3pk11060fcgmr foreign key (bigImage_id) references YadaAttachedFile (id);
alter table FamilyImages add constraint FKivqh6ghl5m87n2xqxhv76wgdf foreign key (defaultImage_id) references YadaAttachedFile (id);
alter table FamilyImages add constraint FK4q8nkjfr5lrr3ldob73ssu746 foreign key (largeImage_id) references YadaAttachedFile (id);
alter table FamilyImages add constraint FKfkcidmy4p6itp9um490nqcb9p foreign key (tallImage_id) references YadaAttachedFile (id);
alter table FamilyImages add constraint FK8ynq9dvh1jpnxbdrej4yvtj1q foreign key (Famiglia_id) references Famiglia (id);


