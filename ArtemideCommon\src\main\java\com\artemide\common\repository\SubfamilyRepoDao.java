package com.artemide.common.repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.Famiglia;
import com.yr.entity.Subfamily;

@Repository
@Transactional(readOnly = true)
public class SubfamilyRepoDao {

    @PersistenceContext EntityManager em;

    /**
     * Prende il nome localizzato
     * @param id
     * @param localeString string nel formato xx_XX dato da locale.toString()
     * @return
     */
    public String findName(Long id, String localeString) {
        String sql = "select n.value from Subfamily s left join s.names n where s.id = :id and KEY(n) = :localeString";
        List<String> resultList = em.createQuery(sql, String.class)
            .setParameter("id", id)
            .setParameter("localeString", localeString)
            .setMaxResults(1)
            .getResultList();
        return resultList.isEmpty() ? null : resultList.get(0);
    }

    /**
     * Conta quante sottofamiglie ci sono in una famiglia
     * @param famigliaId
     * @param preview
     * @return
     */
    public int countByFamiglia(Long famigliaId, boolean preview) {
        String sql = "select count(*) from Subfamily s join s.famiglia f where (s.published=true or s.published != :preview) and f.id = :famigliaId and (f.published=true or f.published != :preview)";
        return em.createQuery(sql, Long.class)
            .setParameter("famigliaId", famigliaId)
            .setParameter("preview", preview)
            .getSingleResult().intValue();
    }

    /**
     * Controlla il colore dell'immagine del logo App Compatible
     * @param famigliaId
     * @param preview
     * @return
     */
    public int appCompatibleColorFromFamiglia(Long famigliaId, boolean preview) {
        String sql = "select appCompatibleColor from Famiglia where id = :famigliaId and (published=true or published != :preview)";
        return em.createQuery(sql, Integer.class)
            .setParameter("famigliaId", famigliaId)
            .setParameter("preview", preview)
            .getSingleResult();
    }

    /**
     * Ritorna gli id di sottofamiglie appartenenti alla data famiglia, senza check sul published
     * @param familyId
     * @return lista di id di sottofamiglie
     */
    @SuppressWarnings("unchecked")
    public Set<Long> findByFamiglia(Long familyId) {
        String sql = "select s.id from Subfamily s left join s.famiglia f where f.id = :familyId";
        return (Set<Long>) em.createQuery(sql)
            .setParameter("familyId", familyId)
            .getResultList();
    }

    /**
     * Prende le subfamily della famiglia, ordinate come impostato nel CMS, controllando che siano published
     * @param famiglia
     * @param preview se true, prende anche le published a false
     * @return
     */
    public List<Subfamily> findByFamiglia(Famiglia famiglia, boolean preview) {
        String sql = "select s from Subfamily s join s.famiglia f where (s.published=true or s.published != :preview) and f = :famiglia and (f.published=true or f.published != :preview) order by s.pos";
        return em.createQuery(sql, Subfamily.class)
            .setParameter("famiglia", famiglia)
            .setParameter("preview", preview)
            .getResultList();
    }

    /**
     * Restituisce una lista di Subfamilies se questi sono state pubblicate
     * @return lista di Subfamily
     */
    public List<Subfamily> findAllSorted(String locale) {
        String sql = "select s from Subfamily s join s.names names where s.published = true and KEY(names) = :locale order by names.value";
        return em.createQuery(sql, Subfamily.class)
            .setParameter("locale", locale)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public int setPublishedStatusForSubfamilies(Collection<Long> subfamilyIds, boolean published) {
        String sql = "update Subfamily s set s.published = :published where s.id in :ids";
        return em.createQuery(sql)
            .setParameter("ids", subfamilyIds)
            .setParameter("published", published)
            .executeUpdate();
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Subfamily", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Subfamily save(Subfamily entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Subfamily> findById(Long entityId) {
        Subfamily result = em.find(Subfamily.class, entityId);
        return Optional.ofNullable(result);
    }

    public Subfamily findOne(Long entityId) {
        return em.find(Subfamily.class, entityId);
    }

    public List<Subfamily> findAll() {
        return em.createQuery("from Subfamily", Subfamily.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Subfamily> batchToSave) {
        for (Subfamily entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Subfamily entity) {
        em.remove(entity);
    }
}
