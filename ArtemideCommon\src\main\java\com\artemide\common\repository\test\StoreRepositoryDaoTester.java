package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.repository.StoreRepoDao;
import com.artemide.common.repository.StoreRepository;
import com.yr.entity.Store;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies StoreRepoDao behaves exactly like StoreRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class StoreRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private StoreRepository storeRepository;

    @Autowired
    private StoreRepoDao storeRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    Store entity = entityManager.find(Store.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            entityManager.createQuery("DELETE FROM Store s WHERE s.nome LIKE 'TEST_%' OR s.nome LIKE 'INTEGRATION_%'")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private Store createTestEntity(String nome) {
        Store entity = new Store();
        entity.setNome(nome); // Store has setNome(), not setName()
        return entity;
    }

    private void trackEntity(Store entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = storeRepository.count();
            long initialDaoCount = storeRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            Store testEntity = createTestEntity("INTEGRATION_SAVE_001");

            Store repoSaved = storeRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            Store testEntity2 = createTestEntity("INTEGRATION_SAVE_002");
            Store daoSaved = storeRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<Store> repoFound = storeRepository.findById(repoSaved.getId());
            Optional<Store> daoFoundOptional = storeRepoDao.findById(daoSaved.getId());
            Store daoFoundDirect = storeRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<Store> repoResults = storeRepository.findAll();
            List<Store> daoResults = storeRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            Store entityForRepo = createTestEntity("INTEGRATION_DELETE_001");
            Store entityForDao = createTestEntity("INTEGRATION_DELETE_002");
            
            Store savedForRepo = storeRepository.save(entityForRepo);
            Store savedForDao = storeRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!storeRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!storeRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            storeRepository.delete(savedForRepo);
            storeRepoDao.delete(savedForDao);
            entityManager.flush();

            if (storeRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (storeRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testGetNazioniForTipologia() {
        setUp();
        try {
            // Test with existing tipologia
            String[] testTipologia = {"showroom", "web_partner"};
            List<Object[]> repoResult = storeRepository.getNazioniForTipologia(testTipologia);
            List<Object[]> daoResult = storeRepoDao.getNazioniForTipologia(testTipologia);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: getNazioniForTipologia results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: getNazioniForTipologia() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindFirstByIndirizzoIndirizzoLinea1AndIndirizzoCitta() {
        setUp();
        try {
            // Get a real Store from database to use its address data
            Optional<Long> storeIdOpt = DbUtil.findFirstId(entityManager, Store.class);
            if (!storeIdOpt.isPresent()) {
                return "SKIP: No Store records found for address test";
            }
            
            Store testStore = entityManager.find(Store.class, storeIdOpt.get());
            if (testStore == null || testStore.getIndirizzo() == null) {
                return "SKIP: No Store with address found for testing";
            }
            
            String testAddress = testStore.getIndirizzo().getIndirizzoLinea1();
            String testCity = testStore.getIndirizzo().getCitta();
            
            if (testAddress == null || testCity == null) {
                return "SKIP: Store address data incomplete for testing";
            }
            
            Optional<Store> repoResult = storeRepository.findFirstByIndirizzoIndirizzoLinea1AndIndirizzoCitta(testAddress, testCity);
            Optional<Store> daoResult = storeRepoDao.findFirstByIndirizzoIndirizzoLinea1AndIndirizzoCitta(testAddress, testCity);

            if (repoResult.isPresent() != daoResult.isPresent()) {
                return "FAIL: findFirstByIndirizzoIndirizzoLinea1AndIndirizzoCitta results don't match - Repository present: " + repoResult.isPresent() + ", DAO present: " + daoResult.isPresent();
            }
            
            return "PASS: findFirstByIndirizzoIndirizzoLinea1AndIndirizzoCitta() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindByHiddenTrue() {
        setUp();
        try {
            List<Store> repoResult = storeRepository.findByHiddenTrue();
            List<Store> daoResult = storeRepoDao.findByHiddenTrue();

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findByHiddenTrue results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findByHiddenTrue() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindByNations() {
        setUp();
        try {
            // Test with common nation codes - these are likely to exist
            List<String> testNations = java.util.Arrays.asList("IT", "FR", "DE");
            List<Store> repoResult = storeRepository.findByNations(testNations);
            List<Store> daoResult = storeRepoDao.findByNations(testNations);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findByNations results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findByNations() test successful";
        } finally {
            tearDown();
        }
    }

    @Transactional(readOnly = false)
    public String testDeleteAllByHiddenTrue() {
        setUp();
        try {
            // Create test entities with hidden=true
            Store testEntity1 = createTestEntity("INTEGRATION_HIDDEN_001");
            Store testEntity2 = createTestEntity("INTEGRATION_HIDDEN_002");
            
            Store saved1 = storeRepository.save(testEntity1);
            Store saved2 = storeRepository.save(testEntity2);
            trackEntity(saved1);
            trackEntity(saved2);
            entityManager.flush();

            // Test the method - this deletes records
            storeRepository.deleteAllByHiddenTrue();
            storeRepoDao.deleteAllByHiddenTrue();
            entityManager.flush();
            
            // Both methods should execute without error
            return "PASS: deleteAllByHiddenTrue() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== StoreRepository vs StoreRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String getNazioniForTipologiaResult = testGetNazioniForTipologia();
            report.append("5. GetNazioniForTipologia Test: ").append(getNazioniForTipologiaResult).append("\n");
            
            String findFirstByAddressResult = testFindFirstByIndirizzoIndirizzoLinea1AndIndirizzoCitta();
            report.append("6. FindFirstByIndirizzoIndirizzoLinea1AndIndirizzoCitta Test: ").append(findFirstByAddressResult).append("\n");
            
            String findByHiddenTrueResult = testFindByHiddenTrue();
            report.append("7. FindByHiddenTrue Test: ").append(findByHiddenTrueResult).append("\n");
            
            String findByNationsResult = testFindByNations();
            report.append("8. FindByNations Test: ").append(findByNationsResult).append("\n");
            
            String deleteAllByHiddenTrueResult = testDeleteAllByHiddenTrue();
            report.append("9. DeleteAllByHiddenTrue Test: ").append(deleteAllByHiddenTrueResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - StoreRepoDao behaves exactly like StoreRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
