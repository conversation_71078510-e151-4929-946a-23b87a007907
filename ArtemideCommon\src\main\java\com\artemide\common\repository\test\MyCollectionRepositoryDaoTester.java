package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.MyCollection;
import com.artemide.common.persistence.entity.UserProfile;
import com.artemide.common.repository.MyCollectionDao;
import com.artemide.common.repository.MyCollectionRepository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies MyCollectionDao behaves exactly like MyCollectionRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class MyCollectionRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private MyCollectionRepository myCollectionRepository;

    @Autowired
    private MyCollectionDao myCollectionDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    MyCollection entity = entityManager.find(MyCollection.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            entityManager.createQuery("DELETE FROM MyCollection m WHERE m.title LIKE 'TEST_%' OR m.title LIKE 'INTEGRATION_%'")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private MyCollection createTestEntity(String title) {
        MyCollection entity = new MyCollection();
        entity.setTitle(title); // MyCollection has setTitle(String) method
        return entity;
    }

    private void trackEntity(MyCollection entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = myCollectionRepository.count();
            long initialDaoCount = myCollectionDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            MyCollection testEntity = createTestEntity("INTEGRATION_SAVE_001");

            MyCollection repoSaved = myCollectionRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            MyCollection testEntity2 = createTestEntity("INTEGRATION_SAVE_002");
            MyCollection daoSaved = myCollectionDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<MyCollection> repoFound = myCollectionRepository.findById(repoSaved.getId());
            Optional<MyCollection> daoFoundOptional = myCollectionDao.findById(daoSaved.getId());
            MyCollection daoFoundDirect = myCollectionDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<MyCollection> repoResults = myCollectionRepository.findAll();
            List<MyCollection> daoResults = myCollectionDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            MyCollection entityForRepo = createTestEntity("INTEGRATION_DELETE_001");
            MyCollection entityForDao = createTestEntity("INTEGRATION_DELETE_002");
            
            MyCollection savedForRepo = myCollectionRepository.save(entityForRepo);
            MyCollection savedForDao = myCollectionDao.save(entityForDao);
            entityManager.flush();
            
            if (!myCollectionRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!myCollectionDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            myCollectionRepository.delete(savedForRepo);
            myCollectionDao.delete(savedForDao);
            entityManager.flush();

            if (myCollectionRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (myCollectionDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindOneByIdAndUserProfileId() {
        setUp();
        try {
            // Get first available collection ID using DbUtil
            java.util.Optional<Long> collectionIdOpt = DbUtil.findFirstId(entityManager, MyCollection.class);
            if (!collectionIdOpt.isPresent()) {
                return "SKIP: No MyCollection records found in database for testing";
            }
            long testCollectionId = collectionIdOpt.get();
            
            // Get first available user profile ID using DbUtil
            java.util.Optional<Long> userProfileIdOpt = DbUtil.findFirstId(entityManager, UserProfile.class);
            if (!userProfileIdOpt.isPresent()) {
                return "SKIP: No UserProfile records found in database for testing";
            }
            Long testUserProfileId = userProfileIdOpt.get();
            
            MyCollection repoResult = myCollectionRepository.findOneByIdAndUserProfileId(testCollectionId, testUserProfileId);
            MyCollection daoResult = myCollectionDao.findOneByIdAndUserProfileId(testCollectionId, testUserProfileId);

            if (repoResult == null && daoResult == null) {
                return "PASS: findOneByIdAndUserProfileId() test successful - both return null";
            }
            if (repoResult == null || daoResult == null) {
                return "FAIL: Repository and DAO should return same result - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            if (!repoResult.getId().equals(daoResult.getId())) {
                return "FAIL: Repository and DAO should return same entity - Repository ID: " + repoResult.getId() + ", DAO ID: " + daoResult.getId();
            }
            
            return "PASS: findOneByIdAndUserProfileId() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== MyCollectionRepository vs MyCollectionDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String findOneByIdAndUserProfileIdResult = testFindOneByIdAndUserProfileId();
            report.append("5. FindOneByIdAndUserProfileId Test: ").append(findOneByIdAndUserProfileIdResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - MyCollectionDao behaves exactly like MyCollectionRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
