<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="net.yadaframework.components.YadaUtilTest" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-09-18T16:14:08.170Z" hostname="DREAMPC2" time="0.155">
  <properties/>
  <testcase name="ensureSafeFilename_WithSpecialCharacters_ReturnsSafeString()" classname="net.yadaframework.components.YadaUtilTest" time="0.146"/>
  <testcase name="ensureSafeFilename_WithDiacritics_RemovesDiacritics()" classname="net.yadaframework.components.YadaUtilTest" time="0.002"/>
  <testcase name="ensureSafeFilename_WithLowerCase_ConvertsToLowerCase()" classname="net.yadaframework.components.YadaUtilTest" time="0.002"/>
  <testcase name="ensureSafeFilename_WithBlankInput_ReturnsNoname()" classname="net.yadaframework.components.YadaUtilTest" time="0.001"/>
  <testcase name="ensureSafeFilename_WithCase_RespectsCase()" classname="net.yadaframework.components.YadaUtilTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
