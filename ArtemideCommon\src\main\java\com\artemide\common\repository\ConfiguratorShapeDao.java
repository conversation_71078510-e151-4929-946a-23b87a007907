package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.ConfiguratorShape;

@Repository
@Transactional(readOnly = true)
public class ConfiguratorShapeDao {

    @PersistenceContext EntityManager em;

    // Anche se non ci sono metodi è usata per il find e save

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from ConfiguratorShape", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public ConfiguratorShape save(ConfiguratorShape entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<ConfiguratorShape> findById(Long entityId) {
        ConfiguratorShape result = em.find(ConfiguratorShape.class, entityId);
        return Optional.ofNullable(result);
    }

    public ConfiguratorShape findOne(Long entityId) {
        return em.find(ConfiguratorShape.class, entityId);
    }

    public List<ConfiguratorShape> findAll() {
        return em.createQuery("from ConfiguratorShape", ConfiguratorShape.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<ConfiguratorShape> batchToSave) {
        for (ConfiguratorShape entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(ConfiguratorShape entity) {
        em.remove(entity);
    }
}
