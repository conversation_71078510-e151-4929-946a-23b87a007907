package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.ConfiguratorShape;
import com.artemide.common.repository.ConfiguratorShapeDao;
import com.artemide.common.repository.ConfiguratorShapeRepository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies ConfiguratorShapeDao behaves exactly like ConfiguratorShapeRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class ConfiguratorShapeRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private ConfiguratorShapeRepository configuratorShapeRepository;

    @Autowired
    private ConfiguratorShapeDao configuratorShapeDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    ConfiguratorShape entity = entityManager.find(ConfiguratorShape.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            entityManager.createQuery("DELETE FROM ConfiguratorShape c WHERE c.text1 LIKE 'TEST_%' OR c.text1 LIKE 'INTEGRATION_%'")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private ConfiguratorShape createTestEntity(String text) {
        ConfiguratorShape entity = new ConfiguratorShape();
        entity.setText1(text); // ConfiguratorShape doesn't have setName(), use setText1()
        entity.setEnabled(true);
        return entity;
    }

    private void trackEntity(ConfiguratorShape entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = configuratorShapeRepository.count();
            long initialDaoCount = configuratorShapeDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            ConfiguratorShape testEntity = createTestEntity("INTEGRATION_SAVE_001");

            ConfiguratorShape repoSaved = configuratorShapeRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            ConfiguratorShape testEntity2 = createTestEntity("INTEGRATION_SAVE_002");
            ConfiguratorShape daoSaved = configuratorShapeDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<ConfiguratorShape> repoFound = configuratorShapeRepository.findById(repoSaved.getId());
            Optional<ConfiguratorShape> daoFoundOptional = configuratorShapeDao.findById(daoSaved.getId());
            ConfiguratorShape daoFoundDirect = configuratorShapeDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<ConfiguratorShape> repoResults = configuratorShapeRepository.findAll();
            List<ConfiguratorShape> daoResults = configuratorShapeDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            ConfiguratorShape entityForRepo = createTestEntity("INTEGRATION_DELETE_001");
            ConfiguratorShape entityForDao = createTestEntity("INTEGRATION_DELETE_002");
            
            ConfiguratorShape savedForRepo = configuratorShapeRepository.save(entityForRepo);
            ConfiguratorShape savedForDao = configuratorShapeDao.save(entityForDao);
            entityManager.flush();
            
            if (!configuratorShapeRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!configuratorShapeDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            configuratorShapeRepository.delete(savedForRepo);
            configuratorShapeDao.delete(savedForDao);
            entityManager.flush();

            if (configuratorShapeRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (configuratorShapeDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== ConfiguratorShapeRepository vs ConfiguratorShapeDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - ConfiguratorShapeDao behaves exactly like ConfiguratorShapeRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
