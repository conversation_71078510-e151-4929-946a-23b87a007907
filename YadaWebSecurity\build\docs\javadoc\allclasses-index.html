<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>All Classes and Interfaces (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#all-classes">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="All Classes and Interfaces" class="title">All Classes and Interfaces</h1>
</div>
<div id="all-classes-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="all-classes-table-tab0" role="tab" aria-selected="true" aria-controls="all-classes-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="all-classes-table-tab2" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab2', 2)" class="table-tab">Classes</button><button id="all-classes-table-tab3" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab3', 2)" class="table-tab">Enum Classes</button><button id="all-classes-table-tab5" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab5', 2)" class="table-tab">Exception Classes</button></div>
<div id="all-classes-table.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="all-classes-table-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/AuditFilter.html" title="class in net.yadaframework.security">AuditFilter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Inietta il session ID nell'MDC di logback, chiamandolo "session".</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/CheckSessionFilter.html" title="class in net.yadaframework.security">CheckSessionFilter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Questo filtro viene eseguito prima di qualunque cosa e, se la richiesta è COMMAND, ritorna "active" o "expired" riguardo la session.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/security/exceptions/InternalAuthenticationException.html" title="class in net.yadaframework.security.exceptions">InternalAuthenticationException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/SecurityWebApplicationInitializer.html" title="class in net.yadaframework.security">SecurityWebApplicationInitializer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/security/TooManyFailedAttemptsException.html" title="class in net.yadaframework.security">TooManyFailedAttemptsException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html" title="class in net.yadaframework.security.web">YadaActionUploadAttrProcessor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Serve solo se il csrf è attivo.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Questa classe aggiunge un pò di informazioni in request quando il login fallisce.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessFilter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Converts the AJAX_LOGGEDIN_PARAM request parameter into a AJAX_LOGGEDIN_HEADER response header
 so that the ajax target, after login, knows it has to close the login modal somehow (with a reload)</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used to create an url to access the site with an automatic login.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html" title="class in net.yadaframework.security.persistence.repository">YadaAutoLoginTokenDao</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/web/YadaCropDefinition.html" title="class in net.yadaframework.security.web">YadaCropDefinition</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/web/YadaDialectWithSecurity.html" title="class in net.yadaframework.security.web">YadaDialectWithSecurity</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html" title="class in net.yadaframework.security.exceptions">YadaInvalidUserException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">The current session user can't handle the object passed as parameter.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/YadaLocalePathRequestCache.html" title="class in net.yadaframework.security">YadaLocalePathRequestCache</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is needed to store the original request on access failure so that after login the browser is redirected to 
 the url with locale in path.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/web/YadaLoginController.html" title="class in net.yadaframework.security.web">YadaLoginController</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This controller handles the opening of the login form/modal, autologin links, and the result of an ajax login.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/components/YadaLogoutSuccessHandler.html" title="class in net.yadaframework.security.components">YadaLogoutSuccessHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Handler called during logout</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/web/YadaMiscController.html" title="class in net.yadaframework.security.web">YadaMiscController</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Miscellaneous @RequestMapping methods</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Data to submit during user registration.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html" title="class in net.yadaframework.security.persistence.repository">YadaRegistrationRequestDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/components/YadaSecurityBeans.html" title="class in net.yadaframework.security.components">YadaSecurityBeans</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Basic security configuration.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/components/YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession&lt;T&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for application session.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Rappresenta le credenziali generate alla registrazione con un social login</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaSocialCredentialsDao</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html" title="class in net.yadaframework.security.persistence.repository">YadaTicketDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaTicketMessage</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A message inside a YadaTicket.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/repository/YadaTicketMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaTicketMessageDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">The localized state of a YadaTicket</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">The localized type of a YadaTicket.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/components/YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Handles autologin links: creation and parsing.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">YadaUserCredentials database operations.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage&lt;YLE&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A message sent to some user by another user or by the system.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">The localized type of a YadaUserMessage - can be replaced by a different one in the application.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao&lt;T&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/components/YadaUserSetup.html" title="class in net.yadaframework.security.components">YadaUserSetup&lt;T&gt;</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Convenience method to create configured application users.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/YadaWebSecurityConfig.html" title="class in net.yadaframework.security">YadaWebSecurityConfig</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A wrapper for the saved request that allows to add url parameters.</div>
</div>
</div>
</div>
</div>
</main>
</div>
</div>
</body>
</html>
