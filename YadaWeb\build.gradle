buildscript {
	repositories {
		mavenCentral()
	}
	dependencies {
		// Needed for test schema creation
		classpath 'net.yadaframework:yadatools:0.7.7.R4'
		// classpath 'kr.motd.gradle:sphinx-gradle-plugin:2.2.0'
	}                    
}

plugins {
	id 'java-library'
	id 'maven-publish'
	id 'signing'
	id 'eclipse-wtp'
}

apply from: '../YadaWeb/loadSharedProperties.gradle'

group = 'net.yadaframework'
version = yada_version

java {
    sourceCompatibility = JavaVersion.valueOf("VERSION_${java_version}")
    targetCompatibility = JavaVersion.valueOf("VERSION_${java_version}")
    // Use Gradle toolchains to ensure the correct JDK is selected automatically
    toolchain {
    	languageVersion = JavaLanguageVersion.of(java_version)
    }
    // https://docs.gradle.org/6.0/userguide/java_plugin.html#sec:java-extension
    withSourcesJar()
    withJavadocJar() // https://stackoverflow.com/a/75710366/587641
}

if (!project.hasProperty('repoPath')) {
	ext.repoPath="repoPathMissing"
}

eclipse {
	jdt {
		// sourceCompatibility = 1.8
		// targetCompatibility = 1.8
		// https://stackoverflow.com/a/35302104/587641
		file {
	  		File dir = file('.settings')
	  		dir.mkdirs()
	  		File f = file('.settings/org.eclipse.core.resources.prefs')
	  		if (!f.exists()) {
	  			f.write('eclipse.preferences.version=1\n')
	  			f.append('encoding/<project>=utf-8')
	  		}
		}
	}
	classpath {
		downloadJavadoc = true
		downloadSources = true
	}
}

compileJava.options.encoding = 'UTF-8'
compileTestJava.options.encoding = 'UTF-8'

repositories {
	mavenCentral()
}

tasks.withType(JavaCompile).configureEach {
	// Needed since Spring 6.1: 
	// https://github.com/spring-projects/spring-framework/wiki/Upgrading-to-Spring-Framework-6.x#parameter-name-retention
	options.compilerArgs.add("-parameters")
}

dependencies {
	// Using "api" to keep things simple: no need to repeat the libraries in other projects for compilation

	api 'org.apache.commons:commons-configuration2:2.12.0'
	// These are added to fix the vulnerability "Uncontrolled Recursion" in commons-configuration2 2.12.0
	api 'org.apache.commons:commons-text:1.14.0'
	api 'org.apache.commons:commons-lang3:3.18.0'
	
	// For embedded Tomcat ONLY - add these to your project
    // runtimeOnly 'org.apache.tomcat:tomcat-el-api:8.5.+'
    // runtimeOnly 'org.apache.tomcat:tomcat-jasper-el:8.5.+'
    // runtimeOnly 'org.apache.tomcat:tomcat-jasper:8.5.+'
    // For embedded mariadb
	compileOnly('ch.vorburger.mariaDB4j:mariaDB4j:3.2.0') {
		// mariaDB4j pulls the latest version of spring core but this is never a good thing.
		exclude group: 'org.springframework', module: 'spring-core'
	}	
	// Needed by Commons Configuration
    api 'commons-jxpath:commons-jxpath:1.4.0'

	// For Spring: 	using a specific version instead of the + wildcard to prevent side effects
	// 				in the application when new versions are made available
	api 'org.springframework:spring-context-support:6.2.11',
		'org.springframework:spring-orm:6.2.11',
		'org.springframework:spring-webmvc:6.2.11',
		'org.springframework:spring-core:6.2.11',
		// 'org.springframework.social:spring-social-facebook:2.0.3.RELEASE',
		'org.hibernate.orm:hibernate-core:7.1.1.Final',
		'org.hibernate.validator:hibernate-validator:9.0.1.Final',
		'org.thymeleaf:thymeleaf-spring6:3.1.3.RELEASE',
		'com.mysql:mysql-connector-j:9.4.0',
		'commons-collections:commons-collections:3.2.2',
		'org.apache.commons:commons-exec:1.3',
		// 'jakarta.mail:jakarta.mail-api:2.1.2',
		'com.sun.mail:jakarta.mail:2.0.2', // The implementation is needed, not the api
		'com.google.guava:guava:33.4.8-jre',
		'org.jsoup:jsoup:1.20.1',
		'ch.qos.logback:logback-classic:1.5.18',
		'com.fasterxml.jackson.core:jackson-databind:2.19.2',
		'com.drewnoakes:metadata-extractor:2.19.0',
		'org.flywaydb:flyway-mysql:11.13.1', // Java 21
		'commons-beanutils:commons-beanutils:1.11.0'
		
	// Needed to compile against JPA types, but don’t ship it at runtime
	compileOnly 'jakarta.persistence:jakarta.persistence-api:3.2.0'
		
	 // Connection pool for embedded Tomcat and programmatic datasource
	api 'org.vibur:vibur-dbcp:25.0'
	runtimeOnly 'com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2'
	implementation 'org.apache.tomcat:tomcat-servlet-api:11.0.10'
	compileOnly 'org.apache.tomcat.embed:tomcat-embed-core:11.0.10'
	compileOnly 'jakarta.servlet:jakarta.servlet-api:6.0.0'
	compileOnly 'org.seleniumhq.selenium:selenium-java:4.13.0' // 4.13.0 is the last java 8 version

	// Needed to run in Tomcat
	// runtime 'commons-jxpath:commons-jxpath:1.3'

	testImplementation platform('org.junit:junit-bom:5.13.4')
	testImplementation 'org.junit.jupiter:junit-jupiter'
	testImplementation 'org.junit.platform:junit-platform-launcher'
	testRuntimeOnly 'org.junit.platform:junit-platform-engine'
	testImplementation 'org.mockito:mockito-core:5.3.1'
	testImplementation 'org.mockito:mockito-junit-jupiter:5.3.1'
	testImplementation 'org.springframework:spring-test:6.2.11'
	testImplementation 'javax.servlet:javax.servlet-api:4.0.1'
	
		
}

task listJars {
	doLast {
		configurations.compileClasspath.each { File file -> println file.name }
	}
}

jar {
	manifest {
		attributes("Implementation-Title": "YadaWeb", "Implementation-Version": archiveVersion)
	}
}

// Set the yada version in the properties file
processResources {
	doLast {
		ant.replace(file: "${sourceSets.main.output.resourcesDir}/net.yadaframework.yadaweb.properties", token: '@YADA_VERSION@', value: version)
	}
}

javadoc {
	options.encoding = 'UTF-8'
	options.docEncoding = 'UTF-8'
	options.charSet = 'UTF-8'
	failOnError = false
}

configurations {
    // Fix Spring logging conflict: exclude commons-logging in favor of spring-jcl
    all*.exclude group: 'commons-logging', module: 'commons-logging'
}

// http://stackoverflow.com/a/27853822/587641
// task javadocJar(type: Jar) {
//     classifier = 'javadoc'
//     from javadoc
// }   
// 
// artifacts {
//     archives javadocJar
// }

configurations {
	hibtools {
		extendsFrom configurations.implementation
		canBeResolved = true
		canBeConsumed = false
	}
}

dependencies {
	hibtools files(layout.buildDirectory.dir("classes/java/main"))
	hibtools files(layout.buildDirectory.dir("classes/java/test")) // Needed for yadaPersistenceUnit
	hibtools 'org.hibernate:hibernate-tools:5.6.15.Final'
}

task testDbSchema(dependsOn: [testClasses], type: net.yadaframework.tools.YadaCreateDbSchemaTask) {
	inputs.files configurations.hibtools
	outputfilename = "V1__yadatest.sql"
	doFirst {
		layout.buildDirectory.dir("classes/java/test/META-INF").get().asFile.mkdirs()
		copy {
			from layout.buildDirectory.dir("resources/test/META-INF/persistence.test.xml")
			into layout.buildDirectory.dir("classes/java/test/META-INF")
			rename 'persistence.test.xml', 'persistence.xml'
		} 
	}
	doLast { 
		delete layout.buildDirectory.file("classes/java/test/META-INF/persistence.xml")
	}
}

test {
	useJUnitPlatform()
	testLogging {
		events "passed", "skipped", "failed"
		showStandardStreams = true
	}
}

publishing {
    publications {
        yadaWebLibrary(MavenPublication) {
        	artifactId = 'yadaweb'
            from components.java
            
            versionMapping {
                usage('java-api') {
                    fromResolutionOf('runtimeClasspath')
                }
                usage('java-runtime') {
                    fromResolutionOf('runtimeClasspath')
                }
            }
			pom {
			    name = 'YadaWeb'
			    description = 'Some useful tasks for the Yada Framework'
			    url = 'https://yadaframework.net/en/index.html'
			    inceptionYear = '2014'
			    packaging = 'jar'
			    licenses {
			        license {
			            name = 'MIT License'
			            url = 'https://en.wikipedia.org/wiki/MIT_License'
			        }
			    }
			    developers {
			        developer {
			            id = 'xtianus'
			            name = 'Studio Ghezzi'
			            email = '<EMAIL>'
			        }
			    }
			    organization {
			    	name = 'Studio Ghezzi'
			    	url = 'https://studio.ghezzi.net/'
			    }
			    scm {
			        connection = 'scm:git:**************:xtianus/yadaframework.git'
			        developerConnection = 'scm:git:**************:xtianus/yadaframework.git'
			        url = 'https://github.com/xtianus/yadaframework'
			    }
			}
        }
    }
    repositories {
    	// Creates task publishYadaWebCMSLibraryPublicationToPublicRepoRepository
        maven {
        	name = "PublicRepo"
            def snapshotsRepoUrl = "https://oss.sonatype.org/content/repositories/snapshots/"
            def releasesRepoUrl = "https://oss.sonatype.org/service/local/staging/deploy/maven2/"
            url = version.endsWith('SNAPSHOT') ? snapshotsRepoUrl : releasesRepoUrl
            credentials(PasswordCredentials) {
            	username = project.hasProperty('ossrhUsername')?ossrhUsername:'USERUNSET'
            	password = project.hasProperty('ossrhPassword')?ossrhPassword:'PWDUNSET'
            }
        }
        // Creates task publishYadaWebLibraryPublicationToLocalRepoRepository
        maven {
        	name = "LocalRepo"
        	// repoPath must be passed when invoking gradle using -P
        	url = "file://${repoPath}"
    	}
    }    
}

signing {
    sign publishing.publications.yadaWebLibrary
}
