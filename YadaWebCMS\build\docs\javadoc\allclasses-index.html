<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>All Classes and Interfaces (YadaWebCMS '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#all-classes">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="All Classes and Interfaces" class="title">All Classes and Interfaces</h1>
</div>
<div id="all-classes-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="all-classes-table-tab0" role="tab" aria-selected="true" aria-controls="all-classes-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="all-classes-table-tab1" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab1', 2)" class="table-tab">Interfaces</button><button id="all-classes-table-tab2" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab2', 2)" class="table-tab">Classes</button></div>
<div id="all-classes-table.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="all-classes-table-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An Article is the actual physical item that can be produced and sold, so it will have a specific color, size, price, etc.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/cms/YadaCmsConfig.html" title="class in net.yadaframework.cms">YadaCmsConfig</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/cms/YadaCmsConfiguration.html" title="class in net.yadaframework.cms">YadaCmsConfiguration</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/cms/web/YadaCmsProductController.html" title="class in net.yadaframework.cms.web">YadaCmsProductController</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Entity for a product dimension</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Stores all the elements that can appear on a gallery slide: image, video, text...</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Product is an "abstract" item because it groups similar objects that differ in color, size or other attributes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/cms/persistence/repository/YadaProductDao.html" title="class in net.yadaframework.cms.persistence.repository">YadaProductDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="net/yadaframework/cms/persistence/entity/YadaSortableEntity.html" title="interface in net.yadaframework.cms.persistence.entity">YadaSortableEntity</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Entities can be sorted visually (by showing a list of thumbnails) if they implement this interface.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/cms/persistence/repository/YadaWebCmsDao.html" title="class in net.yadaframework.cms.persistence.repository">YadaWebCmsDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Miscellaneous CMS-related methods</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/cms/components/YadaWebCmsHelper.html" title="class in net.yadaframework.cms.components">YadaWebCmsHelper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Helper for html fragments</div>
</div>
</div>
</div>
</div>
</main>
</div>
</div>
</body>
</html>
