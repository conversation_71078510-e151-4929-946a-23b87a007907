-charset 'UTF-8'
-classpath 'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\build\\classes\\java\\main;C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\build\\resources\\main;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.commons\\commons-configuration2\\2.12.0\\cab81350c85ca35db8c47621a19dcddf128883f\\commons-configuration2-2.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.commons\\commons-text\\1.14.0\\adcb0d4c67eabc79682604b47eb852aaff21138a\\commons-text-1.14.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\ch.vorburger.mariaDB4j\\mariaDB4j\\3.2.0\\d011a74b42cace1de7e7a863e4457947486deea3\\mariaDB4j-3.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\ch.vorburger.mariaDB4j\\mariaDB4j-core\\3.2.0\\678ca175e9c8375d70eed9453a1140f3710234e0\\mariaDB4j-core-3.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.commons\\commons-lang3\\3.18.0\\fb14946f0e39748a6571de0635acbe44e7885491\\commons-lang3-3.18.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\commons-jxpath\\commons-jxpath\\1.4.0\\f61af30d8db727b7bb53d285fe074f89d8b68951\\commons-jxpath-1.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-context-support\\6.2.11\\b092cbb9c21eab6fbbbf57b21eaef8da6d3a96a6\\spring-context-support-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-orm\\6.2.11\\d14df2d6ecddd2984ef5590d73c5178d7df25c11\\spring-orm-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-webmvc\\6.2.11\\3313fe95e06c1d3c94d00301cbed56ed1a9d2a86\\spring-webmvc-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-context\\6.2.11\\bbfb4d55385b2b65ecaf04937d209cf467eaba2f\\spring-context-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-jdbc\\6.2.11\\8aadfc8bed1630563c65bb5628e2175ecd508998\\spring-jdbc-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-tx\\6.2.11\\f2b8bef65f6682019002c12a628886637e596261\\spring-tx-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-aop\\6.2.11\\1e970383810700506d646c2fa8943725f123f000\\spring-aop-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-web\\6.2.11\\e590ac3e1aaea2d34a5ccb5c25ad84acbada8468\\spring-web-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-beans\\6.2.11\\8d92eb837b70094d7316f6a07770eefd360bc53c\\spring-beans-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-expression\\6.2.11\\7190c1c69576516efef1a061d956dccf17cb0b86\\spring-expression-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-core\\6.2.11\\f4860eb6ea92abb8ae6c5e82e2e7efc395cef8d\\spring-core-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.hibernate.orm\\hibernate-core\\7.1.1.Final\\ef758e23453f219771a7fdb020f2fd047ca830bc\\hibernate-core-7.1.1.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.hibernate.validator\\hibernate-validator\\9.0.1.Final\\28c0e41ecc84d1f0b8b6d81d16e0784e63364e68\\hibernate-validator-9.0.1.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.thymeleaf\\thymeleaf-spring6\\3.1.3.RELEASE\\4b276ea2bd536a18e44b40ff1d9f4848965ff59c\\thymeleaf-spring6-3.1.3.RELEASE.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.mysql\\mysql-connector-j\\9.4.0\\8f6c66269048fbd2316b7b45d75898ebee44986f\\mysql-connector-j-9.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\commons-beanutils\\commons-beanutils\\1.11.0\\ac03ea606d13de04c2e4508227680faff151f491\\commons-beanutils-1.11.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\commons-collections\\commons-collections\\3.2.2\\8ad72fe39fa8c91eaaf12aadb21e0c3661fe26d5\\commons-collections-3.2.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\ch.vorburger.exec\\exec\\3.3.1\\75a474a460730420c856c395684d4647dc4cefdf\\exec-3.3.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-java\\4.13.0\\18cef0adbc08d152da07b234a04f6e8240ce215f\\selenium-java-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-chrome-driver\\4.13.0\\490603c4c79f938ffaf30058a675d850d9717265\\selenium-chrome-driver-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-devtools-v115\\4.13.0\\76c322f8b51ce2176c1b9aae458e13d18aa51276\\selenium-devtools-v115-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-devtools-v116\\4.13.0\\5bb40cd24bcaf9d6015eb6cdf76784506d36c033\\selenium-devtools-v116-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-devtools-v117\\4.13.0\\86db93ca74312d7795df726d0412cb350c31f15b\\selenium-devtools-v117-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-firefox-driver\\4.13.0\\2f2eb0ff81797444908024dd05b36fe287204cfe\\selenium-firefox-driver-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-devtools-v85\\4.13.0\\5469963e082f59946ec627114f7baa2fa5bf81d8\\selenium-devtools-v85-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-edge-driver\\4.13.0\\5f818b6057c9a9abc3b4ac9c1b9d5dd5e478d753\\selenium-edge-driver-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-ie-driver\\4.13.0\\67137653d0d2e0c4f6ea735c9e57772ed9f5b3f4\\selenium-ie-driver-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-safari-driver\\4.13.0\\739a5e2a88b640ee7660779ece1fa0f8cfaa1372\\selenium-safari-driver-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-support\\4.13.0\\decde2a7df52ce78132dc6fe6085384327c844f0\\selenium-support-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-chromium-driver\\4.13.0\\4ff91ea6a6b7157e44490269664419cc1a008f64\\selenium-chromium-driver-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-remote-driver\\4.13.0\\5d9f84d7d51174a54fa507b1418db3b1feddec6a\\selenium-remote-driver-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-manager\\4.13.0\\d474cc621720e47e9f00ef7f4fec8cc76643d41b\\selenium-manager-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-os\\4.13.0\\91ce811433b76a85ff37e8b3aeaf2b919f4e65ff\\selenium-os-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.commons\\commons-exec\\1.4.0\\e9061b21958bfaf1cb736eb22e36cbf02d8fe9f\\commons-exec-1.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.sun.mail\\jakarta.mail\\2.0.2\\6dfe5d279fb579a41baa84a7728e31a40a50d90e\\jakarta.mail-2.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-http\\4.13.0\\351d1c150e47cf863027020fef3b9f4f38a6c99c\\selenium-http-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\guava\\33.4.8-android\\e70a3268e6cd3e7d458aa15787ce6811c34e96ae\\guava-33.4.8-jre.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jsoup\\jsoup\\1.20.1\\769377896610be1736f8d6d51fc52a6042d1ce82\\jsoup-1.20.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\ch.qos.logback\\logback-classic\\1.5.18\\fc371f3fc97a639de2d67947cffb7518ec5e3d40\\logback-classic-1.5.18.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.fasterxml.jackson.core\\jackson-annotations\\2.19.2\\c5381f11988ae3d424b197a26087d86067b6d7d\\jackson-annotations-2.19.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.fasterxml.jackson.core\\jackson-core\\2.19.2\\50f3b4bd59b9ff51a0ed493e7b5abaf5c39709bf\\jackson-core-2.19.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.flywaydb\\flyway-mysql\\11.13.1\\d514f7ab22e9373f6a2bee4a19985a1cff3e170c\\flyway-mysql-11.13.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.flywaydb\\flyway-core\\11.13.1\\3d44db5cf1ddb86b682c5f961675fe8606b7b0d5\\flyway-core-11.13.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.fasterxml.jackson.core\\jackson-databind\\2.19.2\\46509399d28f57ca32c6bb4b0d4e10e8f062051e\\jackson-databind-2.19.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.drewnoakes\\metadata-extractor\\2.19.0\\4d3ab42dd9965b8f2b8bd1b7572e028d6f90a34f\\metadata-extractor-2.19.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.vibur\\vibur-dbcp\\25.0\\f03f9159bf6e284bef3090797005f1e426a1ca37\\vibur-dbcp-25.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\jakarta.persistence\\jakarta.persistence-api\\3.2.0\\bb75a113f3fa191c2c7ee7b206d8e674251b3129\\jakarta.persistence-api-3.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.tomcat.embed\\tomcat-embed-core\\11.0.10\\147dd053b2203150c1e13165d0560bf0dfaa7fc2\\tomcat-embed-core-11.0.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\jakarta.servlet\\jakarta.servlet-api\\6.0.0\\abecc699286e65035ebba9844c03931357a6a963\\jakarta.servlet-api-6.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.tomcat\\tomcat-servlet-api\\11.0.10\\98e010486ca909c3081cff9e4af150ade70ac745\\tomcat-servlet-api-11.0.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-jcl\\6.2.11\\819fc7e968ac07d4b042be7cfb8d99c267142ac7\\spring-jcl-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\jakarta.transaction\\jakarta.transaction-api\\2.0.1\\51a520e3fae406abb84e2e1148e6746ce3f80a1a\\jakarta.transaction-api-2.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\jakarta.validation\\jakarta.validation-api\\3.1.1\\ec8622148afc5564235d17af80ea80288d0e7f92\\jakarta.validation-api-3.1.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jboss.logging\\jboss-logging\\3.6.1.Final\\886afbb445b4016a37c8960a7aef6ebd769ce7e5\\jboss-logging-3.6.1.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.fasterxml\\classmate\\1.7.0\\e98374da1f2143ac8e6e0a95036994bb19137a3\\classmate-1.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.thymeleaf\\thymeleaf\\3.1.3.RELEASE\\51474f2a90b282ee97dabcd159c7faf24790f373\\thymeleaf-3.1.3.RELEASE.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.asynchttpclient\\async-http-client\\2.12.3\\6dfc91814cc8b3bc3327246d0e5df36911b9a623\\async-http-client-2.12.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.asynchttpclient\\async-http-client-netty-utils\\2.12.3\\ad99d8622931ed31367d0fef7fa17eb62e033fb3\\async-http-client-netty-utils-2.12.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.slf4j\\slf4j-api\\2.0.17\\d9e58ac9c7779ba3bf8142aff6c830617a7fe60f\\slf4j-api-2.0.17.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.protobuf\\protobuf-java\\4.31.1\\1828b20315b63d5f71b3c61b094494a8f1acdc5a\\protobuf-java-4.31.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.sun.activation\\jakarta.activation\\2.0.1\\828b80e886a52bb09fe41ff410b10b342f533ce1\\jakarta.activation-2.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\failureaccess\\1.0.3\\aeaffd00d57023a2c947393ed251f0354f0985fc\\failureaccess-1.0.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\listenablefuture\\9999.0-empty-to-avoid-conflict-with-guava\\b421526c5f297295adef1c886e5246c39d4ac629\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jspecify\\jspecify\\1.0.0\\7425a601c1c7ec76645a78d22b8c6a627edee507\\jspecify-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.errorprone\\error_prone_annotations\\2.37.0\\8512660d1269d166fad497f51de35da61447f063\\error_prone_annotations-2.37.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.j2objc\\j2objc-annotations\\3.0.0\\7399e65dd7e9ff3404f4535b2f017093bdb134c7\\j2objc-annotations-3.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\ch.qos.logback\\logback-core\\1.5.18\\6c0375624f6f36b4e089e2488ba21334a11ef13f\\logback-core-1.5.18.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.adobe.xmp\\xmpcore\\6.1.11\\852f14101381e527e6d43339d7db1698c970436c\\xmpcore-6.1.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.vibur\\vibur-object-pool\\25.0\\8412049bfba1a65acf239efe74da811397f668c7\\vibur-object-pool-25.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\ch.vorburger.mariaDB4j\\mariaDB4j-db-linux64\\11.4.5\\7063411401b8a28413ba0220cc3506095f9ad75e\\mariaDB4j-db-linux64-11.4.5.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\ch.vorburger.mariaDB4j\\mariaDB4j-db-macos-arm64\\11.4.5\\f62ab0039ec58bfeddd10bf9a1ed9491c560e085\\mariaDB4j-db-macos-arm64-11.4.5.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\ch.vorburger.mariaDB4j\\mariaDB4j-db-winx64\\11.4.5\\1e7d6c6f8c030353f5b78959aaabb4978c2c3c52\\mariaDB4j-db-winx64-11.4.5.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.tomcat\\tomcat-annotations-api\\11.0.10\\a48ba86029760ba6ca14002999bab102b89df4c0\\tomcat-annotations-api-11.0.10.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-json\\4.13.0\\ebab8a0a864a9715df77b8c615d8c41709282924\\selenium-json-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.seleniumhq.selenium\\selenium-api\\4.13.0\\d0471521e3618fad0afa585103ba9f07c899b3ca\\selenium-api-4.13.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.micrometer\\micrometer-observation\\1.14.11\\ad84118158e17027c5c31b7567df736c3edc9cce\\micrometer-observation-1.14.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.attoparser\\attoparser\\2.0.7.RELEASE\\e5d0e988d9124139d645bb5872b24dfa23e283cc\\attoparser-2.0.7.RELEASE.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.unbescape\\unbescape\\1.1.6.RELEASE\\7b90360afb2b860e09e8347112800d12c12b2a13\\unbescape-1.1.6.RELEASE.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\jakarta.annotation\\jakarta.annotation-api\\3.0.0\\54f928fadec906a99d558536756d171917b9d936\\jakarta.annotation-api-3.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\commons-io\\commons-io\\2.18.0\\44084ef756763795b31c578403dd028ff4a22950\\commons-io-2.18.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.auto.service\\auto-service-annotations\\1.1.1\\da12a15cd058ba90a0ff55357fb521161af4736d\\auto-service-annotations-1.1.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-handler-proxy\\4.1.60.Final\\2352f12826400e5db64b36fd951508ce9a61c196\\netty-handler-proxy-4.1.60.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-codec-http\\4.1.96.Final\\a4d0d95df5026965c454902ef3d6d84b81f89626\\netty-codec-http-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-transport-native-epoll\\4.1.96.Final\\9faf365396c933f1b39b60d129391c6c6c43fb86\\netty-transport-native-epoll-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-transport-native-epoll\\4.1.96.Final\\3f8904e072cfc9a8d67c6fe567c39bcbce5c9c55\\netty-transport-native-epoll-4.1.96.Final-linux-x86_64.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-transport-classes-epoll\\4.1.96.Final\\b0369501645f6e71f89ff7f77b5c5f52510a2e31\\netty-transport-classes-epoll-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-transport-native-kqueue\\4.1.96.Final\\2721cf6fe8752168dafdd4187ae097e6cf9dd9f5\\netty-transport-native-kqueue-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-transport-native-kqueue\\4.1.96.Final\\c127ed313fc80cf2cb366dccfded1daddc89a8ef\\netty-transport-native-kqueue-4.1.96.Final-osx-x86_64.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-transport-classes-kqueue\\4.1.96.Final\\782f6bbb8dd5401599d272ea0fb81d1356bdffb2\\netty-transport-classes-kqueue-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.typesafe.netty\\netty-reactive-streams\\2.0.4\\f77c8eaa7d5e2f2160b6d21ba385cf726f164b2\\netty-reactive-streams-2.0.4.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-handler\\4.1.96.Final\\7840d7523d709e02961b647546f9d9dde1699306\\netty-handler-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-transport-native-unix-common\\4.1.96.Final\\daf8578cade63a01525ee9d70371fa78e6e91094\\netty-transport-native-unix-common-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-codec-socks\\4.1.60.Final\\6f4573281df659265bd709fd10471c3e00ef6c70\\netty-codec-socks-4.1.60.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-codec\\4.1.96.Final\\9cfe430f8b14e7ba86969d8e1126aa0aae4d18f0\\netty-codec-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-transport\\4.1.96.Final\\dbd15ca244be28e1a98ed29b9d755edbfa737e02\\netty-transport-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-buffer\\4.1.96.Final\\4b80fffbe77485b457bf844289bf1801f61b9e91\\netty-buffer-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-resolver\\4.1.96.Final\\e51db5568a881e0f9b013b35617c597dc32f130\\netty-resolver-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.netty\\netty-common\\4.1.96.Final\\d10c167623cbc471753f950846df241d1021655c\\netty-common-4.1.96.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-exporter-logging\\1.28.0\\e6721fd80fe703a9bbaf8fcdf269aa878a2fa963\\opentelemetry-exporter-logging-1.28.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-sdk-extension-autoconfigure\\1.28.0\\6db66c77ca29a8d05227324a8392b736744bbe3f\\opentelemetry-sdk-extension-autoconfigure-1.28.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-sdk-extension-autoconfigure-spi\\1.28.0\\582ce034be1262aac6d77b92ec2d6cf4884cee4d\\opentelemetry-sdk-extension-autoconfigure-spi-1.28.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-sdk\\1.28.0\\a1ca3938a03e5bb0749dc92da91edf76f6ee3b7f\\opentelemetry-sdk-1.28.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-sdk-trace\\1.28.0\\18797986d45940d873430023280211d6990680c\\opentelemetry-sdk-trace-1.28.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-sdk-metrics\\1.28.0\\4d955fb6c2ec89b4f55d88d7aed4dd9c36809235\\opentelemetry-sdk-metrics-1.28.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-sdk-logs\\1.28.0\\adcd0bc96bc77152a15b3b8890bd8a04dd0bf36b\\opentelemetry-sdk-logs-1.28.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-sdk-common\\1.28.0\\b25e52ef6829bb41db3227d8fcc206009b018f40\\opentelemetry-sdk-common-1.28.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-semconv\\1.28.0-alpha\\97336840db7cb0ef7e5d292f7cec5bdb385cc370\\opentelemetry-semconv-1.28.0-alpha.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-api\\1.28.0\\ebdea4fbe23c3929f1702b176d2cd63ac6288f0\\opentelemetry-api-1.28.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opentelemetry\\opentelemetry-context\\1.28.0\\468c460d80c5a6e0fdddd3c1a83148b316571c22\\opentelemetry-context-1.28.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\net.bytebuddy\\byte-buddy\\1.14.5\\28a424c0c4f362568e904d992c239c996cf7adc7\\byte-buddy-1.14.5.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.micrometer\\micrometer-commons\\1.14.11\\451c18c4a48cfb74973fb29ec6b9339fc29332f0\\micrometer-commons-1.14.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.commons\\commons-collections4\\4.4\\62ebe7544cb7164d87e0637a2a6a2bdc981395e8\\commons-collections4-4.4.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\dev.failsafe\\failsafe\\3.3.2\\738a986f1f0e4b6c6a49d351dddc772d1378c5a8\\failsafe-3.3.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.reactivestreams\\reactive-streams\\1.0.3\\d9fb7a7926ffa635b3dcaa5049fb2bfa25b3e7d0\\reactive-streams-1.0.3.jar'
-d 'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\build\\docs\\javadoc'
-docencoding 'UTF-8'
-doctitle 'YadaWeb \'0.7.8\' API'
-encoding 'UTF-8'
-notimestamp 
-quiet 
-windowtitle 'YadaWeb \'0.7.8\' API'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaAttachedFileCloneSet.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaCopyNot.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaCopyShallow.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaDataTableFactory.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaDateFormatter.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaEmailBuilder.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaEmailService.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaFileManager.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaFormHelper.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaJobManager.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaJobScheduler.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaJsonMapper.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaKeyRateLimiter.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaLocalePathVariableFilter.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaLongRunningExclusive.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaMariaDB.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaMariaDBServer.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaNotify.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaNotifyData.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaSecurityUtilStub.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaSetup.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaSimpleRateLimiter.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaSleepingRateLimiter.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaUtil.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\components\\YadaWebUtil.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\CloneableDeep.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\CloneableFiltered.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaAjaxInterceptor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaAppConfig.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaConfiguration.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaConstants.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaDummyDatasource.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaDummyEntityManagerFactory.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaDummyJpaConfig.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaFluentBase.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaJpaConfig.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaLinkBuilder.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaLocalEnum.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaLocalePathChangeInterceptor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaLocalePathLinkBuilder.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaRegistrationType.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaTomcatServer.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaWebApplicationInitializer.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\core\\YadaWebConfig.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\InternalException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\InvalidValueException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\SystemException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\YadaAlreadyRunningException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\YadaConfigurationException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\YadaEmailException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\YadaInternalException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\YadaInvalidUsageException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\YadaInvalidValueException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\YadaJobFailedException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\YadaSocialException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\exceptions\\YadaSystemException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\entity\\YadaAttachedFile.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\entity\\YadaBrowserId.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\entity\\YadaClause.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\entity\\YadaJob.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\entity\\YadaJobState.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\entity\\YadaManagedFile.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\entity\\YadaPersistentEnum.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\entity\\YadaRateLog.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\repository\\YadaAttachedFileDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\repository\\YadaBrowserIdDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\repository\\YadaClauseDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\repository\\YadaFileManagerDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\repository\\YadaJobDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\repository\\YadaJobSchedulerDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\repository\\YadaLocaleDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\repository\\YadaPersistentEnumDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\YadaDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\YadaDataTableDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\YadaMoney.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\YadaMoneyConverter.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\YadaSql.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\persistence\\YadaSqlBuilder.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\package-info.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\YadaHttpUtil.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\YadaIntDimension.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\YadaLookupTable.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\YadaLookupTableFive.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\YadaLookupTableFour.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\YadaLookupTableSix.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\YadaLookupTableThree.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\YadaNetworkUtil.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\YadaRegexReplacer.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\raw\\YadaRegexUtil.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\selenium\\package-info.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\selenium\\YadaSeleniumUtil.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\tools\\AntIncrementBuild.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\tools\\YadaSchemaGenerator.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\config\\YadaDataTableButton.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\config\\YadaDataTableColumn.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\config\\YadaDataTableConfirmDialog.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\config\\YadaDataTableHTML.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\config\\YadaDataTableLanguage.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\options\\YadaDTBreakpoint.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\options\\YadaDTColumnDef.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\options\\YadaDTColumns.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\options\\YadaDTOptions.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\options\\YadaDTOrder.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\options\\YadaDTResponsive.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\options\\YadaDTResponsiveDetails.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\proxy\\YadaDataTableButtonProxy.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\proxy\\YadaDataTableColumnProxy.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\proxy\\YadaDataTableConfirmDialogProxy.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\proxy\\YadaDataTableHTMLProxy.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\proxy\\YadaDataTableLanguageProxy.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\proxy\\YadaDataTableProxy.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\proxy\\YadaDTColumnDefProxy.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\proxy\\YadaDTColumnsProxy.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\proxy\\YadaDTOptionsProxy.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\YadaDataTable.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\YadaDataTableConfigurer.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\YadaDataTableHelper.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\datatables\\YadaDtAjaxHandler.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaAjaxAttrProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaBrOnFirstSpaceAttrProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaDataTableTagProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaDialect.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaDialectUtil.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaHrefAttrProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaInputCounterTagProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaInputTagProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaInputTagSuggestion.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaNewlineTextAttrProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaSimpleAttrProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaSrcAttrProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaSrcsetAttrProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\dialect\\YadaTextareaTagProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\exceptions\\YadaHttpNotFoundException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\flot\\YadaFlotAxis.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\flot\\YadaFlotChart.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\flot\\YadaFlotGrid.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\flot\\YadaFlotPlotOptions.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\flot\\YadaFlotSeriesObject.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\flot\\YadaFlotSeriesOptions.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\form\\YadaFormFieldMap.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\form\\YadaFormPasswordChange.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\social\\YadaFacebookRequest.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\social\\YadaFacebookRequestV9.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\social\\YadaSocial.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaController.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaCropImage.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaCropQueue.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaDatatablesColumn.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaDatatablesColumnSearch.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaDatatablesOrder.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaDatatablesRequest.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaEmailContent.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaEmailParam.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaGlobalExceptionHandler.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaJsonDateSimpleSerializer.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaJsonDateTimeShortSerializer.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaJsonRawStringSerializer.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaJsonView.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaPageRequest.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaPageRows.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaPageSort.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaPersistentEnumEditor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaPublicSuffix.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\net\\yadaframework\\web\\YadaViews.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\org\\springframework\\web\\multipart\\commons\\YadaCommonsMultipartResolver.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\sogei\\utility\\UCheckDigit.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\src\\main\\java\\sogei\\utility\\UCheckNum.java'
