# Home builder galleries

create table PageModule_YadaGallerySlide1 (PageModule_id bigint not null, gallery1_id bigint not null, primary key (PageModule_id, gallery1_id)) engine=InnoDB;
create table PageModule_YadaGallerySlide2 (PageModule_id bigint not null, gallery2_id bigint not null, primary key (PageModule_id, gallery2_id)) engine=InnoDB;
create table PageModule_YadaGallerySlide3 (PageModule_id bigint not null, gallery3_id bigint not null, primary key (PageModule_id, gallery3_id)) engine=InnoDB;

create table YadaGallerySlide (id bigint not null auto_increment, data1 varchar(255), data2 varchar(255), data3 varchar(255), data4 varchar(255), data5 varchar(255), data6 varchar(255), enabled bit not null, flag1 bit not null, flag2 bit not null, pos bigint, text1 varchar(2048), text2 varchar(2048), text3 varchar(2048), text4 varchar(2048), version bigint not null, image_id bigint, video_id bigint, primary key (id)) engine=InnoDB;
create table YadaGallerySlide_text5local (YadaGallerySlide_id bigint not null, text5local varchar(2048), locale varchar(32) not null, primary key (YadaGallerySlide_id, locale)) engine=InnoDB;
create table YadaGallerySlide_text6local (YadaGallerySlide_id bigint not null, text6local varchar(2048), locale varchar(32) not null, primary key (YadaGallerySlide_id, locale)) engine=InnoDB;
create table YadaGallerySlide_text7local (YadaGallerySlide_id bigint not null, text7local varchar(2048), locale varchar(32) not null, primary key (YadaGallerySlide_id, locale)) engine=InnoDB;
create table YadaGallerySlide_text8local (YadaGallerySlide_id bigint not null, text8local varchar(2048), locale varchar(32) not null, primary key (YadaGallerySlide_id, locale)) engine=InnoDB;

alter table PageModule_YadaGallerySlide1 add constraint UK_b8wgbxmhlkvdqs290rsembw5k unique (gallery1_id);
alter table PageModule_YadaGallerySlide2 add constraint UK_s7866bat3xfjhgmnee1hbblt4 unique (gallery2_id);
alter table PageModule_YadaGallerySlide3 add constraint UK_r6v5hskf2qlhfmsf3gk5xydme unique (gallery3_id);

alter table PageModule_YadaGallerySlide1 add constraint FKl53cjteqrmcdhs3qoxq52cem1 foreign key (gallery1_id) references YadaGallerySlide (id);
alter table PageModule_YadaGallerySlide1 add constraint FKmvf1rjra3cc3scyrm5ipdbppt foreign key (PageModule_id) references PageModule (id);
alter table PageModule_YadaGallerySlide2 add constraint FKmr2u5vhrhpg0v37nbys0eg5qu foreign key (gallery2_id) references YadaGallerySlide (id);
alter table PageModule_YadaGallerySlide2 add constraint FKgwm7gomq0vvyubdvfl7xrh09g foreign key (PageModule_id) references PageModule (id);
alter table PageModule_YadaGallerySlide3 add constraint FKhem2wik6hnl32f98pq20swsbo foreign key (gallery3_id) references YadaGallerySlide (id);
alter table PageModule_YadaGallerySlide3 add constraint FK5oqwmnyxpvmitxusrqkdwmx8m foreign key (PageModule_id) references PageModule (id);

alter table YadaGallerySlide add constraint FK2qcwx3dwik86t72u9xoumvmpt foreign key (image_id) references YadaAttachedFile (id);
alter table YadaGallerySlide add constraint FK5rlrwp5xr62314t7wiqw8097q foreign key (video_id) references YadaAttachedFile (id);
alter table YadaGallerySlide_text5local add constraint FKtlgucqen7h79farj8vt1j1ltw foreign key (YadaGallerySlide_id) references YadaGallerySlide (id);
alter table YadaGallerySlide_text6local add constraint FK9bbf7hwefi5hfow41xc9lnsi6 foreign key (YadaGallerySlide_id) references YadaGallerySlide (id);
alter table YadaGallerySlide_text7local add constraint FKcyxt85cax5gty0gi3oy4sfumq foreign key (YadaGallerySlide_id) references YadaGallerySlide (id);
alter table YadaGallerySlide_text8local add constraint FK64m13ka5jmpm6yjdwyfkp0p3l foreign key (YadaGallerySlide_id) references YadaGallerySlide (id);

