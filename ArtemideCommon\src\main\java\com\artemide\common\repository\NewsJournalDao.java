package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.NewsJournal;

@Repository
@Transactional(readOnly = true)
public class NewsJournalDao {

    @PersistenceContext EntityManager em;

    /**
     * Aggiorna la topNews a false per tutti
     */
    @Transactional(readOnly = false)
    public int setFalseNewsTop() {
        String sql = "UPDATE NewsJournal SET topNews=false";
        return em.createNativeQuery(sql)
            .executeUpdate();
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from NewsJournal", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public NewsJournal save(NewsJournal entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<NewsJournal> findById(Long entityId) {
        NewsJournal result = em.find(NewsJournal.class, entityId);
        return Optional.ofNullable(result);
    }

    public NewsJournal findOne(Long entityId) {
        return em.find(NewsJournal.class, entityId);
    }

    public List<NewsJournal> findAll() {
        return em.createQuery("from NewsJournal", NewsJournal.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<NewsJournal> batchToSave) {
        for (NewsJournal entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(NewsJournal entity) {
        em.remove(entity);
    }
}
