## Aggiunta colonne emissione, per il configuratore:  vale 0 per default, 1 per "diretta+indiretta"

ALTER TABLE Prodotto ADD COLUMN emissione integer not null AFTER emergencyDuration, ADD COLUMN nonPlanar bit not null AFTER emissione; 
create table SubfamilyConfigText (id bigint not null auto_increment, localeCode varchar(5), value longtext, Subfamily_id bigint, primary key (id)) engine=InnoDB;
alter table SubfamilyConfigText add constraint FKg1wr9jwnk6mgomcff5v77lm2x foreign key (Subfamily_id) references Subfamily (id);
