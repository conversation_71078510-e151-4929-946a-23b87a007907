# Nuova classe per salvare le configurazioni  

create table ProductConfiguration (id bigint not null auto_increment, globalOptions longtext, lastSaved datetime, name varchar(128), scene longtext, version bigint not null, configurator_id bigint, userProfile_id bigint, primary key (id)) engine=InnoDB;
alter table ProductConfiguration add constraint FKvn7uy2nwa5qbmkyc0wj5hwnx foreign key (configurator_id) references Configurator (id);
alter table ProductConfiguration add constraint FKpph5rs797uovncuqcs6rijfw1 foreign key (userProfile_id) references YadaUserProfile (id);
