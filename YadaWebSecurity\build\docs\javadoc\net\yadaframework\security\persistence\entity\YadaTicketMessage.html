<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaTicketMessage (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.security.persistence.entity, class: YadaTicketMessage">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.security.persistence.entity</a></div>
<h1 title="Class YadaTicketMessage" class="title">Class YadaTicketMessage</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">net.yadaframework.security.persistence.entity.YadaUserMessage</a>&lt;<a href="YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a>&gt;
<div class="inheritance">net.yadaframework.security.persistence.entity.YadaTicketMessage</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="annotations">@Entity
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaTicketMessage</span>
<span class="extends-implements">extends <a href="YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&lt;<a href="YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a>&gt;
implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></span></div>
<div class="block">A message inside a YadaTicket.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../serialized-form.html#net.yadaframework.security.persistence.entity.YadaTicketMessage">Serialized Form</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></code></div>
<div class="col-second even-row-color"><code><a href="#yadaTicket" class="member-name-link">yadaTicket</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.security.persistence.entity.YadaUserMessage">Fields inherited from class&nbsp;net.yadaframework.security.persistence.entity.<a href="YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></h3>
<code><a href="YadaUserMessage.html#attachment">attachment</a>, <a href="YadaUserMessage.html#contentHash">contentHash</a>, <a href="YadaUserMessage.html#created">created</a>, <a href="YadaUserMessage.html#data">data</a>, <a href="YadaUserMessage.html#emailed">emailed</a>, <a href="YadaUserMessage.html#id">id</a>, <a href="YadaUserMessage.html#message">message</a>, <a href="YadaUserMessage.html#modified">modified</a>, <a href="YadaUserMessage.html#priority">priority</a>, <a href="YadaUserMessage.html#readByRecipient">readByRecipient</a>, <a href="YadaUserMessage.html#recipient">recipient</a>, <a href="YadaUserMessage.html#sender">sender</a>, <a href="YadaUserMessage.html#stackable">stackable</a>, <a href="YadaUserMessage.html#stackSize">stackSize</a>, <a href="YadaUserMessage.html#status">status</a>, <a href="YadaUserMessage.html#title">title</a>, <a href="YadaUserMessage.html#type">type</a>, <a href="YadaUserMessage.html#version">version</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaTicketMessage</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getYadaTicket()" class="member-name-link">getYadaTicket</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setYadaTicket(net.yadaframework.security.persistence.entity.YadaTicket)" class="member-name-link">setYadaTicket</a><wbr>(<a href="YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a>&nbsp;yadaTicket)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-net.yadaframework.security.persistence.entity.YadaUserMessage">Methods inherited from class&nbsp;net.yadaframework.security.persistence.entity.<a href="YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></h3>
<code><a href="YadaUserMessage.html#addAttachment(net.yadaframework.persistence.entity.YadaAttachedFile)">addAttachment</a>, <a href="YadaUserMessage.html#computeHash()">computeHash</a>, <a href="YadaUserMessage.html#getAttachment()">getAttachment</a>, <a href="YadaUserMessage.html#getContentHash()">getContentHash</a>, <a href="YadaUserMessage.html#getCreated()">getCreated</a>, <a href="YadaUserMessage.html#getData()">getData</a>, <a href="YadaUserMessage.html#getDT_RowClass()">getDT_RowClass</a>, <a href="YadaUserMessage.html#getId()">getId</a>, <a href="YadaUserMessage.html#getLastDate()">getLastDate</a>, <a href="YadaUserMessage.html#getMessage()">getMessage</a>, <a href="YadaUserMessage.html#getModified()">getModified</a>, <a href="YadaUserMessage.html#getPriority()">getPriority</a>, <a href="YadaUserMessage.html#getReceiverName()">getReceiverName</a>, <a href="YadaUserMessage.html#getRecipient()">getRecipient</a>, <a href="YadaUserMessage.html#getSender()">getSender</a>, <a href="YadaUserMessage.html#getSenderName()">getSenderName</a>, <a href="YadaUserMessage.html#getStackSize()">getStackSize</a>, <a href="YadaUserMessage.html#getStatus()">getStatus</a>, <a href="YadaUserMessage.html#getTimestampAsRelative(java.util.Locale)">getTimestampAsRelative</a>, <a href="YadaUserMessage.html#getTitle()">getTitle</a>, <a href="YadaUserMessage.html#getType()">getType</a>, <a href="YadaUserMessage.html#getVersion()">getVersion</a>, <a href="YadaUserMessage.html#incrementStack()">incrementStack</a>, <a href="YadaUserMessage.html#init()">init</a>, <a href="YadaUserMessage.html#isEmailed()">isEmailed</a>, <a href="YadaUserMessage.html#isReadByRecipient()">isReadByRecipient</a>, <a href="YadaUserMessage.html#isStackable()">isStackable</a>, <a href="YadaUserMessage.html#setAttachment(java.util.List)">setAttachment</a>, <a href="YadaUserMessage.html#setContentHash(int)">setContentHash</a>, <a href="YadaUserMessage.html#setCreated(java.util.List)">setCreated</a>, <a href="YadaUserMessage.html#setData(java.lang.String)">setData</a>, <a href="YadaUserMessage.html#setEmailed(boolean)">setEmailed</a>, <a href="YadaUserMessage.html#setId(java.lang.Long)">setId</a>, <a href="YadaUserMessage.html#setInitialDate()">setInitialDate</a>, <a href="YadaUserMessage.html#setMessage(java.lang.String)">setMessage</a>, <a href="YadaUserMessage.html#setMessageEscaped(java.lang.String)">setMessageEscaped</a>, <a href="YadaUserMessage.html#setModified(java.util.Date)">setModified</a>, <a href="YadaUserMessage.html#setPriority(int)">setPriority</a>, <a href="YadaUserMessage.html#setReadByRecipient(boolean)">setReadByRecipient</a>, <a href="YadaUserMessage.html#setRecipient(net.yadaframework.security.persistence.entity.YadaUserProfile)">setRecipient</a>, <a href="YadaUserMessage.html#setSender(net.yadaframework.security.persistence.entity.YadaUserProfile)">setSender</a>, <a href="YadaUserMessage.html#setStackable(boolean)">setStackable</a>, <a href="YadaUserMessage.html#setStackSize(int)">setStackSize</a>, <a href="YadaUserMessage.html#setStatus(java.lang.Integer)">setStatus</a>, <a href="YadaUserMessage.html#setTitle(java.lang.String)">setTitle</a>, <a href="YadaUserMessage.html#setType(net.yadaframework.persistence.entity.YadaPersistentEnum)">setType</a>, <a href="YadaUserMessage.html#setType(YLE)">setType</a>, <a href="YadaUserMessage.html#setVersion(long)">setVersion</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="yadaTicket">
<h3>yadaTicket</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></span>&nbsp;<span class="element-name">yadaTicket</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaTicketMessage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaTicketMessage</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getYadaTicket()">
<h3>getYadaTicket</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></span>&nbsp;<span class="element-name">getYadaTicket</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setYadaTicket(net.yadaframework.security.persistence.entity.YadaTicket)">
<h3>setYadaTicket</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setYadaTicket</span><wbr><span class="parameters">(<a href="YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a>&nbsp;yadaTicket)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
