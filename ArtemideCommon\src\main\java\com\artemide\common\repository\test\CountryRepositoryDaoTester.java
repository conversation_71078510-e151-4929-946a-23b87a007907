package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.repository.CountryRepoDao;
import com.artemide.common.repository.CountryRepository;
import com.yr.entity.Country;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies CountryRepoDao behaves exactly like CountryRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class CountryRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private CountryRepoDao countryRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    Country entity = entityManager.find(Country.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            entityManager.createQuery("DELETE FROM Country c WHERE c.iso3 LIKE 'TST%' OR c.iso3 LIKE 'INT%'")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private Country createTestEntity(String iso3) {
        Country entity = new Country();
        entity.setIso3(iso3);
        // Set other required fields as needed
        return entity;
    }

    private void trackEntity(Country entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = countryRepository.count();
            long initialDaoCount = countryRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            Country testEntity = createTestEntity("TST");

            Country repoSaved = countryRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            Country testEntity2 = createTestEntity("INT");
            Country daoSaved = countryRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }
            if (!testEntity.getIso3().equals(repoSaved.getIso3())) {
                return "FAIL: Repository should preserve iso3";
            }
            if (!testEntity2.getIso3().equals(daoSaved.getIso3())) {
                return "FAIL: DAO should preserve iso3";
            }

            Optional<Country> repoFound = countryRepository.findById(repoSaved.getId());
            Optional<Country> daoFoundOptional = countryRepoDao.findById(daoSaved.getId());
            Country daoFoundDirect = countryRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }

            if (!repoSaved.getId().equals(repoFound.get().getId())) {
                return "FAIL: Repository should find correct entity";
            }
            if (!daoSaved.getId().equals(daoFoundOptional.get().getId())) {
                return "FAIL: DAO findById should find correct entity";
            }
            if (!daoSaved.getId().equals(daoFoundDirect.getId())) {
                return "FAIL: DAO findOne should find correct entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<Country> repoResults = countryRepository.findAll();
            List<Country> daoResults = countryRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            Country entityForRepo = createTestEntity("TS1");
            Country entityForDao = createTestEntity("TS2");
            
            Country savedForRepo = countryRepository.save(entityForRepo);
            Country savedForDao = countryRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!countryRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!countryRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            countryRepository.delete(savedForRepo);
            countryRepoDao.delete(savedForDao);
            entityManager.flush();

            if (countryRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (countryRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindByIso3() {
        setUp();
        try {
            Country testEntity = createTestEntity("TES");
            Country saved = countryRepository.save(testEntity);
            trackEntity(saved);
            entityManager.flush();

            Optional<Country> repoResult = countryRepository.findByIso3("TES");
            Optional<Country> daoResult = countryRepoDao.findByIso3("TES");

            if (!repoResult.isPresent()) {
                return "FAIL: Repository should find entity by iso3";
            }
            if (!daoResult.isPresent()) {
                return "FAIL: DAO should find entity by iso3";
            }
            if (!repoResult.get().getId().equals(daoResult.get().getId())) {
                return "FAIL: Repository and DAO should return same entity - Repository ID: " + repoResult.get().getId() + ", DAO ID: " + daoResult.get().getId();
            }

            // Test with non-existent iso3
            Optional<Country> repoNotFound = countryRepository.findByIso3("XXX");
            Optional<Country> daoNotFound = countryRepoDao.findByIso3("XXX");

            if (repoNotFound.isPresent()) {
                return "FAIL: Repository should not find non-existent iso3";
            }
            if (daoNotFound.isPresent()) {
                return "FAIL: DAO should not find non-existent iso3";
            }
            
            return "PASS: findByIso3() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== CountryRepository vs CountryRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String findByIso3Result = testFindByIso3();
            report.append("5. FindByIso3 Test: ").append(findByIso3Result).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - CountryRepoDao behaves exactly like CountryRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
