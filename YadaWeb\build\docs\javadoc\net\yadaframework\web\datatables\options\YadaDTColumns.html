<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaDTColumns (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.datatables.options, class: YadaDTColumns">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.datatables.options</a></div>
<h1 title="Class YadaDTColumns" class="title">Class YadaDTColumns</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">net.yadaframework.core.YadaFluentBase</a>&lt;<a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>&gt;
<div class="inheritance">net.yadaframework.web.datatables.options.YadaDTColumns</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></code>, <code><a href="../proxy/YadaDTColumnsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTColumnsProxy</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaDTColumns</span>
<span class="extends-implements">extends <a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;<a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>&gt;</span></div>
<div class="block">Defines a column in DataTables.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/columns">DataTables Reference: columns</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ariaTitle" class="member-name-link">ariaTitle</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#cellType" class="member-name-link">cellType</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#className" class="member-name-link">className</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#contentPadding" class="member-name-link">contentPadding</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#createdCell" class="member-name-link">createdCell</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color"><code><a href="#data" class="member-name-link">data</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#dataFunction" class="member-name-link">dataFunction</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#defaultContent" class="member-name-link">defaultContent</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#footer" class="member-name-link">footer</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#name" class="member-name-link">name</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#orderable" class="member-name-link">orderable</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#orderData" class="member-name-link">orderData</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#orderDataType" class="member-name-link">orderDataType</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#orderSequence" class="member-name-link">orderSequence</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#render" class="member-name-link">render</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second odd-row-color"><code><a href="#responsivePriority" class="member-name-link">responsivePriority</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#searchable" class="member-name-link">searchable</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#title" class="member-name-link">title</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#visible" class="member-name-link">visible</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#width" class="member-name-link">width</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.core.YadaFluentBase">Fields inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#parent">parent</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(net.yadaframework.web.datatables.options.YadaDTOptions)" class="member-name-link">YadaDTColumns</a><wbr>(<a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>&nbsp;parent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtAriaTitle(java.lang.String)" class="member-name-link">dtAriaTitle</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ariaTitle)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtCellType(java.lang.String)" class="member-name-link">dtCellType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cellType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtClassName(java.lang.String)" class="member-name-link">dtClassName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtContentPadding(java.lang.String)" class="member-name-link">dtContentPadding</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;contentPadding)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtCreatedCell(java.lang.String)" class="member-name-link">dtCreatedCell</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;createdCell)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtData(java.lang.Integer)" class="member-name-link">dtData</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;index)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This property can be used to read and write data to and from any data source property.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtData(java.lang.String)" class="member-name-link">dtData</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This property can be used to read and write data to and from any data source property.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDataFunction(java.lang.String)" class="member-name-link">dtDataFunction</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;function)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">This property can be used to read and write data to and from any data source property.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDataNull()" class="member-name-link">dtDataNull</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Use the original data source for the row rather than plucking data directly from it.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDefaultContent(java.lang.String)" class="member-name-link">dtDefaultContent</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;htmlContent)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Static content in a column or default content when the data source is null.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtFooter(java.lang.String)" class="member-name-link">dtFooter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;footer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtName(java.lang.String)" class="member-name-link">dtName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a descriptive name for a column.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderableOff()" class="member-name-link">dtOrderableOff</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disable ordering for the column.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderData(java.lang.Integer)" class="member-name-link">dtOrderData</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;orderData)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderDataType(java.lang.String)" class="member-name-link">dtOrderDataType</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;orderDataType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderSequence(java.lang.String)" class="member-name-link">dtOrderSequence</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;orderSequence)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtRender(java.lang.String)" class="member-name-link">dtRender</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;renderFunction)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtResponsivePriority(int)" class="member-name-link">dtResponsivePriority</a><wbr>(int&nbsp;responsivePriority)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">In a responsive table control the order in which columns are hidden.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtSearchableOff()" class="member-name-link">dtSearchableOff</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disables searching for this column.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTitle(java.lang.String)" class="member-name-link">dtTitle</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtVisibleOff()" class="member-name-link">dtVisibleOff</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets a column as not visible.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtWidth(java.lang.String)" class="member-name-link">dtWidth</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;width)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-net.yadaframework.core.YadaFluentBase">Methods inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#back()">back</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="ariaTitle">
<h3>ariaTitle</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ariaTitle</span></div>
</section>
</li>
<li>
<section class="detail" id="cellType">
<h3>cellType</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">cellType</span></div>
</section>
</li>
<li>
<section class="detail" id="className">
<h3>className</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">className</span></div>
</section>
</li>
<li>
<section class="detail" id="contentPadding">
<h3>contentPadding</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">contentPadding</span></div>
</section>
</li>
<li>
<section class="detail" id="createdCell">
<h3>createdCell</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">createdCell</span></div>
</section>
</li>
<li>
<section class="detail" id="data">
<h3>data</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">data</span></div>
</section>
</li>
<li>
<section class="detail" id="dataFunction">
<h3>dataFunction</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">dataFunction</span></div>
</section>
</li>
<li>
<section class="detail" id="defaultContent">
<h3>defaultContent</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">defaultContent</span></div>
</section>
</li>
<li>
<section class="detail" id="footer">
<h3>footer</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">footer</span></div>
</section>
</li>
<li>
<section class="detail" id="name">
<h3>name</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">name</span></div>
</section>
</li>
<li>
<section class="detail" id="orderable">
<h3>orderable</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">orderable</span></div>
</section>
</li>
<li>
<section class="detail" id="orderData">
<h3>orderData</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</span>&nbsp;<span class="element-name">orderData</span></div>
</section>
</li>
<li>
<section class="detail" id="orderDataType">
<h3>orderDataType</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">orderDataType</span></div>
</section>
</li>
<li>
<section class="detail" id="orderSequence">
<h3>orderSequence</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">orderSequence</span></div>
</section>
</li>
<li>
<section class="detail" id="render">
<h3>render</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">render</span></div>
</section>
</li>
<li>
<section class="detail" id="searchable">
<h3>searchable</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">searchable</span></div>
</section>
</li>
<li>
<section class="detail" id="title">
<h3>title</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">title</span></div>
</section>
</li>
<li>
<section class="detail" id="visible">
<h3>visible</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">visible</span></div>
</section>
</li>
<li>
<section class="detail" id="width">
<h3>width</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">width</span></div>
</section>
</li>
<li>
<section class="detail" id="responsivePriority">
<h3>responsivePriority</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">responsivePriority</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(net.yadaframework.web.datatables.options.YadaDTOptions)">
<h3>YadaDTColumns</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaDTColumns</span><wbr><span class="parameters">(<a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>&nbsp;parent)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="dtAriaTitle(java.lang.String)">
<h3>dtAriaTitle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtAriaTitle</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ariaTitle)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ariaTitle</code> - The ARIA label for the column for accessibility purposes.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.ariaTitle">DataTables Reference: columns.ariaTitle</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtCellType(java.lang.String)">
<h3>dtCellType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtCellType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cellType)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cellType</code> - The cell type for the column (e.g., "td" or "th").</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.cellType">DataTables Reference: columns.cellType</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtClassName(java.lang.String)">
<h3>dtClassName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtClassName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>className</code> - The class name to be added to all cells in the column.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.className">DataTables Reference: columns.className</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtContentPadding(java.lang.String)">
<h3>dtContentPadding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtContentPadding</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;contentPadding)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>contentPadding</code> - The padding to be applied to the content of the column.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.contentPadding">DataTables Reference: columns.contentPadding</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtCreatedCell(java.lang.String)">
<h3>dtCreatedCell</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtCreatedCell</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;createdCell)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>createdCell</code> - The function to be executed when a cell is created.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.createdCell">DataTables Reference: columns.createdCell</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtDataNull()">
<h3>dtDataNull</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtDataNull</span>()</div>
<div class="block">Use the original data source for the row rather than plucking data directly from it.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#dtData(java.lang.String)"><code>dtData(String)</code></a></li>
<li><a href="https://datatables.net/reference/option/columns.data">DataTables Reference: columns.data</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtData(java.lang.Integer)">
<h3>dtData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtData</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;index)</span></div>
<div class="block">This property can be used to read and write data to and from any data source property.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>index</code> - Treated as an array index for the data source. 
                        This is the default that DataTables uses (incrementally increased for each column).</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#dtData(java.lang.String)"><code>dtData(String)</code></a></li>
<li><a href="https://datatables.net/reference/option/columns.data">DataTables Reference: columns.data</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtData(java.lang.String)">
<h3>dtData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtData</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">This property can be used to read and write data to and from any data source property.
 Set to null to use the original data source for the row</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - The data source for the column, eventually as an array notation</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#dtDataFunction(java.lang.String)"><code>dtDataFunction(String)</code></a></li>
<li><a href="https://datatables.net/reference/option/columns.data">DataTables Reference: columns.data</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtDataFunction(java.lang.String)">
<h3>dtDataFunction</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtDataFunction</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;function)</span></div>
<div class="block">This property can be used to read and write data to and from any data source property.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>function</code> - The function given will be executed whenever DataTables needs to set 
        or get the data for a cell in the column.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#dtData(java.lang.String)"><code>dtData(String)</code></a></li>
<li><a href="https://datatables.net/reference/option/columns.data">DataTables Reference: columns.data</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtDefaultContent(java.lang.String)">
<h3>dtDefaultContent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtDefaultContent</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;htmlContent)</span></div>
<div class="block">Static content in a column or default content when the data source is null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>htmlContent</code> - The default content for the column if the data source is null.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.defaultContent">DataTables Reference: columns.defaultContent</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtFooter(java.lang.String)">
<h3>dtFooter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtFooter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;footer)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>footer</code> - The content for the footer of the column.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.footer">DataTables Reference: columns.footer</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtName(java.lang.String)">
<h3>dtName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Set a descriptive name for a column.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - The name for the column.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.name">DataTables Reference: columns.name</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderData(java.lang.Integer)">
<h3>dtOrderData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtOrderData</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;orderData)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>orderData</code> - The columns to order by when this column is sorted.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.orderData">DataTables Reference: columns.orderData</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderDataType(java.lang.String)">
<h3>dtOrderDataType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtOrderDataType</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;orderDataType)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>orderDataType</code> - The ordering data type for the column.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.orderDataType">DataTables Reference: columns.orderDataType</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderSequence(java.lang.String)">
<h3>dtOrderSequence</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtOrderSequence</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;orderSequence)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>orderSequence</code> - The ordering sequence for the column.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.orderSequence">DataTables Reference: columns.orderSequence</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderableOff()">
<h3>dtOrderableOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtOrderableOff</span>()</div>
<div class="block">Disable ordering for the column.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.orderable">DataTables Reference: columns.orderable</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtRender(java.lang.String)">
<h3>dtRender</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtRender</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;renderFunction)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>renderFunction</code> - The rendering function for the column's data, with no parentheses.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.render">DataTables Reference: columns.render</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtResponsivePriority(int)">
<h3>dtResponsivePriority</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtResponsivePriority</span><wbr><span class="parameters">(int&nbsp;responsivePriority)</span></div>
<div class="block">In a responsive table control the order in which columns are hidden.
 Responsive will automatically remove columns from the right-hand-side 
 of the table when a table is too wide for a given display, unless this value is set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>responsivePriority</code> - The priority is an integer value where lower numbers
                        are given a higher priority (i.e. a column with priority 2 will be 
                        hidden before a column with priority 1). The default is 10000.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.responsivePriority">DataTables Reference: columns.responsivePriority</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtSearchableOff()">
<h3>dtSearchableOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtSearchableOff</span>()</div>
<div class="block">Disables searching for this column.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.searchable">DataTables Reference: columns.searchable</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtTitle(java.lang.String)">
<h3>dtTitle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtTitle</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - The title for the column.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.title">DataTables Reference: columns.title</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtVisibleOff()">
<h3>dtVisibleOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtVisibleOff</span>()</div>
<div class="block">Sets a column as not visible.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.visible">DataTables Reference: columns.visible</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtWidth(java.lang.String)">
<h3>dtWidth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">dtWidth</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;width)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>width</code> - The width of the column.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.width">DataTables Reference: columns.width</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
