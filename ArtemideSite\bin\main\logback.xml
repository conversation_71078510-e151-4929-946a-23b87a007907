<!-- Logback configuration for development environments -->
<configuration scan="true" scanPeriod="5 seconds" debug="false">
	<logger name="ch.qos.logback" level="warn"/>

	<appender name="FILE" class="ch.qos.logback.core.FileAppender">
		<file>/srv/amddev/log/site.log</file>
		<append>false</append>
		<encoder>
			<pattern>%d :%X{session}:%X{remoteIp}:%X{username} [%thread] %-5level %logger{36} - %msg%n
			</pattern>
		</encoder>
	</appender>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>dev %d{HH:mm:ss} %-5level %logger{36} - %X{mdc}: %msg%n</pattern>
		</encoder>
	</appender>

<!-- 
    <logger name="org.springframework.security" level="warn"/>
    <logger name="freemarker" level="warn"/>
    <logger name="org.springframework" level="warn"/>
    <logger name="com.opensymphony" level="warn"/>
    <logger name="com.mchange.v2.c3p0" level="warn"/>
    <logger name="org.apache.struts2" level="warn"/>
    <logger name="org.hibernate" level="warn"/>
    <logger name="org.apache" level="warn"/>
	<logger name="org.hibernate.event.def.AbstractFlushingEventListener" level="OFF" /> 
	<logger name="org.hibernate.util.JDBCExceptionReporter" level="OFF" /> 
 
 
-->
 
    <logger name="org.springframework.core" level="INFO"/>
    <logger name="org.springframework.web" level="INFO"/>
    <logger name="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping" level="ERROR"/>
    <logger name="org.springframework.jndi" level="INFO"/>
    <logger name="org.springframework.jdbc.datasource.DataSourceUtils" level="INFO"/>
    <logger name="org.springframework.beans" level="INFO"/>
    <logger name="org.springframework.context" level="INFO"/>
    <logger name="org.springframework.ui" level="INFO"/>
    <logger name="org.springframework.security" level="INFO"/>
    <logger name="org.springframework.aop.framework.autoproxy.InfrastructureAdvisorAutoProxyCreator" level="INFO"/>
    <logger name="org.springframework.aop.framework.CglibAopProxy" level="INFO"/>
    <logger name="org.springframework.aop.framework.JdkDynamicAopProxy" level="INFO"/>
    <logger name="org.springframework.orm.jpa.JpaTransactionManager" level="INFO"/>
    <logger name="org.springframework.orm.jpa.EntityManagerFactoryUtils" level="INFO"/>
    <logger name="org.springframework.orm.jpa.SharedEntityManagerCreator" level="INFO"/>
    <logger name="org.springframework.transaction.annotation.AnnotationTransactionAttributeSource" level="INFO"/>
    <logger name="org.springframework.data.repository.config.RepositoryComponentProvider" level="INFO"/>
    <logger name="org.springframework.data.jpa.repository.query.JpaQueryFactory" level="INFO"/>
    <logger name="org.springframework.data.jpa.repository.query.NamedQuery" level="INFO"/>
    <logger name="org.springframework.data.repository.config.RepositoryConfigurationDelegate" level="INFO"/>
    <logger name="org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor" level="INFO"/>

    <logger name="org.thymeleaf.templateresolver.AbstractTemplateResolver" level="INFO"/>
    <logger name="org.thymeleaf.TemplateEngine" level="INFO"/>
    <logger name="org.thymeleaf.TemplateEngine.CONFIG" level="INFO"/>
    <logger name="org.thymeleaf.TemplateRepository" level="INFO"/>
    <logger name="org.apache.commons" level="INFO"/>
    <logger name="jakarta.persistence.NamedQuery" level="INFO"/>

    <logger name="org.hibernate" level="INFO"/>

    
    <logger name="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping" level="ERROR"/>
    <logger name="org.apache.commons.configuration.DefaultConfigurationBuilder" level="ERROR"/>
    <!-- To hide WARN: bad write method arg count: public final void org.apache.commons.configuration2.AbstractConfiguration.setProperty -->
    <logger name="org.apache.commons.beanutils.FluentPropertyBeanIntrospector" level="ERROR"/> 
    
    <logger name="com.artemide" level="DEBUG"/>
    <logger name="com.yr" level="DEBUG"/>
    <logger name="net.yadaframework" level="DEBUG"/>
    
    <!-- AuditFilter does not log file requests -->
    <logger name="net.yadaframework.security.AuditFilter.files" level="ERROR"/>

	<!-- Per vedere le query native di hibernate abilitare il seguente: -->
	<!-- <logger name="org.hibernate.SQL" level="DEBUG" /> --> <!-- Same as hibernate.show_sql=true -->
    <logger name="net.yadaframework.persistence.YadaSql" level="INFO"/>
    <logger name="com.yr.babka37.entity.UploadedAttributes" level="INFO"/>
    
	<root level="INFO">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="FILE" />
	</root>

	<!-- Developer personal settings -->
	<include resource="logback-personal.xml" />
</configuration>