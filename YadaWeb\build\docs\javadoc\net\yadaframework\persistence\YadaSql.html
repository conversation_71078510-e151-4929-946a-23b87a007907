<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaSql (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.persistence, class: YadaSql">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.persistence</a></div>
<h1 title="Class YadaSql" class="title">Class YadaSql</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.persistence.YadaSql</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../core/CloneableDeep.html" title="interface in net.yadaframework.core">CloneableDeep</a></code>, <code><a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaSql</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="../core/CloneableDeep.html" title="interface in net.yadaframework.core">CloneableDeep</a></span></div>
<div class="block">Incrementally and conditionally builds a sql select/update query with MySQL syntax.
 Example: YadaSql.instance().selectFrom("select * from MyTable").query(em).getResultList();</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#enabled" class="member-name-link">enabled</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#groupBy" class="member-name-link">groupBy</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/StringBuilder.html" title="class or interface in java.lang" class="external-link">StringBuilder</a></code></div>
<div class="col-second even-row-color"><code><a href="#havingConditions" class="member-name-link">havingConditions</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#insertValues" class="member-name-link">insertValues</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/StringBuilder.html" title="class or interface in java.lang" class="external-link">StringBuilder</a></code></div>
<div class="col-second even-row-color"><code><a href="#joins" class="member-name-link">joins</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#lastSkipped" class="member-name-link">lastSkipped</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color"><code><a href="#limit" class="member-name-link">limit</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#nativeQuery" class="member-name-link">nativeQuery</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#nowInHaving" class="member-name-link">nowInHaving</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#orderBy" class="member-name-link">orderBy</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#parameters" class="member-name-link">parameters</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color"><code><a href="#parent" class="member-name-link">parent</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#pendingHavingOperand" class="member-name-link">pendingHavingOperand</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#pendingWhereOperand" class="member-name-link">pendingWhereOperand</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/StringBuilder.html" title="class or interface in java.lang" class="external-link">StringBuilder</a></code></div>
<div class="col-second even-row-color"><code><a href="#queryBuffer" class="member-name-link">queryBuffer</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected boolean</code></div>
<div class="col-second odd-row-color"><code><a href="#queryDone" class="member-name-link">queryDone</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#selectFrom" class="member-name-link">selectFrom</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#unions" class="member-name-link">unions</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/StringBuilder.html" title="class or interface in java.lang" class="external-link">StringBuilder</a></code></div>
<div class="col-second even-row-color"><code><a href="#whereConditions" class="member-name-link">whereConditions</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaSql</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected </code></div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(net.yadaframework.persistence.YadaSql,boolean)" class="member-name-link">YadaSql</a><wbr>(<a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a>&nbsp;parent,
 boolean&nbsp;enabled)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add(net.yadaframework.persistence.YadaSql)" class="member-name-link">add</a><wbr>(<a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a>&nbsp;yadaSql)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add all the joins, where, having, group, order statements and parameters of another YadaSql object.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#and()" class="member-name-link">and</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds an AND condition, but only if the previous expression was not skipped</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#and(boolean)" class="member-name-link">and</a><wbr>(boolean&nbsp;enabled)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds an AND condition if the parameter is true, but only if the previous expression was not skipped</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clearWhere()" class="member-name-link">clearWhere</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteFrom(boolean,java.lang.String)" class="member-name-link">deleteFrom</a><wbr>(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;deleteFrom)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Start a "delete from ..." query</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteFrom(java.lang.String)" class="member-name-link">deleteFrom</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;deleteFrom)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Start a "delete from ..." query</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtype(java.lang.Class)" class="member-name-link">dtype</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;targetType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a DTYPE condition - NATIVE QUERIES ONLY</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endSubexpression()" class="member-name-link">endSubexpression</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ends a where/having subexpression.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endSubexpression(boolean)" class="member-name-link">endSubexpression</a><wbr>(boolean&nbsp;enabled)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ending a subexpression is always safe even if the startSubexpression had an "enabled" condition</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#endSubexpression(java.lang.String)" class="member-name-link">endSubexpression</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;alias)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ends a where/having subexpression.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/reflect/Field.html" title="class or interface in java.lang.reflect" class="external-link">Field</a>[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getExcludedFields()" class="member-name-link">getExcludedFields</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ritorna la lista dei campi da non copiare.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getOrderByNative(net.yadaframework.web.YadaPageRequest)" class="member-name-link">getOrderByNative</a><wbr>(<a href="../web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a>&nbsp;yadaPageRequest)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns the "order by" statement in MySql syntax</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWhere()" class="member-name-link">getWhere</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the "where" section of the query, not starting with "where"</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#groupBy(boolean,java.lang.String)" class="member-name-link">groupBy</a><wbr>(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;groupBy)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#groupBy(java.lang.String)" class="member-name-link">groupBy</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;groupBy)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#having(boolean,java.lang.String)" class="member-name-link">having</a><wbr>(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;havingConditions)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#having(java.lang.String)" class="member-name-link">having</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;havingConditions)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a having condition</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#instance()" class="member-name-link">instance</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns an empty YadaSql for later use</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#join(boolean,java.lang.String)" class="member-name-link">join</a><wbr>(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;joinOn)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a join condition if not already added (the join keyword is required).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#join(java.lang.String)" class="member-name-link">join</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;joinOn)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a join condition if not already added (the join keyword is required).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#limit(java.lang.Integer)" class="member-name-link">limit</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;limit)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a limit</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>jakarta.persistence.Query</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#nativeQuery(jakarta.persistence.EntityManager)" class="member-name-link">nativeQuery</a><wbr>(jakarta.persistence.EntityManager&nbsp;em)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>jakarta.persistence.Query</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#nativeQuery(jakarta.persistence.EntityManager,java.lang.Class)" class="member-name-link">nativeQuery</a><wbr>(jakarta.persistence.EntityManager&nbsp;em,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;targetClass)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a native query that returns object instances</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>jakarta.persistence.Query</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#nativeQuery(jakarta.persistence.EntityManager,java.lang.String)" class="member-name-link">nativeQuery</a><wbr>(jakarta.persistence.EntityManager&nbsp;em,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;resultSetMapping)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#or()" class="member-name-link">or</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#or(boolean)" class="member-name-link">or</a><wbr>(boolean&nbsp;enabled)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#orderBy(boolean,java.lang.String)" class="member-name-link">orderBy</a><wbr>(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;orderBy)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#orderBy(java.lang.String)" class="member-name-link">orderBy</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;orderBy)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#orderBy(net.yadaframework.web.YadaPageRequest)" class="member-name-link">orderBy</a><wbr>(<a href="../web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a>&nbsp;yadaPageRequest)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add sorting from a YadaPageRequest, using JPA syntax</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#orderByNative(net.yadaframework.web.YadaPageRequest)" class="member-name-link">orderByNative</a><wbr>(<a href="../web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a>&nbsp;yadaPageRequest)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add sorting from a YadaPageRequest, using MySQL syntax</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#overwriteQuery(java.lang.String)" class="member-name-link">overwriteQuery</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;query)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the whole query.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>jakarta.persistence.Query</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#query(jakarta.persistence.EntityManager)" class="member-name-link">query</a><wbr>(jakarta.persistence.EntityManager&nbsp;em)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create an instance of Query for executing a Java Persistence query language statement.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T&gt;&nbsp;jakarta.persistence.TypedQuery<wbr>&lt;T&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#query(jakarta.persistence.EntityManager,java.lang.Class)" class="member-name-link">query</a><wbr>(jakarta.persistence.EntityManager&nbsp;em,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;targetClass)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create an instance of TypedQuery for executing a Java Persistence query language statement.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#selectFrom(boolean,java.lang.String)" class="member-name-link">selectFrom</a><wbr>(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;selectFrom)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Start a "select ...</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#selectFrom(java.lang.String)" class="member-name-link">selectFrom</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;selectFrom)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Start or extend a "select ...</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#selectFromReplace(java.lang.String)" class="member-name-link">selectFromReplace</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;selectFrom)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Replaces the current "select...</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set(boolean,java.lang.String)" class="member-name-link">set</a><wbr>(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nameValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Continues an update - set query with name=value, e.g.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#set(java.lang.String)" class="member-name-link">set</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nameValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Continues an update - set query with name=value, e.g.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setParameter(java.lang.String,java.lang.Long%5B%5D)" class="member-name-link">setParameter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>[]&nbsp;values)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setParameter(java.lang.String,java.lang.Object)" class="member-name-link">setParameter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">To be used before calling query() or nativeQuery().</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setParameter(java.lang.String,java.lang.String%5B%5D)" class="member-name-link">setParameter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;values)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setParameterNotNull(java.lang.String,java.lang.Object)" class="member-name-link">setParameterNotNull</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>&nbsp;</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sql()" class="member-name-link">sql</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the resulting sql</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sql(java.lang.String,java.lang.String,java.lang.String...)" class="member-name-link">sql</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;oldAliasName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newAliasName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;oldToNew)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the resulting sql after replacing aliases.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startSubexpression()" class="member-name-link">startSubexpression</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Starts a subexpression.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#startSubexpression(boolean)" class="member-name-link">startSubexpression</a><wbr>(boolean&nbsp;enabled)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Starts a subexpression.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toCount()" class="member-name-link">toCount</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Transforms a "select ...</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toCount(java.lang.String)" class="member-name-link">toCount</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sql)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toSelectFrom(boolean,java.lang.String)" class="member-name-link">toSelectFrom</a><wbr>(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newSelectFrom)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toSelectFrom(java.lang.String)" class="member-name-link">toSelectFrom</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newSelectFrom)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#type(java.lang.String,java.lang.Class)" class="member-name-link">type</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeAlias,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;targetType)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a TYPE condition - JPQL QUERIES ONLY</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#union(net.yadaframework.persistence.YadaSql)" class="member-name-link">union</a><wbr>(<a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a>&nbsp;unioned)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a query as a union of the current one.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#updateSet(java.lang.String)" class="member-name-link">updateSet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;updateSet)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Start a "update ...</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#where()" class="member-name-link">where</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add an empty where.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#where(boolean,java.lang.String)" class="member-name-link">where</a><wbr>(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;whereConditions)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a where condition</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#where(java.lang.String)" class="member-name-link">where</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;whereConditions)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a where condition</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#whereIn(java.lang.String,java.util.Collection)" class="member-name-link">whereIn</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&nbsp;values)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a "where aaa in (x, y, z)" clause.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#whereIn(java.lang.String,net.yadaframework.persistence.YadaSql)" class="member-name-link">whereIn</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName,
 <a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a>&nbsp;subselect)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a "where aaa in (select ...)" clause.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#whereNotEmpty(java.util.Collection,java.lang.String)" class="member-name-link">whereNotEmpty</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&nbsp;collection,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;whereConditions)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds a where condition only if the collection is not null and non empty</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#xor()" class="member-name-link">xor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#xor(boolean)" class="member-name-link">xor</a><wbr>(boolean&nbsp;enabled)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="parent">
<h3>parent</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">parent</span></div>
</section>
</li>
<li>
<section class="detail" id="enabled">
<h3>enabled</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">enabled</span></div>
</section>
</li>
<li>
<section class="detail" id="queryBuffer">
<h3>queryBuffer</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/StringBuilder.html" title="class or interface in java.lang" class="external-link">StringBuilder</a></span>&nbsp;<span class="element-name">queryBuffer</span></div>
</section>
</li>
<li>
<section class="detail" id="joins">
<h3>joins</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/StringBuilder.html" title="class or interface in java.lang" class="external-link">StringBuilder</a></span>&nbsp;<span class="element-name">joins</span></div>
</section>
</li>
<li>
<section class="detail" id="whereConditions">
<h3>whereConditions</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/StringBuilder.html" title="class or interface in java.lang" class="external-link">StringBuilder</a></span>&nbsp;<span class="element-name">whereConditions</span></div>
</section>
</li>
<li>
<section class="detail" id="havingConditions">
<h3>havingConditions</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/StringBuilder.html" title="class or interface in java.lang" class="external-link">StringBuilder</a></span>&nbsp;<span class="element-name">havingConditions</span></div>
</section>
</li>
<li>
<section class="detail" id="selectFrom">
<h3>selectFrom</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">selectFrom</span></div>
</section>
</li>
<li>
<section class="detail" id="groupBy">
<h3>groupBy</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">groupBy</span></div>
</section>
</li>
<li>
<section class="detail" id="orderBy">
<h3>orderBy</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">orderBy</span></div>
</section>
</li>
<li>
<section class="detail" id="limit">
<h3>limit</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">limit</span></div>
</section>
</li>
<li>
<section class="detail" id="nowInHaving">
<h3>nowInHaving</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">nowInHaving</span></div>
</section>
</li>
<li>
<section class="detail" id="lastSkipped">
<h3>lastSkipped</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">lastSkipped</span></div>
</section>
</li>
<li>
<section class="detail" id="pendingWhereOperand">
<h3>pendingWhereOperand</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">pendingWhereOperand</span></div>
</section>
</li>
<li>
<section class="detail" id="pendingHavingOperand">
<h3>pendingHavingOperand</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">pendingHavingOperand</span></div>
</section>
</li>
<li>
<section class="detail" id="nativeQuery">
<h3>nativeQuery</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">nativeQuery</span></div>
</section>
</li>
<li>
<section class="detail" id="insertValues">
<h3>insertValues</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">insertValues</span></div>
</section>
</li>
<li>
<section class="detail" id="parameters">
<h3>parameters</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;</span>&nbsp;<span class="element-name">parameters</span></div>
</section>
</li>
<li>
<section class="detail" id="queryDone">
<h3>queryDone</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">queryDone</span></div>
</section>
</li>
<li>
<section class="detail" id="unions">
<h3>unions</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a>&gt;</span>&nbsp;<span class="element-name">unions</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaSql</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">YadaSql</span>()</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(net.yadaframework.persistence.YadaSql,boolean)">
<h3>YadaSql</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">YadaSql</span><wbr><span class="parameters">(<a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a>&nbsp;parent,
 boolean&nbsp;enabled)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getOrderByNative(net.yadaframework.web.YadaPageRequest)">
<h3>getOrderByNative</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getOrderByNative</span><wbr><span class="parameters">(<a href="../web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a>&nbsp;yadaPageRequest)</span></div>
<div class="block">Returns the "order by" statement in MySql syntax</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaPageRequest</code> - </dd>
<dt>Returns:</dt>
<dd>"order by xxx" or "" if there is no sort to do</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="union(net.yadaframework.persistence.YadaSql)">
<h3>union</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">union</span><wbr><span class="parameters">(<a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a>&nbsp;unioned)</span></div>
<div class="block">Add a query as a union of the current one.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>unioned</code> - the query to add as a union</dd>
<dt>Returns:</dt>
<dd>the current object</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteFrom(boolean,java.lang.String)">
<h3>deleteFrom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">deleteFrom</span><wbr><span class="parameters">(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;deleteFrom)</span></div>
<div class="block">Start a "delete from ..." query</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enabled</code> - </dd>
<dd><code>deleteFrom</code> - like "delete from User"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteFrom(java.lang.String)">
<h3>deleteFrom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">deleteFrom</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;deleteFrom)</span></div>
<div class="block">Start a "delete from ..." query</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>deleteFrom</code> - like "delete from User"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="selectFrom(boolean,java.lang.String)">
<h3>selectFrom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">selectFrom</span><wbr><span class="parameters">(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;selectFrom)</span></div>
<div class="block">Start a "select ... from ..." query
 If this method has already been called, the new select text is inserted before the "from" after a comma</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enabled</code> - </dd>
<dd><code>selectFrom</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="selectFromReplace(java.lang.String)">
<h3>selectFromReplace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">selectFromReplace</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;selectFrom)</span></div>
<div class="block">Replaces the current "select... from..." with another one</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>selectFrom</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="selectFrom(java.lang.String)">
<h3>selectFrom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">selectFrom</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;selectFrom)</span></div>
<div class="block">Start or extend a "select ... from ..." query.
 If this method has already been called, the new select text is inserted before the "from" after a comma: "select x, y from" when the parameter is "y"
 This method also accepts a full query (with join, where etc)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>selectFrom</code> - either a "select... from..." statement or a new select section to add to the current one</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="set(java.lang.String)">
<h3>set</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">set</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nameValue)</span></div>
<div class="block">Continues an update - set query with name=value, e.g. "name=:usename"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>nameValue</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="set(boolean,java.lang.String)">
<h3>set</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">set</span><wbr><span class="parameters">(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;nameValue)</span></div>
<div class="block">Continues an update - set query with name=value, e.g. "name=:usename"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enabled</code> - </dd>
<dd><code>nameValue</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="updateSet(java.lang.String)">
<h3>updateSet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">updateSet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;updateSet)</span></div>
<div class="block">Start a "update ... set ..." query</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>updateSet</code> - e.g. "update Uset set name=:username"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="instance()">
<h3>instance</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">instance</span>()</div>
<div class="block">Returns an empty YadaSql for later use</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="join(boolean,java.lang.String)">
<h3>join</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">join</span><wbr><span class="parameters">(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;joinOn)</span></div>
<div class="block">Adds a join condition if not already added (the join keyword is required).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enabled</code> - false to prevent the join from being added. Can be null or empty.</dd>
<dd><code>joinOn</code> - </dd>
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#join(java.lang.String)"><code>join(String)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="join(java.lang.String)">
<h3>join</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">join</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;joinOn)</span></div>
<div class="block">Adds a join condition if not already added (the join keyword is required).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>joinOn</code> - a join that could include a ON operand, like "left join User on e.uid = u.id". Can be null or empty.</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="type(java.lang.String,java.lang.Class)">
<h3>type</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">type</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeAlias,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;targetType)</span></div>
<div class="block">Adds a TYPE condition - JPQL QUERIES ONLY</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attributeAlias</code> - the attribute name used in the query</dd>
<dd><code>targetType</code> - the type to query</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtype(java.lang.Class)">
<h3>dtype</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">dtype</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;?&gt;&nbsp;targetType)</span></div>
<div class="block">Adds a DTYPE condition - NATIVE QUERIES ONLY</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>dtype</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="where()">
<h3>where</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">where</span>()</div>
<div class="block">Add an empty where. Needed for example when the where is followed by a subexpression: where (a=1 or b=2) and (c=3 or d=4)
 becomes where().startSubexpression().where("a=1").or("b=2").endSubexpression().and().startSubexpression().where("c=3").or("d=4").endSubexpression()
 Usually it's clearer if you just put the subexpression in the string: .where("(a=1 or b=2)").and().where("(c=3 or d=4)")</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="where(java.lang.String)">
<h3>where</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">where</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;whereConditions)</span></div>
<div class="block">Adds a where condition</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>whereConditions</code> - a condition like "where a&gt;0" or just "a&gt;0", can be null or empty</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="where(boolean,java.lang.String)">
<h3>where</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">where</span><wbr><span class="parameters">(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;whereConditions)</span></div>
<div class="block">Adds a where condition</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enabled</code> - false to skip this statement. Any following and/or operator is also skipped.</dd>
<dd><code>whereConditions</code> - a condition like "where a&gt;0" or just "a&gt;0"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="whereNotEmpty(java.util.Collection,java.lang.String)">
<h3>whereNotEmpty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">whereNotEmpty</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&nbsp;collection,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;whereConditions)</span></div>
<div class="block">Adds a where condition only if the collection is not null and non empty</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>whereConditions</code> - a condition like "where a in :someCollection"</dd>
<dd><code>enabled</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="whereIn(java.lang.String,java.util.Collection)">
<h3>whereIn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">whereIn</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&nbsp;values)</span></div>
<div class="block">Add a "where aaa in (x, y, z)" clause. Skipped if the collection is null or empty.
 The collection is converted to a comma-separated strings.
 <b>NOTE:</b> It is always better to use collections as a parameter, like "aaa in :someCollection"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attributeName</code> - attribute or column name</dd>
<dd><code>values</code> - a list of values (e.g. integers)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="whereIn(java.lang.String,net.yadaframework.persistence.YadaSql)">
<h3>whereIn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">whereIn</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attributeName,
 <a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a>&nbsp;subselect)</span></div>
<div class="block">Add a "where aaa in (select ...)" clause.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attributeName</code> - attribute (can also be a collection) or column name</dd>
<dd><code>subselect</code> - a subselect that returns any number of results compatibile with attributeName. Parameters set on the subquery are carried over.</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="having(java.lang.String)">
<h3>having</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">having</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;havingConditions)</span></div>
<div class="block">Adds a having condition</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>havingConditions</code> - , can be null or empty</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="having(boolean,java.lang.String)">
<h3>having</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">having</span><wbr><span class="parameters">(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;havingConditions)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enabled</code> - </dd>
<dd><code>havingConditions</code> - a condition like "having a&gt;0" or just "a&gt;0"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="and(boolean)">
<h3>and</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">and</span><wbr><span class="parameters">(boolean&nbsp;enabled)</span></div>
<div class="block">Adds an AND condition if the parameter is true, but only if the previous expression was not skipped</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enabled</code> - false to ignore this call</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="and()">
<h3>and</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">and</span>()</div>
<div class="block">Adds an AND condition, but only if the previous expression was not skipped</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="or(boolean)">
<h3>or</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">or</span><wbr><span class="parameters">(boolean&nbsp;enabled)</span></div>
</section>
</li>
<li>
<section class="detail" id="or()">
<h3>or</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">or</span>()</div>
</section>
</li>
<li>
<section class="detail" id="xor(boolean)">
<h3>xor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">xor</span><wbr><span class="parameters">(boolean&nbsp;enabled)</span></div>
</section>
</li>
<li>
<section class="detail" id="xor()">
<h3>xor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">xor</span>()</div>
</section>
</li>
<li>
<section class="detail" id="startSubexpression()">
<h3>startSubexpression</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">startSubexpression</span>()</div>
<div class="block">Starts a subexpression. Be careful that the returned YadaSql object is different from the original one, so don't use the original one for ending the subexpression.</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="startSubexpression(boolean)">
<h3>startSubexpression</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">startSubexpression</span><wbr><span class="parameters">(boolean&nbsp;enabled)</span></div>
<div class="block">Starts a subexpression. Be careful that the returned YadaSql object is different from the original one, so don't use the original one for ending the subexpression.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enabled</code> - If the subexpression is disabled, the whole subquery is not included, not just the parenthesis</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="endSubexpression()">
<h3>endSubexpression</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">endSubexpression</span>()</div>
<div class="block">Ends a where/having subexpression. To be called on the object returned by the <a href="#startSubexpression()"><code>startSubexpression()</code></a> method (or any method called on that object)</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="endSubexpression(boolean)">
<h3>endSubexpression</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">endSubexpression</span><wbr><span class="parameters">(boolean&nbsp;enabled)</span></div>
<div class="block">Ending a subexpression is always safe even if the startSubexpression had an "enabled" condition</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>enabled</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="endSubexpression(java.lang.String)">
<h3>endSubexpression</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">endSubexpression</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;alias)</span></div>
<div class="block">Ends a where/having subexpression. To be called on the object returned by the <a href="#startSubexpression()"><code>startSubexpression()</code></a> method (or any method called on that object)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>alias</code> - the table alias, like in "select alias.a+alias.c from ( select 2*b as a ...) alias"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="groupBy(boolean,java.lang.String)">
<h3>groupBy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">groupBy</span><wbr><span class="parameters">(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;groupBy)</span></div>
</section>
</li>
<li>
<section class="detail" id="groupBy(java.lang.String)">
<h3>groupBy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">groupBy</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;groupBy)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>groupBy</code> - can be empty or null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="orderBy(java.lang.String)">
<h3>orderBy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">orderBy</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;orderBy)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>orderBy</code> - can be empty or null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="orderBy(boolean,java.lang.String)">
<h3>orderBy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">orderBy</span><wbr><span class="parameters">(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;orderBy)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>orderBy</code> - can be empty or null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="orderByNative(net.yadaframework.web.YadaPageRequest)">
<h3>orderByNative</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">orderByNative</span><wbr><span class="parameters">(<a href="../web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a>&nbsp;yadaPageRequest)</span></div>
<div class="block">Add sorting from a YadaPageRequest, using MySQL syntax</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaPageRequest</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="orderBy(net.yadaframework.web.YadaPageRequest)">
<h3>orderBy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">orderBy</span><wbr><span class="parameters">(<a href="../web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a>&nbsp;yadaPageRequest)</span></div>
<div class="block">Add sorting from a YadaPageRequest, using JPA syntax</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaPageRequest</code> - </dd>
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#orderByNative(net.yadaframework.web.YadaPageRequest)"><code>orderByNative(YadaPageRequest)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="limit(java.lang.Integer)">
<h3>limit</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">limit</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;limit)</span></div>
<div class="block">Set a limit</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>limit</code> - the limit, or null for not doing anything</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="clearWhere()">
<h3>clearWhere</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">clearWhere</span>()</div>
</section>
</li>
<li>
<section class="detail" id="toCount()">
<h3>toCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">toCount</span>()</div>
<div class="block">Transforms a "select ... from" to a "select count(*) from"</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="toSelectFrom(boolean,java.lang.String)">
<h3>toSelectFrom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">toSelectFrom</span><wbr><span class="parameters">(boolean&nbsp;enabled,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newSelectFrom)</span></div>
</section>
</li>
<li>
<section class="detail" id="toSelectFrom(java.lang.String)">
<h3>toSelectFrom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">toSelectFrom</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newSelectFrom)</span></div>
</section>
</li>
<li>
<section class="detail" id="toCount(java.lang.String)">
<h3>toCount</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">toCount</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sql)</span></div>
</section>
</li>
<li>
<section class="detail" id="nativeQuery(jakarta.persistence.EntityManager)">
<h3>nativeQuery</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">jakarta.persistence.Query</span>&nbsp;<span class="element-name">nativeQuery</span><wbr><span class="parameters">(jakarta.persistence.EntityManager&nbsp;em)</span></div>
</section>
</li>
<li>
<section class="detail" id="nativeQuery(jakarta.persistence.EntityManager,java.lang.String)">
<h3>nativeQuery</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">jakarta.persistence.Query</span>&nbsp;<span class="element-name">nativeQuery</span><wbr><span class="parameters">(jakarta.persistence.EntityManager&nbsp;em,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;resultSetMapping)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>em</code> - </dd>
<dd><code>resultSetMapping</code> - the name of the result set mapping, defined with a @SqlResultSetMapping annotation on the @Entity</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="nativeQuery(jakarta.persistence.EntityManager,java.lang.Class)">
<h3>nativeQuery</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">jakarta.persistence.Query</span>&nbsp;<span class="element-name">nativeQuery</span><wbr><span class="parameters">(jakarta.persistence.EntityManager&nbsp;em,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;targetClass)</span></div>
<div class="block">Create a native query that returns object instances</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>em</code> - </dd>
<dd><code>targetClass</code> - the result class</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="query(jakarta.persistence.EntityManager)">
<h3>query</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">jakarta.persistence.Query</span>&nbsp;<span class="element-name">query</span><wbr><span class="parameters">(jakarta.persistence.EntityManager&nbsp;em)</span></div>
<div class="block">Create an instance of Query for executing a Java Persistence query language statement.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>em</code> - </dd>
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><code>EntityManager.createQuery(String)</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="query(jakarta.persistence.EntityManager,java.lang.Class)">
<h3>query</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>&nbsp;<span class="return-type">jakarta.persistence.TypedQuery&lt;T&gt;</span>&nbsp;<span class="element-name">query</span><wbr><span class="parameters">(jakarta.persistence.EntityManager&nbsp;em,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;targetClass)</span></div>
<div class="block">Create an instance of TypedQuery for executing a Java Persistence query language statement. The select list of the query must contain only a single item, which must be assignable to the type specified by the resultClass argument.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>em</code> - </dd>
<dd><code>targetClass</code> - </dd>
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><code>EntityManager.createQuery(String, Class)</code></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParameter(java.lang.String,java.lang.Object)">
<h3>setParameter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">setParameter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="block">To be used before calling query() or nativeQuery(). It is ok to set a parameter that is not used in the query.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the parameter name, without the initial :</dd>
<dd><code>value</code> - the parameter value</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="add(net.yadaframework.persistence.YadaSql)">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">add</span><wbr><span class="parameters">(<a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a>&nbsp;yadaSql)</span></div>
<div class="block">Add all the joins, where, having, group, order statements and parameters of another YadaSql object.
 Joins are added only if not present already.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaSql</code> - the object to get items from, can be null to no nothing</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="setParameter(java.lang.String,java.lang.String[])">
<h3>setParameter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">setParameter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;values)</span></div>
</section>
</li>
<li>
<section class="detail" id="setParameter(java.lang.String,java.lang.Long[])">
<h3>setParameter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">setParameter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>[]&nbsp;values)</span></div>
</section>
</li>
<li>
<section class="detail" id="setParameterNotNull(java.lang.String,java.lang.Object)">
<h3>setParameterNotNull</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></span>&nbsp;<span class="element-name">setParameterNotNull</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;value)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set a parameter only when not null. Quite useless because setting a parameter that is not used has no effect.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - </dd>
<dd><code>value</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="sql()">
<h3>sql</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">sql</span>()</div>
<div class="block">Returns the resulting sql</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="overwriteQuery(java.lang.String)">
<h3>overwriteQuery</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">overwriteQuery</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;query)</span></div>
<div class="block">Sets the whole query. Useful for debugging.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>query</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sql(java.lang.String,java.lang.String,java.lang.String...)">
<h3>sql</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">sql</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;oldAliasName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newAliasName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;oldToNew)</span></div>
<div class="block">Returns the resulting sql after replacing aliases. It can be used when reusing an existing query with a new alias, to get something like
 "select u from User u where u.type=1 and u.age = (select min(u2.age) from User u2 where u2.type=1)". In this case, create a query "where XX.type=1" and use it
 twice, first replacing "XX" with "u", then replacing "XX" with "u2".</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>oldAliasName</code> - existing alias name (usually a placeholder) like "XX" in "from User XX where XX.name=...". Be sure to use unique character sequences.</dd>
<dd><code>newAliasName</code> - replacement alias name, like "user" to transform the previous example in "from User user where user.name=..."</dd>
<dd><code>oldToNew</code> - additional couples of alias substitutions, like "YY", "part", "ZZ", "cost"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getExcludedFields()">
<h3>getExcludedFields</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/reflect/Field.html" title="class or interface in java.lang.reflect" class="external-link">Field</a>[]</span>&nbsp;<span class="element-name">getExcludedFields</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="../core/CloneableFiltered.html#getExcludedFields()">CloneableFiltered</a></code></span></div>
<div class="block">Ritorna la lista dei campi da non copiare. Tornare null per non filtrare niente.
 Il campo "id" è escluso per default.
 Vedere come è implementato in Prodotto. Marchiarla @Transient
 Esempio: java.lang.reflect.Field[] result = new java.lang.reflect.Field[] {
                                Prodotto.class.getDeclaredField("codice"),
                                Prodotto.class.getDeclaredField("stato")
                        };</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../core/CloneableFiltered.html#getExcludedFields()">getExcludedFields</a></code>&nbsp;in interface&nbsp;<code><a href="../core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWhere()">
<h3>getWhere</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getWhere</span>()</div>
<div class="block">Returns the "where" section of the query, not starting with "where"</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
