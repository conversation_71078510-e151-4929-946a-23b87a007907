package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.UserProfile;

@Repository
@Transactional(readOnly = true)
public class UserProfileRepoDao {

    @PersistenceContext EntityManager em;

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from UserProfile", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public UserProfile save(UserProfile entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<UserProfile> findById(Long entityId) {
        UserProfile result = em.find(UserProfile.class, entityId);
        return Optional.ofNullable(result);
    }

    public UserProfile findOne(Long entityId) {
        return em.find(UserProfile.class, entityId);
    }

    public List<UserProfile> findAll() {
        return em.createQuery("from UserProfile", UserProfile.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<UserProfile> batchToSave) {
        for (UserProfile entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(UserProfile entity) {
        em.remove(entity);
    }
}
