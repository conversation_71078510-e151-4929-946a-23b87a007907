package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.Project;
import com.artemide.common.persistence.entity.ProjectModule;
import com.artemide.common.repository.ProjectModuleRepoDao;
import com.artemide.common.repository.ProjectModuleRepository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies ProjectModuleRepoDao behaves exactly like ProjectModuleRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class ProjectModuleRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private ProjectModuleRepository projectModuleRepository;

    @Autowired
    private ProjectModuleRepoDao projectModuleRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    ProjectModule entity = entityManager.find(ProjectModule.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            entityManager.createQuery("DELETE FROM ProjectModule p WHERE p.author LIKE 'TEST_%' OR p.author LIKE 'INTEGRATION_%'")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private ProjectModule createTestEntity(String author) {
        ProjectModule entity = new ProjectModule();
        // ProjectModule extends PageModule which doesn't have setTitle()
        // We can use setAuthor() which accepts String
        entity.setAuthor(author);
        entity.setEnabled(true);
        return entity;
    }

    private void trackEntity(ProjectModule entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = projectModuleRepository.count();
            long initialDaoCount = projectModuleRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            ProjectModule testEntity = createTestEntity("INTEGRATION_SAVE_001");

            ProjectModule repoSaved = projectModuleRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            ProjectModule testEntity2 = createTestEntity("INTEGRATION_SAVE_002");
            ProjectModule daoSaved = projectModuleRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<ProjectModule> repoFound = projectModuleRepository.findById(repoSaved.getId());
            Optional<ProjectModule> daoFoundOptional = projectModuleRepoDao.findById(daoSaved.getId());
            ProjectModule daoFoundDirect = projectModuleRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<ProjectModule> repoResults = projectModuleRepository.findAll();
            List<ProjectModule> daoResults = projectModuleRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            ProjectModule entityForRepo = createTestEntity("INTEGRATION_DELETE_001");
            ProjectModule entityForDao = createTestEntity("INTEGRATION_DELETE_002");
            
            ProjectModule savedForRepo = projectModuleRepository.save(entityForRepo);
            ProjectModule savedForDao = projectModuleRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!projectModuleRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!projectModuleRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            projectModuleRepository.delete(savedForRepo);
            projectModuleRepoDao.delete(savedForDao);
            entityManager.flush();

            if (projectModuleRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (projectModuleRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAllOrderByProject() {
        setUp();
        try {
            // Get first available Project ID using DbUtil
            java.util.Optional<Long> projectIdOpt = DbUtil.findFirstId(entityManager, Project.class);
            if (!projectIdOpt.isPresent()) {
                return "SKIP: No Project records found in database for testing";
            }
            
            Project testProject = new Project();
            testProject.setId(projectIdOpt.get());
            
            List<ProjectModule> repoResult = projectModuleRepository.findAllOrderByProject(testProject);
            List<ProjectModule> daoResult = projectModuleRepoDao.findAllOrderByProject(testProject);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findAllOrderByProject results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findAllOrderByProject() test successful";
        } finally {
            tearDown();
        }
    }

    @Transactional(readOnly = false)
    public String testRemoveImageFromCarrousel() {
        setUp();
        try {
            // Create test entity first
            ProjectModule testEntity = createTestEntity("INTEGRATION_IMAGE_001");
            ProjectModule saved = projectModuleRepository.save(testEntity);
            trackEntity(saved);
            entityManager.flush();

            // Test the method - this modifies associations
            Long testProjectModuleId = saved.getId();
            // Get a real image ID from database
            Optional<Long> imageIdOpt = DbUtil.findFirstId(entityManager, com.yr.babka37.entity.UploadedFile.class);
            if (!imageIdOpt.isPresent()) {
                return "SKIP: No UploadedFile records found for removeImageFromCarrousel test";
            }
            Long testImageId = imageIdOpt.get();
            
            projectModuleRepository.removeImageFromCarrousel(testProjectModuleId, testImageId);
            projectModuleRepoDao.removeImageFromCarrousel(testProjectModuleId, testImageId);
            entityManager.flush();
            
            // Both methods should execute without error
            return "PASS: removeImageFromCarrousel() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== ProjectModuleRepository vs ProjectModuleRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String findAllOrderByProjectResult = testFindAllOrderByProject();
            report.append("5. FindAllOrderByProject Test: ").append(findAllOrderByProjectResult).append("\n");
            
            String removeImageFromCarrouselResult = testRemoveImageFromCarrousel();
            report.append("6. RemoveImageFromCarrousel Test: ").append(removeImageFromCarrouselResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - ProjectModuleRepoDao behaves exactly like ProjectModuleRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
