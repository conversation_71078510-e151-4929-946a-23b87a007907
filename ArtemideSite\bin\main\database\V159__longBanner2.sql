# nuovi campi per Prodotto e ProdottoElectrical

alter table HomePage add column enableLongBanner2 bit not null, add column moveLongBanner2 bit not null;
create table HomeLongBannerEventDateTwo (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeLongBannerEventLocationTwo (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeLongBannerExtraLineTwo (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeLongBannerLinkTwo (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeLongBannerSubtitleTwo (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeLongBannerTitleTwo (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;

alter table HomeLongBannerEventDateTwo add constraint FKi71hofyuxv1opfwlsu6wn5ur1 foreign key (HomePage_id) references HomePage (id);
alter table HomeLongBannerEventLocationTwo add constraint FK8ir97foa5x54aguc2t14a7xio foreign key (HomePage_id) references HomePage (id);
alter table HomeLongBannerExtraLineTwo add constraint FKf6afb753322iy4u8s755bry7b foreign key (HomePage_id) references HomePage (id);
alter table HomeLongBannerLinkTwo add constraint FKkcvml7plim6iw0tml9qx32h4f foreign key (HomePage_id) references HomePage (id);
alter table HomeLongBannerSubtitleTwo add constraint FKsxy69faofgm219c12r1pgrdid foreign key (HomePage_id) references HomePage (id);
alter table HomeLongBannerTitleTwo add constraint FK544ggjkc5jhjadd6vu0c4c8bb foreign key (HomePage_id) references HomePage (id);
