<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaNotifyData (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.components, class: YadaNotifyData">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Constr</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.components</a></div>
<h1 title="Class YadaNotifyData" class="title">Class YadaNotifyData</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.components.YadaNotifyData</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaNotifyData</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#add()" class="member-name-link">add</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Makes the notification active.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addClasses(java.lang.String)" class="member-name-link">addClasses</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a space-separated list of classes to add to the notification .modal-dialog element</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#autoclose(long)" class="member-name-link">autoclose</a><wbr>(long&nbsp;milliseconds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the autoclose time in milliseconds - no close button is rendered</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#callScript(java.lang.String)" class="member-name-link">callScript</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;scriptId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a script id to call when opening the notification modal.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#center()" class="member-name-link">center</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Vertically center the modal (with Bootstrap 4)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#error()" class="member-name-link">error</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification severity</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#error(boolean)" class="member-name-link">error</a><wbr>(boolean&nbsp;active)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification severity if active is true</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#info()" class="member-name-link">info</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification severity</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#info(boolean)" class="member-name-link">info</a><wbr>(boolean&nbsp;active)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification severity if active is true</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#keepLoader()" class="member-name-link">keepLoader</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The notification modal does not turn off any existing loader</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#keepLoader(java.lang.Boolean)" class="member-name-link">keepLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;keepLoader)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The notification modal does not turn off any existing loader when keepLoader is true</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#message(java.lang.String)" class="member-name-link">message</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification message.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#message(java.lang.String,java.lang.Object...)" class="member-name-link">message</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;messageFormat,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;params)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification message with slf4j-style parameters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#messageKey(java.lang.String...)" class="member-name-link">messageKey</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;messageKeyAndArgs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification message using localization.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#ok()" class="member-name-link">ok</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification severity.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#ok(boolean)" class="member-name-link">ok</a><wbr>(boolean&nbsp;active)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification severity if active is true</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#redirectOnClose(java.lang.String)" class="member-name-link">redirectOnClose</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The page will redirect on modal close</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#reloadOnClose()" class="member-name-link">reloadOnClose</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the page to reload when the modal is closed</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTitle(java.lang.String)" class="member-name-link">setTitle</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification title</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setTitleKey(java.lang.String...)" class="member-name-link">setTitleKey</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the notification title using localization</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="add()">
<h3>add</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">add</span>()</div>
<div class="block">Makes the notification active. Can be called many times to add different notifications, even on the same instance, after setting a new title/message/severity.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>If used with a Model, returns the view of the notification modal.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="../core/YadaConfiguration.html#getNotifyModalView()"><code>YadaConfiguration.getNotifyModalView()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="keepLoader()">
<h3>keepLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">keepLoader</span>()</div>
<div class="block">The notification modal does not turn off any existing loader</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="keepLoader(java.lang.Boolean)">
<h3>keepLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">keepLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;keepLoader)</span></div>
<div class="block">The notification modal does not turn off any existing loader when keepLoader is true</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTitle(java.lang.String)">
<h3>setTitle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">setTitle</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</span></div>
<div class="block">Set the notification title</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="setTitleKey(java.lang.String...)">
<h3>setTitleKey</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">setTitleKey</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</span></div>
<div class="block">Set the notification title using localization</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>titleKeyAndArgs</code> - the title key, followed by optional arguments to be replaced in the localized value</dd>
<dt>Returns:</dt>
<dt>Throws:</dt>
<dd><code><a href="../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - if yadaMessageSource() hasn't been called</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="message(java.lang.String)">
<h3>message</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">message</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</span></div>
<div class="block">Set the notification message. Can be HTML.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="message(java.lang.String,java.lang.Object...)">
<h3>message</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">message</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;messageFormat,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;params)</span></div>
<div class="block">Set the notification message with slf4j-style parameters. Can be HTML.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>messageFormat</code> - the message format with slf4j syntax: use {} as placeholders</dd>
<dd><code>params</code> - values to be inserted at placeholder positions</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="autoclose(long)">
<h3>autoclose</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">autoclose</span><wbr><span class="parameters">(long&nbsp;milliseconds)</span></div>
<div class="block">Set the autoclose time in milliseconds - no close button is rendered</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>milliseconds</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="center()">
<h3>center</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">center</span>()</div>
<div class="block">Vertically center the modal (with Bootstrap 4)</div>
</section>
</li>
<li>
<section class="detail" id="addClasses(java.lang.String)">
<h3>addClasses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">addClasses</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;classes)</span></div>
<div class="block">Add a space-separated list of classes to add to the notification .modal-dialog element</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>classes</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="reloadOnClose()">
<h3>reloadOnClose</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">reloadOnClose</span>()</div>
<div class="block">Set the page to reload when the modal is closed</div>
</section>
</li>
<li>
<section class="detail" id="messageKey(java.lang.String...)">
<h3>messageKey</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">messageKey</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;messageKeyAndArgs)</span></div>
<div class="block">Set the notification message using localization. Can be HTML.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>messageKeyAndArgs</code> - the message key, followed by optional arguments to be replaced in the localized value</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="redirectOnClose(java.lang.String)">
<h3>redirectOnClose</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">redirectOnClose</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">The page will redirect on modal close</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - the last part of the url after the servlet context, like "/user/profile"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="callScript(java.lang.String)">
<h3>callScript</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">callScript</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;scriptId)</span></div>
<div class="block">Add a script id to call when opening the notification modal. The script must be inserted into "/script.html"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>scriptId</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ok(boolean)">
<h3>ok</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">ok</span><wbr><span class="parameters">(boolean&nbsp;active)</span></div>
<div class="block">Set the notification severity if active is true</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>active</code> - true to set the severity, false for not setting it</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="info(boolean)">
<h3>info</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">info</span><wbr><span class="parameters">(boolean&nbsp;active)</span></div>
<div class="block">Set the notification severity if active is true</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>active</code> - true to set the severity, false for not setting it</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="error(boolean)">
<h3>error</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">error</span><wbr><span class="parameters">(boolean&nbsp;active)</span></div>
<div class="block">Set the notification severity if active is true</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>active</code> - true to set the severity, false for not setting it</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="ok()">
<h3>ok</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">ok</span>()</div>
<div class="block">Set the notification severity. This is the default.</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="info()">
<h3>info</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">info</span>()</div>
<div class="block">Set the notification severity</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="error()">
<h3>error</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">error</span>()</div>
<div class="block">Set the notification severity</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
