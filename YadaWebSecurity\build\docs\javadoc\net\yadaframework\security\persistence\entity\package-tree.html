<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.security.persistence.entity Class Hierarchy (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="tree: package: net.yadaframework.security.persistence.entity">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package net.yadaframework.security.persistence.entity</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaAutoLoginToken.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaRegistrationRequest.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaSocialCredentials.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaTicket.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaUserCredentials.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaUserMessage.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&lt;YLE&gt; (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaCommentMessage.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaTicketMessage.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaTicketMessage</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
</ul>
</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaUserProfile.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="class or interface in java.lang">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/constant/Constable.html" title="class or interface in java.lang.constant" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaTicketStatus.html" class="type-name-link" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a> (implements net.yadaframework.core.YadaLocalEnum&lt;E&gt;)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaTicketType.html" class="type-name-link" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a> (implements net.yadaframework.core.YadaLocalEnum&lt;E&gt;)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="YadaUserMessageType.html" class="type-name-link" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a> (implements net.yadaframework.core.YadaLocalEnum&lt;E&gt;)</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
