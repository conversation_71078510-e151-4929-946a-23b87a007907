<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Serialized Form (YadaWebCMS '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="serialized forms">
<meta name="generator" content="javadoc/SerializedFormWriterImpl">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="serialized-form-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#serialized-form">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Serialized Form" class="title">Serialized Form</h1>
</div>
<ul class="block-list">
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/cms/persistence/entity/package-summary.html">net.yadaframework.cms.persistence.entity</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.cms.persistence.entity.YadaArticle">
<h3>Class&nbsp;<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">net.yadaframework.cms.persistence.entity.YadaArticle</a></h3>
<div class="type-signature">class YadaArticle extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>attachments</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;net.yadaframework.persistence.entity.YadaAttachedFile&gt; attachments</pre>
</li>
<li class="block-list">
<h5>chosenProductId</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> chosenProductId</pre>
</li>
<li class="block-list">
<h5>color</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt; color</pre>
</li>
<li class="block-list">
<h5>dimension</h5>
<pre><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a> dimension</pre>
</li>
<li class="block-list">
<h5>galleryImages</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;net.yadaframework.persistence.entity.YadaAttachedFile&gt; galleryImages</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>image</h5>
<pre>net.yadaframework.persistence.entity.YadaAttachedFile image</pre>
<div class="block">The main image to show in lists etc.</div>
</li>
<li class="block-list">
<h5>internalName</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> internalName</pre>
</li>
<li class="block-list">
<h5>modified</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> modified</pre>
</li>
<li class="block-list">
<h5>name</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt; name</pre>
</li>
<li class="block-list">
<h5>product</h5>
<pre><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a> product</pre>
</li>
<li class="block-list">
<h5>published</h5>
<pre>boolean published</pre>
</li>
<li class="block-list">
<h5>silhouetteImages</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;net.yadaframework.persistence.entity.YadaAttachedFile&gt; silhouetteImages</pre>
</li>
<li class="block-list">
<h5>sku</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> sku</pre>
</li>
<li class="block-list">
<h5>unitPrice</h5>
<pre>net.yadaframework.persistence.YadaMoney unitPrice</pre>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.cms.persistence.entity.YadaProduct">
<h3>Class&nbsp;<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">net.yadaframework.cms.persistence.entity.YadaProduct</a></h3>
<div class="type-signature">class YadaProduct extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>accessories</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a>&gt; accessories</pre>
</li>
<li class="block-list">
<h5>accessoryFlag</h5>
<pre>boolean accessoryFlag</pre>
<div class="block">true if the YadaProduct is an accessory</div>
</li>
<li class="block-list">
<h5>accessoryOf</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a>&gt; accessoryOf</pre>
</li>
<li class="block-list">
<h5>articles</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a>&gt; articles</pre>
</li>
<li class="block-list">
<h5>attachments</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;net.yadaframework.persistence.entity.YadaAttachedFile&gt; attachments</pre>
</li>
<li class="block-list">
<h5>categories</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;net.yadaframework.persistence.entity.YadaPersistentEnum&lt;?&gt;&gt; categories</pre>
</li>
<li class="block-list">
<h5>description</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt; description</pre>
</li>
<li class="block-list">
<h5>galleryImages</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;net.yadaframework.persistence.entity.YadaAttachedFile&gt; galleryImages</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>image</h5>
<pre>net.yadaframework.persistence.entity.YadaAttachedFile image</pre>
<div class="block">The main image to show in lists etc. (for example a thumbnail)</div>
</li>
<li class="block-list">
<h5>materials</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt; materials</pre>
</li>
<li class="block-list">
<h5>modified</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> modified</pre>
</li>
<li class="block-list">
<h5>name</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt; name</pre>
</li>
<li class="block-list">
<h5>published</h5>
<pre>boolean published</pre>
</li>
<li class="block-list">
<h5>subcategories</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;net.yadaframework.persistence.entity.YadaPersistentEnum&lt;?&gt;&gt; subcategories</pre>
</li>
<li class="block-list">
<h5>subtitle</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt; subtitle</pre>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
<li class="block-list">
<h5>year</h5>
<pre>int year</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</main>
</div>
</div>
</body>
</html>
