package com.artemide.common.repository;

import java.util.Collection;
import java.util.List;
import java.util.Locale;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.Project;
import com.yr.entity.Subfamily;

@Repository
@Transactional(readOnly = true)
public class ProjectsRepoDao {

    @PersistenceContext EntityManager em;

    /**
     * Trova tutti i prodotti pubblicati che hanno il tag indicato
     * @param tagId
     * @return
     */
    public List<Project> findByTagPublished(Long tagId) {
        String sql = "select p from Project p join p.tags t where p.enabled=true and p.publishDate <= NOW() and t.id = :tagId order by p.publishDate desc";
        return em.createQuery(sql, Project.class)
            .setParameter("tagId", tagId)
            .getResultList();
    }

    /**
     * Trova una pagina di project pubblicate
     * @param pageable
     * @return
     */
    public Slice<Project> findPublished(Pageable pageable) {
        String sql = "select p from Project p join fetch p.projectModules where p.enabled=true and p.publishDate <= NOW() order by p.publishDate desc";
        List<Project> results = em.createQuery(sql, Project.class)
            .setFirstResult((int) pageable.getOffset())
            .setMaxResults(pageable.getPageSize())
            .getResultList();
        
        boolean hasNext = results.size() == pageable.getPageSize();
        return new SliceImpl<>(results, pageable, hasNext);
    }

    /**
     * Trova tutte le Project che hanno titolo o sottotitolo contenente la stringa passata
     * @param searchString
     * @param locale
     * @return
     */
    public List<Project> search(String searchString, Locale locale) {
        String sql = "select p from Project p join p.titlePartOne o left join p.titlePartTwo t where p.enabled=true and p.publishDate <= NOW() and KEY(t) = :locale and (lower(o) like :s or lower(t) like :s)";
        return em.createQuery(sql, Project.class)
            .setParameter("s", searchString)
            .setParameter("locale", locale)
            .getResultList();
    }

    /**
     * Update a new topIndice image attachment to a News
     * @param projectId
     * @param yadaAttachedFileId
     */
    @Transactional(readOnly = false)
    public int setBigImage(Long projectId, Long yadaAttachedFileId) {
        String sql = "UPDATE Project SET topIndice_id=:yadaAttachedFileId where id=:projectId";
        return em.createNativeQuery(sql)
            .setParameter("projectId", projectId)
            .setParameter("yadaAttachedFileId", yadaAttachedFileId)
            .executeUpdate();
    }

    /**
     * Update a new thumbnail image attachment to a News
     * @param projectId
     * @param yadaAttachedFileId
     */
    @Transactional(readOnly = false)
    public int setSmallImage(Long projectId, Long yadaAttachedFileId) {
        String sql = "UPDATE Project SET thumbnail_id=:yadaAttachedFileId where id=:projectId";
        return em.createNativeQuery(sql)
            .setParameter("projectId", projectId)
            .setParameter("yadaAttachedFileId", yadaAttachedFileId)
            .executeUpdate();
    }

    /**
     * Aggiorna la topProject a false per tutti
     */
    @Transactional(readOnly = false)
    public int setFalseTopProject() {
        String sql = "UPDATE Project SET topProject=false";
        return em.createNativeQuery(sql)
            .executeUpdate();
    }

    /**
     * Trova tutti i progetti che hanno un id tra quelle indicate, prelevando anche le localitas
     * @param ids
     * @return
     */
    public List<Project> findAllWithLocalitas(Collection<Long> ids) {
        String sql = "select distinct p from Project p join fetch p.titlePartOne left join p.titlePartTwo where p.id in :ids";
        return em.createQuery(sql, Project.class)
            .setParameter("ids", ids)
            .getResultList();
    }

    public List<Subfamily> findSottofamiglie(Long projectId) {
        String sql = "select c from Project p join p.projectModules pm join pm.subfamilies c where p.id = :projectId and c.published = true";
        return em.createQuery(sql, Subfamily.class)
            .setParameter("projectId", projectId)
            .getResultList();
    }

    public Project findWithLocalitas(Long id) {
        String sql = "select p from Project p join fetch p.titlePartOne left join p.titlePartTwo where p.id = :id";
        List<Project> resultList = em.createQuery(sql, Project.class)
            .setParameter("id", id)
            .setMaxResults(1)
            .getResultList();
        return resultList.isEmpty() ? null : resultList.get(0);
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Project", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Project save(Project entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Project> findById(Long entityId) {
        Project result = em.find(Project.class, entityId);
        return Optional.ofNullable(result);
    }

    public Project findOne(Long entityId) {
        return em.find(Project.class, entityId);
    }

    public List<Project> findAll() {
        return em.createQuery("from Project", Project.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Project> batchToSave) {
        for (Project entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Project entity) {
        em.remove(entity);
    }
}
