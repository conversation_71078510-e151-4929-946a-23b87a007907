<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaSeleniumUtil (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.selenium, class: YadaSeleniumUtil">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.selenium</a></div>
<h1 title="Class YadaSeleniumUtil" class="title">Class YadaSeleniumUtil</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.selenium.YadaSeleniumUtil</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Component
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaSeleniumUtil</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final int</code></div>
<div class="col-second even-row-color"><code><a href="#DRIVER_CHROME" class="member-name-link">DRIVER_CHROME</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final int</code></div>
<div class="col-second odd-row-color"><code><a href="#DRIVER_FIREFOX" class="member-name-link">DRIVER_FIREFOX</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaSeleniumUtil</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clickByJavascript(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver)" class="member-name-link">clickByJavascript</a><wbr>(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Click on the given element using javascript.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clickByJavascript(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver,int,int,int,int)" class="member-name-link">clickByJavascript</a><wbr>(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 int&nbsp;minPercentX,
 int&nbsp;maxPercentX,
 int&nbsp;minPercentY,
 int&nbsp;maxPercentY)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Click on the given element using javascript.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.openqa.selenium.WebElement</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findById(java.lang.String,org.openqa.selenium.WebDriver)" class="member-name-link">findById</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;id,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get an element by id</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.openqa.selenium.WebElement</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findOrNull(org.openqa.selenium.SearchContext,org.openqa.selenium.By)" class="member-name-link">findOrNull</a><wbr>(org.openqa.selenium.SearchContext&nbsp;from,
 org.openqa.selenium.By&nbsp;by)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the first element matched by the selector, or null if not found</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#foundByClass(java.lang.String,org.openqa.selenium.WebDriver)" class="member-name-link">foundByClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if at least one element with the given class exists</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#foundById(java.lang.String,org.openqa.selenium.WebDriver)" class="member-name-link">foundById</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;id,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if an element with a given id exists</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#foundByText(java.util.List,java.lang.String)" class="member-name-link">foundByText</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;org.openqa.selenium.WebElement&gt;&nbsp;webElements,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if an element contains the given text (literally)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getByJavascript(java.lang.String,org.openqa.selenium.WebDriver)" class="member-name-link">getByJavascript</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;javascriptCode,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return a value calculated via javascript.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSourceSnippet(java.lang.String,java.lang.String,java.lang.String,org.openqa.selenium.WebDriver)" class="member-name-link">getSourceSnippet</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;startPattern,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;endPattern,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extractPattern,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a part of the page source.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTextIfExists(org.openqa.selenium.SearchContext,org.openqa.selenium.By)" class="member-name-link">getTextIfExists</a><wbr>(org.openqa.selenium.SearchContext&nbsp;from,
 org.openqa.selenium.By&nbsp;by)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Search for text</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.openqa.selenium.WebDriver</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeWebDriver(java.io.File,java.net.InetSocketAddress,java.lang.String,java.lang.String,java.util.Set,int)" class="member-name-link">makeWebDriver</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;customProfileDir,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/net/InetSocketAddress.html" title="class or interface in java.net" class="external-link">InetSocketAddress</a>&nbsp;proxyToUse,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;proxyUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;proxyPassword,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;org.openqa.selenium.Cookie&gt;&nbsp;cookiesToSet,
 int&nbsp;driverType)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a new browser instance positioning the window</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>org.openqa.selenium.WebDriver</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeWebDriver(java.io.File,java.net.InetSocketAddress,java.lang.String,java.lang.String,java.util.Set,int,java.lang.String)" class="member-name-link">makeWebDriver</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;customProfileDir,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/net/InetSocketAddress.html" title="class or interface in java.net" class="external-link">InetSocketAddress</a>&nbsp;proxyToUse,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;proxyUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;proxyPassword,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;org.openqa.selenium.Cookie&gt;&nbsp;cookiesToSet,
 int&nbsp;driverType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userAgent)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a new browser instance positioning the window</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#positionWindow(int,int,int,int,org.openqa.selenium.WebDriver)" class="member-name-link">positionWindow</a><wbr>(int&nbsp;posx,
 int&nbsp;posy,
 int&nbsp;width,
 int&nbsp;height,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#randomClick(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver)" class="member-name-link">randomClick</a><wbr>(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Click on the given element in a range between 20% and 80% of the dimensions</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#randomClick(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver,int,int,int,int)" class="member-name-link">randomClick</a><wbr>(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 int&nbsp;minPercentX,
 int&nbsp;maxPercentX,
 int&nbsp;minPercentY,
 int&nbsp;maxPercentY)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Click on the given element in a range between min and max % of the dimensions.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#relativeToAbsolute(java.lang.String,org.openqa.selenium.WebDriver)" class="member-name-link">relativeToAbsolute</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeAddress,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Given a relative address, computes the new full address.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runJavascript(java.lang.String,org.openqa.selenium.WebDriver,java.lang.Object...)" class="member-name-link">runJavascript</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;script,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;args)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run some javascript.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setNormalPageLoadTimeout(org.openqa.selenium.WebDriver)" class="member-name-link">setNormalPageLoadTimeout</a><wbr>(org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">From now on, page load timeout will be set to the "normale" value defined in the "pageLoadSeconds" confg property</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPageloadTimeoutSeconds(org.openqa.selenium.WebDriver,long)" class="member-name-link">setPageloadTimeoutSeconds</a><wbr>(org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeoutSeconds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the pageload timeout for the following requests</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSlowPageLoadTimeout(org.openqa.selenium.WebDriver)" class="member-name-link">setSlowPageLoadTimeout</a><wbr>(org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">From now on, page load timeout will be set to the "slow" value defined in the "slowPageLoadSeconds" confg property</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#takeScreenshot(org.openqa.selenium.WebDriver,java.nio.file.Path)" class="member-name-link">takeScreenshot</a><wbr>(org.openqa.selenium.WebDriver&nbsp;webDriver,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;toPath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Take a browser screenshot and move it to the specified path</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#typeAsHuman(org.openqa.selenium.WebElement,java.lang.String)" class="member-name-link">typeAsHuman</a><wbr>(org.openqa.selenium.WebElement&nbsp;inputField,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Insert some text slowly into a field</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#urlContains(java.lang.String,org.openqa.selenium.WebDriver)" class="member-name-link">urlContains</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;urlSegment,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the current url contains the specified string at any position</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#urlMatches(java.util.regex.Pattern,org.openqa.selenium.WebDriver)" class="member-name-link">urlMatches</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/regex/Pattern.html" title="class or interface in java.util.regex" class="external-link">Pattern</a>&nbsp;urlPattern,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the current url matches the specified pattern</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitUntilAttributeNotEmpty(org.openqa.selenium.WebElement,java.lang.String,org.openqa.selenium.WebDriver,long)" class="member-name-link">waitUntilAttributeNotEmpty</a><wbr>(org.openqa.selenium.WebElement&nbsp;element,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attribute,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Waits until the attribute contains some non-empty text</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitUntilLost(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver,long)" class="member-name-link">waitUntilLost</a><wbr>(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Waits until the element is no more attached to the DOM (stale).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitUntilPresent(java.lang.String,org.openqa.selenium.WebDriver,long)" class="member-name-link">waitUntilPresent</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Wait until the selector matches an element</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitUntilVisible(java.lang.String,org.openqa.selenium.WebDriver,long)" class="member-name-link">waitUntilVisible</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Wait until the selector matches a visible element</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitWhileEmptyText(java.lang.String,org.openqa.selenium.WebDriver,long)" class="member-name-link">waitWhileEmptyText</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Waits until the selected element contains some non-empty text</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitWhileEmptyText(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver,long)" class="member-name-link">waitWhileEmptyText</a><wbr>(org.openqa.selenium.WebElement&nbsp;element,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Waits until the selected element contains some non-empty text (warning: this method may not work as expected)</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitWhilePresent(java.lang.String,org.openqa.selenium.WebDriver,long)" class="member-name-link">waitWhilePresent</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Wait until the selector matches zero elements</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitWhileVisible(java.lang.String,org.openqa.selenium.WebDriver,long)" class="member-name-link">waitWhileVisible</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitWhileVisible(java.util.List,org.openqa.selenium.WebDriver,long)" class="member-name-link">waitWhileVisible</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;org.openqa.selenium.WebElement&gt;&nbsp;webElements,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#waitWhileVisible(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver,long)" class="member-name-link">waitWhileVisible</a><wbr>(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="DRIVER_FIREFOX">
<h3>DRIVER_FIREFOX</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DRIVER_FIREFOX</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.selenium.YadaSeleniumUtil.DRIVER_FIREFOX">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="DRIVER_CHROME">
<h3>DRIVER_CHROME</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">DRIVER_CHROME</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.selenium.YadaSeleniumUtil.DRIVER_CHROME">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaSeleniumUtil</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaSeleniumUtil</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="runJavascript(java.lang.String,org.openqa.selenium.WebDriver,java.lang.Object...)">
<h3>runJavascript</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">runJavascript</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;script,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>...&nbsp;args)</span></div>
<div class="block">Run some javascript.

 <p>
 If the script has a return value (i.e. if the script contains a <code>return</code> statement),
 then the following steps will be taken:

 <ul>
 <li>For an HTML element, this method returns a WebElement</li>
 <li>For a decimal, a Double is returned</li>
 <li>For a non-decimal number, a Long is returned</li>
 <li>For a boolean, a Boolean is returned</li>
 <li>For all other cases, a String is returned.</li>
 <li>For an array, return a List&lt;Object&gt; with each object following the rules above. We
 support nested lists.</li>
 <li>For a map, return a Map&lt;String, Object&gt; with values following the rules above.</li>
 <li>Unless the value is null or there is no return value, in which null is returned</li>
 </ul>

 <p>
 Arguments must be a number, a boolean, a String, WebElement, or a List of any combination of
 the above. An exception will be thrown if the arguments do not meet these criteria. The
 arguments will be made available to the JavaScript via the "arguments" magic variable, as if
 the function were called via "Function.apply"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>script</code> - The JavaScript to execute</dd>
<dd><code>webDriver</code> - </dd>
<dd><code>args</code> - The arguments to the script. May be empty. They will be available to the script as the arguments[] array.</dd>
<dt>Returns:</dt>
<dd>One of Boolean, Long, Double, String, List, Map or WebElement. Or null.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="urlMatches(java.util.regex.Pattern,org.openqa.selenium.WebDriver)">
<h3>urlMatches</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">urlMatches</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/regex/Pattern.html" title="class or interface in java.util.regex" class="external-link">Pattern</a>&nbsp;urlPattern,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">Returns true if the current url matches the specified pattern</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>urlPattern</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="urlContains(java.lang.String,org.openqa.selenium.WebDriver)">
<h3>urlContains</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">urlContains</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;urlSegment,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">Returns true if the current url contains the specified string at any position</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>urlSegment</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSourceSnippet(java.lang.String,java.lang.String,java.lang.String,org.openqa.selenium.WebDriver)">
<h3>getSourceSnippet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSourceSnippet</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;startPattern,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;endPattern,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extractPattern,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">Returns a part of the page source.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>startPattern</code> - regular expression that matches the start of the search area, null for the beginning of the page.
 The matched text is not part of the search area.</dd>
<dd><code>endPattern</code> - regular expression that matches the end of the search area, null for the end of the page
 The matched text is not part of the search area.</dd>
<dd><code>extractPattern</code> - regular expression with one capturing group to search in the search area. Use null to just return the search area.</dd>
<dd><code>webDriver</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getByJavascript(java.lang.String,org.openqa.selenium.WebDriver)">
<h3>getByJavascript</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getByJavascript</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;javascriptCode,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">Return a value calculated via javascript.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>javascriptCode</code> - Any valid javascript code with a return value</dd>
<dd><code>webDriver</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="typeAsHuman(org.openqa.selenium.WebElement,java.lang.String)">
<h3>typeAsHuman</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">typeAsHuman</span><wbr><span class="parameters">(org.openqa.selenium.WebElement&nbsp;inputField,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</span></div>
<div class="block">Insert some text slowly into a field</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>inputField</code> - </dd>
<dd><code>text</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSlowPageLoadTimeout(org.openqa.selenium.WebDriver)">
<h3>setSlowPageLoadTimeout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSlowPageLoadTimeout</span><wbr><span class="parameters">(org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">From now on, page load timeout will be set to the "slow" value defined in the "slowPageLoadSeconds" confg property</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>webDriver</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setNormalPageLoadTimeout(org.openqa.selenium.WebDriver)">
<h3>setNormalPageLoadTimeout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setNormalPageLoadTimeout</span><wbr><span class="parameters">(org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">From now on, page load timeout will be set to the "normale" value defined in the "pageLoadSeconds" confg property</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>webDriver</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPageloadTimeoutSeconds(org.openqa.selenium.WebDriver,long)">
<h3>setPageloadTimeoutSeconds</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPageloadTimeoutSeconds</span><wbr><span class="parameters">(org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeoutSeconds)</span></div>
<div class="block">Set the pageload timeout for the following requests</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>webDriver</code> - </dd>
<dd><code>timeoutSeconds</code> - timeout in seconds</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findOrNull(org.openqa.selenium.SearchContext,org.openqa.selenium.By)">
<h3>findOrNull</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.openqa.selenium.WebElement</span>&nbsp;<span class="element-name">findOrNull</span><wbr><span class="parameters">(org.openqa.selenium.SearchContext&nbsp;from,
 org.openqa.selenium.By&nbsp;by)</span></div>
<div class="block">Return the first element matched by the selector, or null if not found</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>from</code> - a WebElement to start the search from, or the WebDriver to search in all the page</dd>
<dd><code>by</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTextIfExists(org.openqa.selenium.SearchContext,org.openqa.selenium.By)">
<h3>getTextIfExists</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTextIfExists</span><wbr><span class="parameters">(org.openqa.selenium.SearchContext&nbsp;from,
 org.openqa.selenium.By&nbsp;by)</span></div>
<div class="block">Search for text</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>from</code> - a WebElement to start the search from, or the WebDriver to search in all the page</dd>
<dd><code>by</code> - </dd>
<dt>Returns:</dt>
<dd>the text found, or ""</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="foundByText(java.util.List,java.lang.String)">
<h3>foundByText</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">foundByText</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;org.openqa.selenium.WebElement&gt;&nbsp;webElements,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</span></div>
<div class="block">Returns true if an element contains the given text (literally)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>webElements</code> - to search text int</dd>
<dd><code>text</code> - to search</dd>
<dt>Returns:</dt>
<dd>true if the text is found</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findById(java.lang.String,org.openqa.selenium.WebDriver)">
<h3>findById</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.openqa.selenium.WebElement</span>&nbsp;<span class="element-name">findById</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;id,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">Get an element by id</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>id</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="foundById(java.lang.String,org.openqa.selenium.WebDriver)">
<h3>foundById</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">foundById</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;id,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">Check if an element with a given id exists</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>id</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="foundByClass(java.lang.String,org.openqa.selenium.WebDriver)">
<h3>foundByClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">foundByClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;className,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">Check if at least one element with the given class exists</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>className</code> - without initial dot</dd>
<dd><code>webDriver</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeWebDriver(java.io.File,java.net.InetSocketAddress,java.lang.String,java.lang.String,java.util.Set,int)">
<h3>makeWebDriver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.openqa.selenium.WebDriver</span>&nbsp;<span class="element-name">makeWebDriver</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;customProfileDir,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/net/InetSocketAddress.html" title="class or interface in java.net" class="external-link">InetSocketAddress</a>&nbsp;proxyToUse,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;proxyUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;proxyPassword,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;org.openqa.selenium.Cookie&gt;&nbsp;cookiesToSet,
 int&nbsp;driverType)</span></div>
<div class="block">Create a new browser instance positioning the window</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>customProfileDir</code> - the folder where to store the user profile, can be null to use the default temporary profile. The folder is created when missing.</dd>
<dd><code>proxyToUse</code> - the address of the proxy, or null for direct connection</dd>
<dd><code>cookiesToSet</code> - cookies to set after the first get of a document. Can be null or empty. Cookies are set only when a 
 cookie with the same name has not been received. It's not possible to set cookies BEFORE the first get (by design of WebDriver).</dd>
<dd><code>driverType</code> - DRIVER_FIREFOX, DRIVER_CHROME</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeWebDriver(java.io.File,java.net.InetSocketAddress,java.lang.String,java.lang.String,java.util.Set,int,java.lang.String)">
<h3>makeWebDriver</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">org.openqa.selenium.WebDriver</span>&nbsp;<span class="element-name">makeWebDriver</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;customProfileDir,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/net/InetSocketAddress.html" title="class or interface in java.net" class="external-link">InetSocketAddress</a>&nbsp;proxyToUse,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;proxyUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;proxyPassword,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;org.openqa.selenium.Cookie&gt;&nbsp;cookiesToSet,
 int&nbsp;driverType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userAgent)</span></div>
<div class="block">Create a new browser instance positioning the window</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>customProfileDir</code> - the folder where to store the user profile, can be null to use the default temporary profile. The folder is created when missing.</dd>
<dd><code>proxyToUse</code> - the address of the proxy, or null for direct connection</dd>
<dd><code>proxyUser</code> - can be set for SOCKS5 proxies only</dd>
<dd><code>proxyPassword</code> - can be set for SOCKS5 proxies only</dd>
<dd><code>cookiesToSet</code> - cookies to set after the first get of a document. Can be null or empty. Cookies are set only when a 
 cookie with the same name has not been received. It's not possible to set cookies BEFORE the first get (by design of WebDriver).</dd>
<dd><code>driverType</code> - DRIVER_FIREFOX, DRIVER_CHROME</dd>
<dd><code>userAgent</code> - the user agent string, null for keeping the current browser's default. Not implemented for Firefox.</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="positionWindow(int,int,int,int,org.openqa.selenium.WebDriver)">
<h3>positionWindow</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">positionWindow</span><wbr><span class="parameters">(int&nbsp;posx,
 int&nbsp;posy,
 int&nbsp;width,
 int&nbsp;height,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
</section>
</li>
<li>
<section class="detail" id="clickByJavascript(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver)">
<h3>clickByJavascript</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clickByJavascript</span><wbr><span class="parameters">(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">Click on the given element using javascript. This is useful when any other form of clicking fails with the error 
 "Element is not clickable". Can't be used on HTML elements that don't have the "click()" method</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>webElement</code> - </dd>
<dd><code>webDriver</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clickByJavascript(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver,int,int,int,int)">
<h3>clickByJavascript</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clickByJavascript</span><wbr><span class="parameters">(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 int&nbsp;minPercentX,
 int&nbsp;maxPercentX,
 int&nbsp;minPercentY,
 int&nbsp;maxPercentY)</span></div>
<div class="block">Click on the given element using javascript. This is useful when any other form of clicking fails with the error 
 "Element is not clickable". Can't be used on HTML elements that don't have the "click()" method</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>webElement</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dd><code>minPercentX</code> - e.g. 10</dd>
<dd><code>maxPercentX</code> - e.g. 90</dd>
<dd><code>minPercentY</code> - </dd>
<dd><code>maxPercentY</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="randomClick(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver)">
<h3>randomClick</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">randomClick</span><wbr><span class="parameters">(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">Click on the given element in a range between 20% and 80% of the dimensions</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>webElement</code> - </dd>
<dd><code>webDriver</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="randomClick(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver,int,int,int,int)">
<h3>randomClick</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">randomClick</span><wbr><span class="parameters">(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 int&nbsp;minPercentX,
 int&nbsp;maxPercentX,
 int&nbsp;minPercentY,
 int&nbsp;maxPercentY)</span></div>
<div class="block">Click on the given element in a range between min and max % of the dimensions.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>webElement</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dd><code>minPercentX</code> - e.g. 10</dd>
<dd><code>maxPercentX</code> - e.g. 90</dd>
<dd><code>minPercentY</code> - </dd>
<dd><code>maxPercentY</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilAttributeNotEmpty(org.openqa.selenium.WebElement,java.lang.String,org.openqa.selenium.WebDriver,long)">
<h3>waitUntilAttributeNotEmpty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitUntilAttributeNotEmpty</span><wbr><span class="parameters">(org.openqa.selenium.WebElement&nbsp;element,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;attribute,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</span></div>
<div class="block">Waits until the attribute contains some non-empty text</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>element</code> - the element to check</dd>
<dd><code>attribute</code> - the attribute of the element that must not be empty</dd>
<dd><code>webDriver</code> - </dd>
<dd><code>timeOutInSeconds</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitWhileEmptyText(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver,long)">
<h3>waitWhileEmptyText</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitWhileEmptyText</span><wbr><span class="parameters">(org.openqa.selenium.WebElement&nbsp;element,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</span></div>
<div class="block">Waits until the selected element contains some non-empty text (warning: this method may not work as expected)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>element</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dd><code>timeOutInSeconds</code> - </dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#waitUntilAttributeNotEmpty(org.openqa.selenium.WebElement,java.lang.String,org.openqa.selenium.WebDriver,long)"><code>waitUntilAttributeNotEmpty(org.openqa.selenium.WebElement, java.lang.String, org.openqa.selenium.WebDriver, long)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitWhileEmptyText(java.lang.String,org.openqa.selenium.WebDriver,long)">
<h3>waitWhileEmptyText</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitWhileEmptyText</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</span></div>
<div class="block">Waits until the selected element contains some non-empty text</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cssSelector</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dd><code>timeOutInSeconds</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilPresent(java.lang.String,org.openqa.selenium.WebDriver,long)">
<h3>waitUntilPresent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitUntilPresent</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</span></div>
<div class="block">Wait until the selector matches an element</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cssSelector</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dd><code>timeOutInSeconds</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitUntilVisible(java.lang.String,org.openqa.selenium.WebDriver,long)">
<h3>waitUntilVisible</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitUntilVisible</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</span></div>
<div class="block">Wait until the selector matches a visible element</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cssSelector</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dd><code>timeOutInSeconds</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitWhilePresent(java.lang.String,org.openqa.selenium.WebDriver,long)">
<h3>waitWhilePresent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitWhilePresent</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</span></div>
<div class="block">Wait until the selector matches zero elements</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cssSelector</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dd><code>timeOutInSeconds</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitWhileVisible(java.lang.String,org.openqa.selenium.WebDriver,long)">
<h3>waitWhileVisible</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitWhileVisible</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</span></div>
</section>
</li>
<li>
<section class="detail" id="waitWhileVisible(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver,long)">
<h3>waitWhileVisible</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitWhileVisible</span><wbr><span class="parameters">(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</span></div>
</section>
</li>
<li>
<section class="detail" id="waitUntilLost(org.openqa.selenium.WebElement,org.openqa.selenium.WebDriver,long)">
<h3>waitUntilLost</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitUntilLost</span><wbr><span class="parameters">(org.openqa.selenium.WebElement&nbsp;webElement,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</span></div>
<div class="block">Waits until the element is no more attached to the DOM (stale).
 It happens when a new page is loaded, for example.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>webElement</code> - </dd>
<dd><code>webDriver</code> - </dd>
<dd><code>timeOutInSeconds</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="waitWhileVisible(java.util.List,org.openqa.selenium.WebDriver,long)">
<h3>waitWhileVisible</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">waitWhileVisible</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;org.openqa.selenium.WebElement&gt;&nbsp;webElements,
 org.openqa.selenium.WebDriver&nbsp;webDriver,
 long&nbsp;timeOutInSeconds)</span></div>
</section>
</li>
<li>
<section class="detail" id="relativeToAbsolute(java.lang.String,org.openqa.selenium.WebDriver)">
<h3>relativeToAbsolute</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">relativeToAbsolute</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeAddress,
 org.openqa.selenium.WebDriver&nbsp;webDriver)</span></div>
<div class="block">Given a relative address, computes the new full address.
 If the relative address starts with / then it is considered relative to the server, not to the servlet context
 Do not use it to convert href attributes because this is done by WebDriver automatically.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>relativeAddress</code> - like ccc.go or /xxx/yyy.go</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="takeScreenshot(org.openqa.selenium.WebDriver,java.nio.file.Path)">
<h3>takeScreenshot</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">takeScreenshot</span><wbr><span class="parameters">(org.openqa.selenium.WebDriver&nbsp;webDriver,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;toPath)</span></div>
<div class="block">Take a browser screenshot and move it to the specified path</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>webDriver</code> - </dd>
<dd><code>toPath</code> - destination for the screenshot</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
