package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.Configurator;
import com.artemide.common.repository.ConfiguratorRepoDao;
import com.artemide.common.repository.ConfiguratorRepository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies ConfiguratorRepoDao behaves exactly like ConfiguratorRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class ConfiguratorRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private ConfiguratorRepository configuratorRepository;

    @Autowired
    private ConfiguratorRepoDao configuratorRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    Configurator entity = entityManager.find(Configurator.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            entityManager.createQuery("DELETE FROM Configurator c WHERE c.name LIKE 'TEST_%' OR c.name LIKE 'INTEGRATION_%'")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private Configurator createTestEntity(String name, boolean enabled) {
        Configurator entity = new Configurator();
        entity.setName(name);
        entity.setEnabled(enabled);
        // Note: famiglia is required but we'll use existing data or create minimal test data
        return entity;
    }

    private void trackEntity(Configurator entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = configuratorRepository.count();
            long initialDaoCount = configuratorRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            Configurator testEntity = createTestEntity("INTEGRATION_SAVE_001", true);

            Configurator repoSaved = configuratorRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            Configurator testEntity2 = createTestEntity("INTEGRATION_SAVE_002", false);
            Configurator daoSaved = configuratorRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }
            if (!testEntity.getName().equals(repoSaved.getName())) {
                return "FAIL: Repository should preserve name";
            }
            if (!testEntity2.getName().equals(daoSaved.getName())) {
                return "FAIL: DAO should preserve name";
            }

            Optional<Configurator> repoFound = configuratorRepository.findById(repoSaved.getId());
            Optional<Configurator> daoFoundOptional = configuratorRepoDao.findById(daoSaved.getId());
            Configurator daoFoundDirect = configuratorRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }

            if (!repoSaved.getId().equals(repoFound.get().getId())) {
                return "FAIL: Repository should find correct entity";
            }
            if (!daoSaved.getId().equals(daoFoundOptional.get().getId())) {
                return "FAIL: DAO findById should find correct entity";
            }
            if (!daoSaved.getId().equals(daoFoundDirect.getId())) {
                return "FAIL: DAO findOne should find correct entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<Configurator> repoResults = configuratorRepository.findAll();
            List<Configurator> daoResults = configuratorRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            Configurator entityForRepo = createTestEntity("INTEGRATION_DELETE_001", true);
            Configurator entityForDao = createTestEntity("INTEGRATION_DELETE_002", false);
            
            Configurator savedForRepo = configuratorRepository.save(entityForRepo);
            Configurator savedForDao = configuratorRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!configuratorRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!configuratorRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            configuratorRepository.delete(savedForRepo);
            configuratorRepoDao.delete(savedForDao);
            entityManager.flush();

            if (configuratorRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (configuratorRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindFirstByEnabledTrueOrderById() {
        setUp();
        try {
            Configurator repoResult = configuratorRepository.findFirstByEnabledTrueOrderById();
            Configurator daoResult = configuratorRepoDao.findFirstByEnabledTrueOrderById();

            if (repoResult == null && daoResult == null) {
                return "PASS: findFirstByEnabledTrueOrderById() test successful - both return null";
            }
            if (repoResult == null || daoResult == null) {
                return "FAIL: Repository and DAO should return same result - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            if (!repoResult.getId().equals(daoResult.getId())) {
                return "FAIL: Repository and DAO should return same entity - Repository ID: " + repoResult.getId() + ", DAO ID: " + daoResult.getId();
            }
            
            return "PASS: findFirstByEnabledTrueOrderById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testCountByEnabled() {
        setUp();
        try {
            int repoCountEnabled = configuratorRepository.countByEnabled(true);
            int daoCountEnabled = configuratorRepoDao.countByEnabled(true);
            
            int repoCountDisabled = configuratorRepository.countByEnabled(false);
            int daoCountDisabled = configuratorRepoDao.countByEnabled(false);

            if (repoCountEnabled != daoCountEnabled) {
                return "FAIL: Enabled counts don't match - Repository: " + repoCountEnabled + ", DAO: " + daoCountEnabled;
            }
            if (repoCountDisabled != daoCountDisabled) {
                return "FAIL: Disabled counts don't match - Repository: " + repoCountDisabled + ", DAO: " + daoCountDisabled;
            }
            
            return "PASS: countByEnabled() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindFirstByFamigliaId() {
        setUp();
        try {
            // Get a real Famiglia ID from the database
            Optional<Long> famigliaIdOpt = DbUtil.findFirstId(entityManager, com.yr.entity.Famiglia.class);
            if (!famigliaIdOpt.isPresent()) {
                return "SKIP: No Famiglia records found in database";
            }
            Long testFamigliaId = famigliaIdOpt.get();
            
            Configurator repoResult = configuratorRepository.findFirstByFamigliaId(testFamigliaId);
            Configurator daoResult = configuratorRepoDao.findFirstByFamigliaId(testFamigliaId);

            if (repoResult == null && daoResult == null) {
                return "PASS: findFirstByFamigliaId() test successful - both return null for famiglia ID " + testFamigliaId;
            }
            if (repoResult == null || daoResult == null) {
                return "FAIL: Repository and DAO should return same result - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            if (!repoResult.getId().equals(daoResult.getId())) {
                return "FAIL: Repository and DAO should return same entity - Repository ID: " + repoResult.getId() + ", DAO ID: " + daoResult.getId();
            }
            
            return "PASS: findFirstByFamigliaId() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== ConfiguratorRepository vs ConfiguratorRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String findFirstEnabledResult = testFindFirstByEnabledTrueOrderById();
            report.append("5. FindFirstByEnabledTrueOrderById Test: ").append(findFirstEnabledResult).append("\n");
            
            String countByEnabledResult = testCountByEnabled();
            report.append("6. CountByEnabled Test: ").append(countByEnabledResult).append("\n");
            
            String findFirstByFamigliaResult = testFindFirstByFamigliaId();
            report.append("7. FindFirstByFamigliaId Test: ").append(findFirstByFamigliaResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - ConfiguratorRepoDao behaves exactly like ConfiguratorRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
