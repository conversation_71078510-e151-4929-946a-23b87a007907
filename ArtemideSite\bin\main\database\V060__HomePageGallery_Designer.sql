#Aggiunta nuove tabelle da usare nelle slider della HomePage e aggiunta campo Designer in HomePageGallery 

create table HomeLongBannerEventDate (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeLongBannerEventLocation (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
alter table HomePageGallery ADD COLUMN designer_id bigint after pos;

alter table HomeLongBannerEventDate add constraint FKpfyqvd41a4iml3ttw973wug5p foreign key (HomePage_id) references HomePage (id);
alter table HomeLongBannerEventLocation add constraint FKfdjeawer1gj06vf7w3hhic7ww foreign key (HomePage_id) references HomePage (id);
alter table HomePageGallery add constraint FK5j30de4mfyrvu41k5199aev4h foreign key (designer_id) references Designer (id);