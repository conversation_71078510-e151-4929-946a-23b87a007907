package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.Famiglia;

@Repository
@Transactional(readOnly = true)
public class FamigliaRepoDao {

    @PersistenceContext EntityManager em;

    /**
     * Ritorna true se la famiglia ha almeno una sottofamiglia magnetica (NON USATO)
     * @param famigliaId
     * @return
     */
    public int hasMagneticSubfamily(long famigliaId) {
        String sql = "select case when exists (" +
            "select 1 from Famiglia f join Subfamily s on s.famiglia_id = f.id where f.id=:famigliaId and s.magnetic = true limit 1" +
            ") then 1 else 0 end";
        return ((Number) em.createNativeQuery(sql)
            .setParameter("famigliaId", famigliaId)
            .getSingleResult()).intValue();
    }

    /**
     * Trova tutte le tipologie assegnate ai prodotti di questa famiglia (NON alle sottofamiglie)
     * @param famigliaId id della famiglia
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Integer> findProductTipologies(long famigliaId) {
        String sql = "select distinct pmt.mTipologia from Prodotto_mTipologia pmt join Prodotto p ON p.id = pmt.Prodotto_id join Subfamily s on s.id = p.subfamily_id join Famiglia f on f.id = s.famiglia_id where f.id = :famigliaId";
        return em.createNativeQuery(sql)
            .setParameter("famigliaId", famigliaId)
            .getResultList();
    }

    /**
     * Trova l'id della famiglia dato l'id della sottofamiglia
     * @param subfamilyId
     * @return
     */
    public Long findBySubfamilyId(Long subfamilyId) {
        String sql = "select f.id from Subfamily s left join s.famiglia f where s.id = :subfamilyId";
        List<Long> resultList = em.createQuery(sql, Long.class)
            .setParameter("subfamilyId", subfamilyId)
            .setMaxResults(1)
            .getResultList();
        return resultList.isEmpty() ? null : resultList.get(0);
    }

    /**
     * Prende il nome localizzato
     * @param id
     * @param locale
     * @return
     */
    public String findName(Long id, String locale) {
        String sql = "select n from Famiglia f left join f.name n where f.id = :id and CAST(KEY(n) AS text) = :locale";
        List<String> resultList = em.createQuery(sql, String.class)
            .setParameter("id", id)
            .setParameter("locale", locale)
            .setMaxResults(1)
            .getResultList();
        return resultList.isEmpty() ? null : resultList.get(0);
    }

    /**
     * Get a page
     * @param offset where to start
     * @param size the size of the page
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<Famiglia> findPage(long offset, int size) {
        String sql = "select * from Famiglia limit :offset,:size";
        return em.createNativeQuery(sql, Famiglia.class)
            .setParameter("offset", offset)
            .setParameter("size", size)
            .getResultList();
    }

    public boolean isFamigliaPublished(Long id) {
        String sql = "select f.published from Famiglia f where f.id = :id";
        return em.createQuery(sql, Boolean.class)
            .setParameter("id", id)
            .getSingleResult();
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Famiglia", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Famiglia save(Famiglia entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Famiglia> findById(Long entityId) {
        Famiglia result = em.find(Famiglia.class, entityId);
        return Optional.ofNullable(result);
    }

    public Famiglia findOne(Long entityId) {
        return em.find(Famiglia.class, entityId);
    }

    public List<Famiglia> findAll() {
        return em.createQuery("from Famiglia", Famiglia.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Famiglia> batchToSave) {
        for (Famiglia entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Famiglia entity) {
        em.remove(entity);
    }
}
