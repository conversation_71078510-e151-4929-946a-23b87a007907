<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaDTColumnsProxy (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.datatables.proxy, class: YadaDTColumnsProxy">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.datatables.proxy</a></div>
<h1 title="Class YadaDTColumnsProxy" class="title">Class YadaDTColumnsProxy</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">net.yadaframework.core.YadaFluentBase</a>&lt;<a href="../options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>&gt;
<div class="inheritance"><a href="../options/YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">net.yadaframework.web.datatables.options.YadaDTColumns</a>
<div class="inheritance">net.yadaframework.web.datatables.proxy.YadaDTColumnsProxy</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaDTColumnsProxy</span>
<span class="extends-implements">extends <a href="../options/YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span></div>
<div class="block">This class implements the methods needed for <b>internal use</b> 
 so that they don't pollute the fluent interface.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.web.datatables.options.YadaDTColumns">Fields inherited from class&nbsp;net.yadaframework.web.datatables.options.<a href="../options/YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></h3>
<code><a href="../options/YadaDTColumns.html#ariaTitle">ariaTitle</a>, <a href="../options/YadaDTColumns.html#cellType">cellType</a>, <a href="../options/YadaDTColumns.html#className">className</a>, <a href="../options/YadaDTColumns.html#contentPadding">contentPadding</a>, <a href="../options/YadaDTColumns.html#createdCell">createdCell</a>, <a href="../options/YadaDTColumns.html#data">data</a>, <a href="../options/YadaDTColumns.html#dataFunction">dataFunction</a>, <a href="../options/YadaDTColumns.html#defaultContent">defaultContent</a>, <a href="../options/YadaDTColumns.html#footer">footer</a>, <a href="../options/YadaDTColumns.html#name">name</a>, <a href="../options/YadaDTColumns.html#orderable">orderable</a>, <a href="../options/YadaDTColumns.html#orderData">orderData</a>, <a href="../options/YadaDTColumns.html#orderDataType">orderDataType</a>, <a href="../options/YadaDTColumns.html#orderSequence">orderSequence</a>, <a href="../options/YadaDTColumns.html#render">render</a>, <a href="../options/YadaDTColumns.html#responsivePriority">responsivePriority</a>, <a href="../options/YadaDTColumns.html#searchable">searchable</a>, <a href="../options/YadaDTColumns.html#title">title</a>, <a href="../options/YadaDTColumns.html#visible">visible</a>, <a href="../options/YadaDTColumns.html#width">width</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.core.YadaFluentBase">Fields inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#parent">parent</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(net.yadaframework.web.datatables.options.YadaDTOptions)" class="member-name-link">YadaDTColumnsProxy</a><wbr>(<a href="../options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>&nbsp;parent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAriaTitle()" class="member-name-link">getAriaTitle</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCellType()" class="member-name-link">getCellType</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClassName()" class="member-name-link">getClassName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getContentPadding()" class="member-name-link">getContentPadding</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCreatedCell()" class="member-name-link">getCreatedCell</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getData()" class="member-name-link">getData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultContent()" class="member-name-link">getDefaultContent</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFooter()" class="member-name-link">getFooter</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getName()" class="member-name-link">getName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOrderData()" class="member-name-link">getOrderData</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOrderDataType()" class="member-name-link">getOrderDataType</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOrderSequence()" class="member-name-link">getOrderSequence</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getParent()" class="member-name-link">getParent</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRender()" class="member-name-link">getRender</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResponsivePriority()" class="member-name-link">getResponsivePriority</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTitle()" class="member-name-link">getTitle</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getWidth()" class="member-name-link">getWidth</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isOrderable()" class="member-name-link">isOrderable</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSearchable()" class="member-name-link">isSearchable</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isVisible()" class="member-name-link">isVisible</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-net.yadaframework.web.datatables.options.YadaDTColumns">Methods inherited from class&nbsp;net.yadaframework.web.datatables.options.<a href="../options/YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></h3>
<code><a href="../options/YadaDTColumns.html#dtAriaTitle(java.lang.String)">dtAriaTitle</a>, <a href="../options/YadaDTColumns.html#dtCellType(java.lang.String)">dtCellType</a>, <a href="../options/YadaDTColumns.html#dtClassName(java.lang.String)">dtClassName</a>, <a href="../options/YadaDTColumns.html#dtContentPadding(java.lang.String)">dtContentPadding</a>, <a href="../options/YadaDTColumns.html#dtCreatedCell(java.lang.String)">dtCreatedCell</a>, <a href="../options/YadaDTColumns.html#dtData(java.lang.Integer)">dtData</a>, <a href="../options/YadaDTColumns.html#dtData(java.lang.String)">dtData</a>, <a href="../options/YadaDTColumns.html#dtDataFunction(java.lang.String)">dtDataFunction</a>, <a href="../options/YadaDTColumns.html#dtDataNull()">dtDataNull</a>, <a href="../options/YadaDTColumns.html#dtDefaultContent(java.lang.String)">dtDefaultContent</a>, <a href="../options/YadaDTColumns.html#dtFooter(java.lang.String)">dtFooter</a>, <a href="../options/YadaDTColumns.html#dtName(java.lang.String)">dtName</a>, <a href="../options/YadaDTColumns.html#dtOrderableOff()">dtOrderableOff</a>, <a href="../options/YadaDTColumns.html#dtOrderData(java.lang.Integer)">dtOrderData</a>, <a href="../options/YadaDTColumns.html#dtOrderDataType(java.lang.String)">dtOrderDataType</a>, <a href="../options/YadaDTColumns.html#dtOrderSequence(java.lang.String)">dtOrderSequence</a>, <a href="../options/YadaDTColumns.html#dtRender(java.lang.String)">dtRender</a>, <a href="../options/YadaDTColumns.html#dtResponsivePriority(int)">dtResponsivePriority</a>, <a href="../options/YadaDTColumns.html#dtSearchableOff()">dtSearchableOff</a>, <a href="../options/YadaDTColumns.html#dtTitle(java.lang.String)">dtTitle</a>, <a href="../options/YadaDTColumns.html#dtVisibleOff()">dtVisibleOff</a>, <a href="../options/YadaDTColumns.html#dtWidth(java.lang.String)">dtWidth</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-net.yadaframework.core.YadaFluentBase">Methods inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#back()">back</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(net.yadaframework.web.datatables.options.YadaDTOptions)">
<h3>YadaDTColumnsProxy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaDTColumnsProxy</span><wbr><span class="parameters">(<a href="../options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>&nbsp;parent)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getParent()">
<h3>getParent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">getParent</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getData()">
<h3>getData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getData</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The data source for the column.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.data">DataTables Reference: columns.data</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAriaTitle()">
<h3>getAriaTitle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getAriaTitle</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The ARIA label for the column for accessibility purposes.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.ariaTitle">DataTables Reference: columns.ariaTitle</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCellType()">
<h3>getCellType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCellType</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The cell type for the column (e.g., "td" or "th").</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.cellType">DataTables Reference: columns.cellType</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClassName()">
<h3>getClassName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getClassName</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The class name to be added to all cells in the column.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.className">DataTables Reference: columns.className</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getContentPadding()">
<h3>getContentPadding</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getContentPadding</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The padding to be applied to the content of the column.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.contentPadding">DataTables Reference: columns.contentPadding</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCreatedCell()">
<h3>getCreatedCell</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCreatedCell</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The function to be executed when a cell is created.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.createdCell">DataTables Reference: columns.createdCell</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultContent()">
<h3>getDefaultContent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultContent</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The default content for the column if the data source is null.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.defaultContent">DataTables Reference: columns.defaultContent</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFooter()">
<h3>getFooter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFooter</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The content for the footer of the column.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.footer">DataTables Reference: columns.footer</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getName()">
<h3>getName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getName</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The name for the column.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.name">DataTables Reference: columns.name</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOrderData()">
<h3>getOrderData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</span>&nbsp;<span class="element-name">getOrderData</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The columns to order by when this column is sorted.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.orderData">DataTables Reference: columns.orderData</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOrderDataType()">
<h3>getOrderDataType</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getOrderDataType</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The ordering data type for the column.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.orderDataType">DataTables Reference: columns.orderDataType</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOrderSequence()">
<h3>getOrderSequence</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getOrderSequence</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The ordering sequence for the column.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.orderSequence">DataTables Reference: columns.orderSequence</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWidth()">
<h3>getWidth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getWidth</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The width of the column.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.width">DataTables Reference: columns.width</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getTitle()">
<h3>getTitle</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getTitle</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The title for the column.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.title">DataTables Reference: columns.title</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRender()">
<h3>getRender</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRender</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The rendering function for the column's data.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.render">DataTables Reference: columns.render</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResponsivePriority()">
<h3>getResponsivePriority</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getResponsivePriority</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The responsive priority for the column.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.responsivePriority">DataTables Reference: columns.responsivePriority</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isOrderable()">
<h3>isOrderable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">isOrderable</span>()</div>
</section>
</li>
<li>
<section class="detail" id="isSearchable()">
<h3>isSearchable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">isSearchable</span>()</div>
</section>
</li>
<li>
<section class="detail" id="isVisible()">
<h3>isVisible</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">isVisible</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
