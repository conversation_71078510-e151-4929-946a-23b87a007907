memberSearchIndex = [{"p":"net.yadaframework.security.web","c":"YadaSocialRegistrationData","l":"accessToken"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"addAttachment(YadaAttachedFile)","u":"addAttachment(net.yadaframework.persistence.entity.YadaAttachedFile)"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"addCropQueue(String, String)","u":"addCropQueue(java.lang.String,java.lang.String)"},{"p":"net.yadaframework.security","c":"YadaWebSecurityConfig","l":"addExtraDialect(SpringTemplateEngine)","u":"addExtraDialect(org.thymeleaf.spring6.SpringTemplateEngine)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"addLoginErrorParams(String)","u":"addLoginErrorParams(java.lang.String)"},{"p":"net.yadaframework.security","c":"YadaWrappedSavedRequest","l":"addOrUpdateUrlParameter(String, String)","u":"addOrUpdateUrlParameter(java.lang.String,java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"addRole(Integer)","u":"addRole(java.lang.Integer)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"addRoles(Integer[])","u":"addRoles(java.lang.Integer[])"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaTicketDao","l":"addTicket(YadaLocalEnum<?>, String, String, YadaUserProfile, int, MultipartFile)","u":"addTicket(net.yadaframework.core.YadaLocalEnum,java.lang.String,java.lang.String,net.yadaframework.security.persistence.entity.YadaUserProfile,int,org.springframework.web.multipart.MultipartFile)"},{"p":"net.yadaframework.security","c":"YadaWebSecurityConfig","l":"addYadaDialect(SpringTemplateEngine)","u":"addYadaDialect(org.thymeleaf.spring6.SpringTemplateEngine)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"addYadaSocialCredentials(YadaSocialCredentials)","u":"addYadaSocialCredentials(net.yadaframework.security.persistence.entity.YadaSocialCredentials)"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessHandler","l":"AJAX_LOGGEDIN_HEADER"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessHandler","l":"AJAX_LOGGEDIN_PARAM"},{"p":"net.yadaframework.security.web","c":"YadaLoginController","l":"ajaxLoginForm(Model)","u":"ajaxLoginForm(org.springframework.ui.Model)"},{"p":"net.yadaframework.security.web","c":"YadaLoginController","l":"ajaxLoginOk(Model)","u":"ajaxLoginOk(org.springframework.ui.Model)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketStatus","l":"ANSWERED"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"assigned"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"attachment"},{"p":"net.yadaframework.security.web","c":"YadaActionUploadAttrProcessor","l":"ATTR_NAME"},{"p":"net.yadaframework.security.web","c":"YadaActionUploadAttrProcessor","l":"ATTR_PRECEDENCE"},{"p":"net.yadaframework.security","c":"AuditFilter","l":"AuditFilter()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"authenticateAs(YadaUserCredentials)","u":"authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials)"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"authenticateAs(YadaUserCredentials, boolean)","u":"authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,boolean)"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"authenticateAs(YadaUserCredentials, boolean, HttpServletRequest, HttpServletResponse)","u":"authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,boolean,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"authenticateAs(YadaUserCredentials, HttpServletRequest, HttpServletResponse)","u":"authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"authorizationManagers(List<SecurityFilterChain>)","u":"authorizationManagers(java.util.List)"},{"p":"net.yadaframework.security.web","c":"YadaLoginController","l":"autologin(String, String, RedirectAttributes, HttpSession, HttpServletRequest, HttpServletResponse)","u":"autologin(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes,jakarta.servlet.http.HttpSession,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"avatar"},{"p":"net.yadaframework.security","c":"AuditFilter","l":"beforeRequest(HttpServletRequest)","u":"beforeRequest(jakarta.servlet.http.HttpServletRequest)"},{"p":"net.yadaframework.security","c":"SecurityWebApplicationInitializer","l":"beforeSpringSecurityFilterChain(ServletContext)","u":"beforeSpringSecurityFilterChain(jakarta.servlet.ServletContext)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketType","l":"BILLING"},{"p":"net.yadaframework.security.components","c":"YadaSecurityEmailService","l":"buildLink(String)","u":"buildLink(java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"caseAnonAuth(String, String)","u":"caseAnonAuth(java.lang.String,java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"changeCurrentRoles(Authentication, int[])","u":"changeCurrentRoles(org.springframework.security.core.Authentication,int[])"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"changePassword(String, PasswordEncoder)","u":"changePassword(java.lang.String,org.springframework.security.crypto.password.PasswordEncoder)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"changePassword(YadaUserCredentials, String)","u":"changePassword(net.yadaframework.security.persistence.entity.YadaUserCredentials,java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"changePassword(YadaUserProfile, String)","u":"changePassword(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"changePasswordIfAuthenticated(String, String, String)","u":"changePasswordIfAuthenticated(java.lang.String,java.lang.String,java.lang.String)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController","l":"changeUsername(String)","u":"changeUsername(java.lang.String)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"changeUsername(String, String)","u":"changeUsername(java.lang.String,java.lang.String)"},{"p":"net.yadaframework.security","c":"CheckSessionFilter","l":"CheckSessionFilter()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"checkUrlAccess(HttpServletRequest, String)","u":"checkUrlAccess(jakarta.servlet.http.HttpServletRequest,java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"clearAnySavedRequest()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"clearCaches()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"clearUserProfileCache()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketStatus","l":"CLOSED"},{"p":"net.yadaframework.security.web","c":"YadaLoginController","l":"closeModal()"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig.CustomAuthenticationEntryPoint","l":"commence(HttpServletRequest, HttpServletResponse, AuthenticationException)","u":"commence(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,org.springframework.security.core.AuthenticationException)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessageType","l":"COMMENT"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketType","l":"COMMERCIAL_SUPPORT"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"computeHash()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"config"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"configure(HttpSecurity)","u":"configure(org.springframework.security.config.annotation.web.builders.HttpSecurity)"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"configureGlobal(AuthenticationManagerBuilder)","u":"configureGlobal(org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"contentHash"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"copyLoginErrorParams(HttpServletRequest, Model)","u":"copyLoginErrorParams(jakarta.servlet.http.HttpServletRequest,org.springframework.ui.Model)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaTicketDao","l":"countAllYadaTicketOpenNative()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"create(Map<String, Object>, Class<T>)","u":"create(java.util.Map,java.lang.Class)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"create(String, String, Set<Integer>)","u":"create(java.lang.String,java.lang.String,java.util.Set)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"created"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController","l":"createNewUser(String, String, String[], Locale, Class<T>)","u":"createNewUser(java.lang.String,java.lang.String,java.lang.String[],java.util.Locale,java.lang.Class)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserMessageDao","l":"createOrIncrement(YadaUserMessage<?>)","u":"createOrIncrement(net.yadaframework.security.persistence.entity.YadaUserMessage)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"creationDate"},{"p":"net.yadaframework.security.web","c":"YadaMiscController","l":"cropCancel(YadaCropDefinition, Model, RedirectAttributes)","u":"cropCancel(net.yadaframework.security.web.YadaCropDefinition,org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)"},{"p":"net.yadaframework.security.web","c":"YadaMiscController","l":"cropPerform(YadaCropDefinition, Model, RedirectAttributes)","u":"cropPerform(net.yadaframework.security.web.YadaCropDefinition,org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"cropQueue"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig.CustomAuthenticationEntryPoint","l":"CustomAuthenticationEntryPoint()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"daoAuthenticationProvider()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"data"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"DEFAULT_LOGIN_POST"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"DEFAULT_LOGIN_URL"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"DEFAULT_LOGIN_URL_AJAX"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"deimpersonate(HttpServletRequest, HttpServletResponse)","u":"deimpersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaRegistrationRequestDao","l":"delete(YadaRegistrationRequest)","u":"delete(net.yadaframework.security.persistence.entity.YadaRegistrationRequest)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserMessageDao","l":"deleteBelongingTo(YadaUserProfile)","u":"deleteBelongingTo(net.yadaframework.security.persistence.entity.YadaUserProfile)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaSocialCredentialsDao","l":"deleteByYadaUserCredentialsAndType(YadaUserCredentials, int)","u":"deleteByYadaUserCredentialsAndType(net.yadaframework.security.persistence.entity.YadaUserCredentials,int)"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"deleteCropQueue()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaAutoLoginTokenDao","l":"deleteExpired()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"depersonate()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"depersonate(HttpServletRequest, HttpServletResponse)","u":"depersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"depersonify()"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessFilter","l":"destroy()"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessHandler","l":"determineTargetUrl(HttpServletRequest, HttpServletResponse)","u":"determineTargetUrl(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"},{"p":"net.yadaframework.security.components","c":"YadaLogoutSuccessHandler","l":"determineTargetUrl(HttpServletRequest, HttpServletResponse)","u":"determineTargetUrl(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"},{"p":"net.yadaframework.security","c":"CheckSessionFilter","l":"doFilter(ServletRequest, ServletResponse, FilterChain)","u":"doFilter(jakarta.servlet.ServletRequest,jakarta.servlet.ServletResponse,jakarta.servlet.FilterChain)"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessFilter","l":"doFilter(ServletRequest, ServletResponse, FilterChain)","u":"doFilter(jakarta.servlet.ServletRequest,jakarta.servlet.ServletResponse,jakarta.servlet.FilterChain)"},{"p":"net.yadaframework.security","c":"AuditFilter","l":"doFilterInternal(HttpServletRequest, HttpServletResponse, FilterChain)","u":"doFilterInternal(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,jakarta.servlet.FilterChain)"},{"p":"net.yadaframework.security.web","c":"YadaActionUploadAttrProcessor","l":"doProcess(ITemplateContext, IProcessableElementTag, AttributeName, String, IElementTagStructureHandler)","u":"doProcess(org.thymeleaf.context.ITemplateContext,org.thymeleaf.model.IProcessableElementTag,org.thymeleaf.engine.AttributeName,java.lang.String,org.thymeleaf.processor.element.IElementTagStructureHandler)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationOutcome","l":"email"},{"p":"net.yadaframework.security.web","c":"YadaSocialRegistrationData","l":"email"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"emailed"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"ensureRole(Integer)","u":"ensureRole(java.lang.Integer)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"ensureRoles(Integer[])","u":"ensureRoles(java.lang.Integer[])"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"equals(Object)","u":"equals(java.lang.Object)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"equals(Object)","u":"equals(java.lang.Object)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaChangeUsernameResult","l":"ERROR"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationStatus","l":"ERROR"},{"p":"net.yadaframework.security.components","c":"YadaSecurityEmailService","l":"extendAutologinLink(String, String)","u":"extendAutologinLink(java.lang.String,java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaTokenHandler","l":"extendAutologinLink(String, String)","u":"extendAutologinLink(java.lang.String,java.lang.String)"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"failureHandler"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketType","l":"FEEDBACK"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"find(long)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserMessageDao","l":"find(Long)","u":"find(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserMessageDao","l":"find(Long, YadaPageRequest, YadaPersistentEnum<?>...)","u":"find(java.lang.Long,net.yadaframework.web.YadaPageRequest,net.yadaframework.persistence.entity.YadaPersistentEnum...)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaRegistrationRequestDao","l":"findByEmailAndRegistrationType(String, YadaRegistrationType, Class<R>)","u":"findByEmailAndRegistrationType(java.lang.String,net.yadaframework.core.YadaRegistrationType,java.lang.Class)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"findById(Long)","u":"findById(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaAutoLoginTokenDao","l":"findByIdAndTokenOrderByTimestampDesc(long, long)","u":"findByIdAndTokenOrderByTimestampDesc(long,long)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaRegistrationRequestDao","l":"findByIdAndTokenOrderByTimestampDesc(long, long, Class<R>)","u":"findByIdAndTokenOrderByTimestampDesc(long,long,java.lang.Class)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"findByRoleKey(String)","u":"findByRoleKey(java.lang.String)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaSocialCredentialsDao","l":"findBySocialIdAndType(String, int)","u":"findBySocialIdAndType(java.lang.String,int)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaRegistrationRequestDao","l":"findByTimestampBefore(Date)","u":"findByTimestampBefore(java.util.Date)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"findByUserCredentials(YadaUserCredentials, YadaPageRequest)","u":"findByUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials,net.yadaframework.web.YadaPageRequest)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"findByUserCredentialsId(Long)","u":"findByUserCredentialsId(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"findByUserCredentialsUsername(String, YadaPageRequest)","u":"findByUserCredentialsUsername(java.lang.String,net.yadaframework.web.YadaPageRequest)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"findByUsername(String)","u":"findByUsername(java.lang.String)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"findByUsername(String, YadaPageRequest)","u":"findByUsername(java.lang.String,net.yadaframework.web.YadaPageRequest)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"findByUserProfileId(Long)","u":"findByUserProfileId(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaTicketMessageDao","l":"findByYadaTicketOrderByCreatedDesc(YadaTicket)","u":"findByYadaTicketOrderByCreatedDesc(net.yadaframework.security.persistence.entity.YadaTicket)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaAutoLoginTokenDao","l":"findByYadaUserCredentials(YadaUserCredentials)","u":"findByYadaUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaSocialCredentialsDao","l":"findByYadaUserCredentialsAndType(YadaUserCredentials, int)","u":"findByYadaUserCredentialsAndType(net.yadaframework.security.persistence.entity.YadaUserCredentials,int)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"findEnabledUsers()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"findEnabledUsersWithRole(Integer)","u":"findEnabledUsersWithRole(java.lang.Integer)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"findFirstByUsername(String)","u":"findFirstByUsername(java.lang.String)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaTicketMessageDao","l":"findMessagesAndAttachmentByYadaTicketOrderByModifiedDesc(YadaTicket)","u":"findMessagesAndAttachmentByYadaTicketOrderByModifiedDesc(net.yadaframework.security.persistence.entity.YadaTicket)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaTicketDao","l":"findOldAnsweredYadaTicketNative()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"findRoleIds(Long)","u":"findRoleIds(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"findUserProfileByUsername(String)","u":"findUserProfileByUsername(java.lang.String)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"findUserProfileIdByUsername(String)","u":"findUserProfileIdByUsername(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"firstName"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"generateClearPassword()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"generateClearPassword(int)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getAssigned()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getAssignedName()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getAttachment()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"getAvatar()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getConfirmPassword()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getContentHash()"},{"p":"net.yadaframework.security","c":"YadaWrappedSavedRequest","l":"getCookies()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getCreated()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getCreationDate()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getCreationDate()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"getCropQueue()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"getCurrentRoles()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"getCurrentUserProfile()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"getCurrentUserProfileId()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getData()"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessHandler","l":"getDefaultTargetUrlAjaxRequest()"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessHandler","l":"getDefaultTargetUrlNormalRequest()"},{"p":"net.yadaframework.security.web","c":"YadaCropDefinition","l":"getDesktopCrop()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getDT_RowClass()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getEmail()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"getEmail()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"getExpiration()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getFailedAttempts()"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"getFailureUrlAjaxRequest()"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"getFailureUrlNormalRequest()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"getFirstName()"},{"p":"net.yadaframework.security","c":"YadaWrappedSavedRequest","l":"getHeaderNames()"},{"p":"net.yadaframework.security","c":"YadaWrappedSavedRequest","l":"getHeaderValues(String)","u":"getHeaderValues(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"getId()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getId()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"getId()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getId()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getId()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getId()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"getId()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getLastDate()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserMessageDao","l":"getLastDate(YadaUserMessage)","u":"getLastDate(net.yadaframework.security.persistence.entity.YadaUserMessage)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getLastFailedAttempt()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"getLastName()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getLastSuccessfulLogin()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaCommentMessage","l":"getLikers()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"getLocale()"},{"p":"net.yadaframework.security","c":"YadaWrappedSavedRequest","l":"getLocales()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"getLoggedInUserRoleKeys()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"getLoggedInUserRoles()"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"getLoginErrorParams(HttpServletRequest)","u":"getLoginErrorParams(jakarta.servlet.http.HttpServletRequest)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getMessage()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getMessages()"},{"p":"net.yadaframework.security","c":"YadaWrappedSavedRequest","l":"getMethod()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"getMiddleName()"},{"p":"net.yadaframework.security.web","c":"YadaCropDefinition","l":"getMobileCrop()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getModified()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getNewPassword()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getOwner()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getOwnerName()"},{"p":"net.yadaframework.security","c":"YadaWrappedSavedRequest","l":"getParameterMap()"},{"p":"net.yadaframework.security","c":"YadaWrappedSavedRequest","l":"getParameterValues(String)","u":"getParameterValues(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getPassword()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getPassword()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getPasswordDate()"},{"p":"net.yadaframework.security.web","c":"YadaCropDefinition","l":"getPdfCrop()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getPriority()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getPriority()"},{"p":"net.yadaframework.security.web","c":"YadaDialectWithSecurity","l":"getProcessors(String)","u":"getProcessors(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getReceiverName()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getRecipient()"},{"p":"net.yadaframework.security","c":"YadaWrappedSavedRequest","l":"getRedirectUrl()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getRegistrationType()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getRoles()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"getSavedRequestUrl()"},{"p":"net.yadaframework.security","c":"SecurityWebApplicationInitializer","l":"getSecurityDispatcherTypes()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getSender()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getSenderName()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"getSocialId()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getStackSize()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getStatus()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getStatus()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"getTimestamp()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getTimestamp()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getTimestampAsRelative(Locale)","u":"getTimestampAsRelative(java.util.Locale)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getTimezone()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"getTimezone()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getTitle()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getTitle()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"getToken()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getToken()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaCommentMessage","l":"getTotLikes()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaCommentMessage","l":"getTotReplies()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getTrattamentoDati()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"getType()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getType()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getType()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"getUserCredentials()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"getUsername()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getUsername()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"getVersion()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getVersion()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"getVersion()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"getVersion()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getVersion()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"getVersion()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"getVersion()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"getYadaSocialCredentialsList()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketMessage","l":"getYadaTicket()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"getYadaUserCredentials()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"getYadaUserCredentials()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"getYadaUserCredentials()"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController","l":"handleRegistrationConfirmation(String, String[], Locale, HttpSession, Class<T>, Class<R>)","u":"handleRegistrationConfirmation(java.lang.String,java.lang.String[],java.util.Locale,jakarta.servlet.http.HttpSession,java.lang.Class,java.lang.Class)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController","l":"handleRegistrationRequest(YadaRegistrationRequest, BindingResult, Model, HttpServletRequest, Locale)","u":"handleRegistrationRequest(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,org.springframework.validation.BindingResult,org.springframework.ui.Model,jakarta.servlet.http.HttpServletRequest,java.util.Locale)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"hasAnyRole(String...)","u":"hasAnyRole(java.lang.String...)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"hasAnyRoleId(Integer...)","u":"hasAnyRoleId(java.lang.Integer...)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"hasAnyRoleKey(String...)","u":"hasAnyRoleKey(java.lang.String...)"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"hasCropQueue()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"hasCurrentRole(String)","u":"hasCurrentRole(java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"hasCurrentRole(String[])","u":"hasCurrentRole(java.lang.String[])"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"hashCode()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"hashCode()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"hasRole(Integer)","u":"hasRole(java.lang.Integer)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserMessageDao","l":"hasUnreadMessage(YadaUserProfile)","u":"hasUnreadMessage(net.yadaframework.security.persistence.entity.YadaUserProfile)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"id"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"id"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"id"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"impersonate(Long)","u":"impersonate(java.lang.Long)"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"impersonate(Long, String, HttpServletRequest, HttpServletResponse)","u":"impersonate(java.lang.Long,java.lang.String,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"impersonatedUserId"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"impersonationStartingLocation"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"impersonatorUserId"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"impersonify(Long)","u":"impersonify(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"incrementFailedAttempts(String)","u":"incrementFailedAttempts(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"incrementStack()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"init()"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController","l":"init()"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessFilter","l":"init(FilterConfig)","u":"init(jakarta.servlet.FilterConfig)"},{"p":"net.yadaframework.security.exceptions","c":"InternalAuthenticationException","l":"InternalAuthenticationException(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"net.yadaframework.security.exceptions","c":"InternalAuthenticationException","l":"InternalAuthenticationException(String, Throwable)","u":"%3Cinit%3E(java.lang.String,java.lang.Throwable)"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"isAdmin()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"isAnswered()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"isChangePassword()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"isClosed()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"isCurrentRole(String)","u":"isCurrentRole(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"isEmailed()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"isEnabled()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"isImpersonationActive()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"isImpersonificationActive()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"isLockedOut(YadaUserProfile)","u":"isLockedOut(net.yadaframework.security.persistence.entity.YadaUserProfile)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"isLoggedIn()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"isLoggedIn()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"isLoggedInUser(T)"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"isLoggedUser(T)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"isOpen()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"isReadByRecipient()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"isStackable()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"isTimezoneSetByUser()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"isTrattamentoDatiAccepted()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"lastName"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaChangeUsernameResult","l":"LINK_EXPIRED"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationStatus","l":"LINK_EXPIRED"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"loadUserByUsername(String)","u":"loadUserByUsername(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"locale"},{"p":"net.yadaframework.security.web","c":"YadaSocialRegistrationData","l":"locale"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"loggedIn()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"loggedIn(HttpServletRequest)","u":"loggedIn(jakarta.servlet.http.HttpServletRequest)"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"loggedInUserProfileId"},{"p":"net.yadaframework.security.web","c":"YadaLoginController","l":"loginModal()"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"loginPost"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"loginUrl"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"loginUrlAjax"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"logout(HttpServletRequest)","u":"logout(jakarta.servlet.http.HttpServletRequest)"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"logoutSuccessHandler"},{"p":"net.yadaframework.security.components","c":"YadaSecurityEmailService","l":"makeAutologinLink(String, YadaUserCredentials, Date, String, HttpServletRequest)","u":"makeAutologinLink(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials,java.util.Date,java.lang.String,jakarta.servlet.http.HttpServletRequest)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityEmailService","l":"makeAutologinLink(String, YadaUserCredentials, Date, String, String)","u":"makeAutologinLink(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials,java.util.Date,java.lang.String,java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityEmailService","l":"makeAutologinLink(String, YadaUserCredentials, String)","u":"makeAutologinLink(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials,java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaTokenHandler","l":"makeAutologinLink(YadaAutoLoginToken, String, String, String)","u":"makeAutologinLink(net.yadaframework.security.persistence.entity.YadaAutoLoginToken,java.lang.String,java.lang.String,java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaTokenHandler","l":"makeAutologinLink(YadaAutoLoginToken, String, String, String, HttpServletRequest)","u":"makeAutologinLink(net.yadaframework.security.persistence.entity.YadaAutoLoginToken,java.lang.String,java.lang.String,java.lang.String,jakarta.servlet.http.HttpServletRequest)"},{"p":"net.yadaframework.security.components","c":"YadaTokenHandler","l":"makeAutoLoginToken(YadaUserCredentials)","u":"makeAutoLoginToken(net.yadaframework.security.persistence.entity.YadaUserCredentials)"},{"p":"net.yadaframework.security.components","c":"YadaTokenHandler","l":"makeAutoLoginToken(YadaUserCredentials, Date)","u":"makeAutoLoginToken(net.yadaframework.security.persistence.entity.YadaUserCredentials,java.util.Date)"},{"p":"net.yadaframework.security.components","c":"YadaTokenHandler","l":"makeLink(long, long, Map<String, String>)","u":"makeLink(long,long,java.util.Map)"},{"p":"net.yadaframework.security.components","c":"YadaTokenHandler","l":"makeLink(YadaRegistrationRequest, Map<String, String>)","u":"makeLink(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,java.util.Map)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserMessageDao","l":"markAsRead(List<YadaUserMessage>)","u":"markAsRead(java.util.List)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"message"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"messages"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"middleName"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"modified"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"multipartResolver()"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"mvcHandlerMappingIntrospector()"},{"p":"net.yadaframework.security.web","c":"YadaSocialRegistrationData","l":"name"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaChangeUsernameOutcome","l":"newUsername"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaChangeUsernameResult","l":"OK"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationStatus","l":"OK"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"onAuthenticationFailure(HttpServletRequest, HttpServletResponse, AuthenticationException)","u":"onAuthenticationFailure(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,org.springframework.security.core.AuthenticationException)"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessHandler","l":"onAuthenticationSuccess(HttpServletRequest, HttpServletResponse, Authentication)","u":"onAuthenticationSuccess(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,org.springframework.security.core.Authentication)"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessHandler","l":"onAuthenticationSuccessCustom(HttpServletRequest, Authentication)","u":"onAuthenticationSuccessCustom(jakarta.servlet.http.HttpServletRequest,org.springframework.security.core.Authentication)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketStatus","l":"OPEN"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketType","l":"OTHER"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessageType","l":"OTHER"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"owner"},{"p":"net.yadaframework.security.components","c":"YadaTokenHandler","l":"parseLink(String)","u":"parseLink(java.lang.String)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController","l":"passwordChangeAfterRequest(YadaFormPasswordChange, BindingResult, Model, Locale)","u":"passwordChangeAfterRequest(net.yadaframework.web.form.YadaFormPasswordChange,org.springframework.validation.BindingResult,org.springframework.ui.Model,java.util.Locale)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController","l":"passwordChangeModal(String, String, YadaFormPasswordChange)","u":"passwordChangeModal(java.lang.String,java.lang.String,net.yadaframework.web.form.YadaFormPasswordChange)"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"passwordEncoder"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"passwordMatch(String, YadaUserCredentials)","u":"passwordMatch(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController","l":"passwordResetForm(String, Model, RedirectAttributes)","u":"passwordResetForm(java.lang.String,org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"performPasswordChange(YadaFormPasswordChange)","u":"performPasswordChange(net.yadaframework.web.form.YadaFormPasswordChange)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"performPasswordChange(YadaFormPasswordChange, Boolean)","u":"performPasswordChange(net.yadaframework.web.form.YadaFormPasswordChange,java.lang.Boolean)"},{"p":"net.yadaframework.security.web","c":"YadaSocialRegistrationData","l":"pictureUrl"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"priority"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"priority"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"readByRecipient"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"recipient"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"registrationRequestCleanup(YadaRegistrationRequest)","u":"registrationRequestCleanup(net.yadaframework.security.persistence.entity.YadaRegistrationRequest)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationOutcome","l":"registrationStatus"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"removeRole(Integer)","u":"removeRole(java.lang.Integer)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaCommentMessage","l":"repliesTo"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaTicketDao","l":"replyTicket(Long, String, YadaUserProfile, boolean, boolean)","u":"replyTicket(java.lang.Long,java.lang.String,net.yadaframework.security.persistence.entity.YadaUserProfile,boolean,boolean)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaChangeUsernameResult","l":"REQUEST_INVALID"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationStatus","l":"REQUEST_INVALID"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"REQUESTATTR_CREDENTIALSEXPIREDFLAG"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"REQUESTATTR_GENERICERRORFLAG"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"REQUESTATTR_LOCKOUTMINUTES"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"REQUESTATTR_LOGINERRORFLAG"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"REQUESTATTR_PASSWORD"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"REQUESTATTR_PASSWORDERRORFLAG"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"REQUESTATTR_USERDISABLEDFLAG"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"REQUESTATTR_USERNAME"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"REQUESTATTR_USERNAMENOTFOUNDFLAG"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"resetFailedAttempts(String)","u":"resetFailedAttempts(java.lang.String)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaChangeUsernameOutcome","l":"resultCode"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"save(T)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaAutoLoginTokenDao","l":"save(YadaAutoLoginToken)","u":"save(net.yadaframework.security.persistence.entity.YadaAutoLoginToken)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaRegistrationRequestDao","l":"save(YadaRegistrationRequest)","u":"save(net.yadaframework.security.persistence.entity.YadaRegistrationRequest)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaSocialCredentialsDao","l":"save(YadaSocialCredentials)","u":"save(net.yadaframework.security.persistence.entity.YadaSocialCredentials)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"save(YadaUserCredentials)","u":"save(net.yadaframework.security.persistence.entity.YadaUserCredentials)"},{"p":"net.yadaframework.security","c":"YadaLocalePathRequestCache","l":"saveRequest(HttpServletRequest, HttpServletResponse)","u":"saveRequest(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"},{"p":"net.yadaframework.security","c":"SecurityWebApplicationInitializer","l":"SecurityWebApplicationInitializer()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityEmailService","l":"sendEmailChangeConfirmation(YadaRegistrationRequest, HttpServletRequest, Locale)","u":"sendEmailChangeConfirmation(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,jakarta.servlet.http.HttpServletRequest,java.util.Locale)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"sender"},{"p":"net.yadaframework.security.components","c":"YadaSecurityEmailService","l":"sendPasswordRecovery(YadaRegistrationRequest, HttpServletRequest, Locale)","u":"sendPasswordRecovery(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,jakarta.servlet.http.HttpServletRequest,java.util.Locale)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityEmailService","l":"sendRegistrationConfirmation(YadaRegistrationRequest, Map<String, String>, HttpServletRequest, Locale)","u":"sendRegistrationConfirmation(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,java.util.Map,jakarta.servlet.http.HttpServletRequest,java.util.Locale)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setAssigned(YadaUserProfile)","u":"setAssigned(net.yadaframework.security.persistence.entity.YadaUserProfile)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setAttachment(List<YadaAttachedFile>)","u":"setAttachment(java.util.List)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setAvatar(YadaAttachedFile)","u":"setAvatar(net.yadaframework.persistence.entity.YadaAttachedFile)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setChangePassword(boolean)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setConfirmPassword(String)","u":"setConfirmPassword(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setContentHash(int)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setCreated(List<Date>)","u":"setCreated(java.util.List)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setCreationDate(Date)","u":"setCreationDate(java.util.Date)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setCreationDate(Date)","u":"setCreationDate(java.util.Date)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setData(String)","u":"setData(java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessHandler","l":"setDefaultTargetUrlAjaxRequest(String)","u":"setDefaultTargetUrlAjaxRequest(java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessHandler","l":"setDefaultTargetUrlNormalRequest(String)","u":"setDefaultTargetUrlNormalRequest(java.lang.String)"},{"p":"net.yadaframework.security.web","c":"YadaCropDefinition","l":"setDesktopCrop(Map<String, String>)","u":"setDesktopCrop(java.util.Map)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setEmail(String)","u":"setEmail(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"setEmail(String)","u":"setEmail(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setEmailed(boolean)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setEnabled(boolean)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"setExpiration(Date)","u":"setExpiration(java.util.Date)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setFailedAttempts(int)"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"setFailureUrlAjaxRequest(String)","u":"setFailureUrlAjaxRequest(java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"setFailureUrlNormalRequest(String)","u":"setFailureUrlNormalRequest(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setFirstName(String)","u":"setFirstName(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"setId(Long)","u":"setId(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setId(Long)","u":"setId(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"setId(Long)","u":"setId(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setId(Long)","u":"setId(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setId(Long)","u":"setId(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setId(Long)","u":"setId(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setId(Long)","u":"setId(java.lang.Long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setInitialDate()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setLastFailedAttempt(Date)","u":"setLastFailedAttempt(java.util.Date)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setLastName(String)","u":"setLastName(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setLastSuccessfulLogin(Date)","u":"setLastSuccessfulLogin(java.util.Date)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaCommentMessage","l":"setLikers(Set<YadaUserProfile>)","u":"setLikers(java.util.Set)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setLocale(Locale)","u":"setLocale(java.util.Locale)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setLocale(String)","u":"setLocale(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setMessage(String)","u":"setMessage(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setMessageEscaped(String)","u":"setMessageEscaped(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setMessages(List<YadaTicketMessage>)","u":"setMessages(java.util.List)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setMiddleName(String)","u":"setMiddleName(java.lang.String)"},{"p":"net.yadaframework.security.web","c":"YadaCropDefinition","l":"setMobileCrop(Map<String, String>)","u":"setMobileCrop(java.util.Map)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setModified(Date)","u":"setModified(java.util.Date)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setNewPassword(String)","u":"setNewPassword(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setOnlyRole(Integer)","u":"setOnlyRole(java.lang.Integer)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setOwner(YadaUserProfile)","u":"setOwner(net.yadaframework.security.persistence.entity.YadaUserProfile)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setPassword(String)","u":"setPassword(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setPassword(String)","u":"setPassword(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setPasswordDate(Date)","u":"setPasswordDate(java.util.Date)"},{"p":"net.yadaframework.security.web","c":"YadaCropDefinition","l":"setPdfCrop(Map<String, String>)","u":"setPdfCrop(java.util.Map)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setPriority(int)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setPriority(int)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setReadByRecipient(boolean)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setRecipient(YadaUserProfile)","u":"setRecipient(net.yadaframework.security.persistence.entity.YadaUserProfile)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setRegistrationType(YadaRegistrationType)","u":"setRegistrationType(net.yadaframework.core.YadaRegistrationType)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setRole(Integer)","u":"setRole(java.lang.Integer)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setRoles(List<Integer>)","u":"setRoles(java.util.List)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"setRolesWhenAllowed(YadaUserProfile, List<Integer>, List<Integer>)","u":"setRolesWhenAllowed(net.yadaframework.security.persistence.entity.YadaUserProfile,java.util.List,java.util.List)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setSender(YadaUserProfile)","u":"setSender(net.yadaframework.security.persistence.entity.YadaUserProfile)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"setSocialId(String)","u":"setSocialId(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setStackable(boolean)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setStackSize(int)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setStatus(Integer)","u":"setStatus(java.lang.Integer)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setStatus(YadaPersistentEnum<YadaTicketStatus>)","u":"setStatus(net.yadaframework.persistence.entity.YadaPersistentEnum)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setStatus(YadaTicketStatus)","u":"setStatus(net.yadaframework.security.persistence.entity.YadaTicketStatus)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"setTimestamp(Date)","u":"setTimestamp(java.util.Date)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setTimestamp(Date)","u":"setTimestamp(java.util.Date)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setTimezone(String)","u":"setTimezone(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setTimezone(TimeZone)","u":"setTimezone(java.util.TimeZone)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setTimezone(TimeZone)","u":"setTimezone(java.util.TimeZone)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setTimezoneSetByUser(boolean)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setTitle(String)","u":"setTitle(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setTitle(String)","u":"setTitle(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"setToken(long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setToken(long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaCommentMessage","l":"setTotLikes(long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaCommentMessage","l":"setTotReplies(int)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setTrattamentoDati(YadaClause)","u":"setTrattamentoDati(net.yadaframework.persistence.entity.YadaClause)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setTrattamentoDatiAccepted(boolean)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"setType(int)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setType(YadaLocalEnum<?>)","u":"setType(net.yadaframework.core.YadaLocalEnum)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setType(YadaPersistentEnum<?>)","u":"setType(net.yadaframework.persistence.entity.YadaPersistentEnum)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setType(YadaPersistentEnum<?>)","u":"setType(net.yadaframework.persistence.entity.YadaPersistentEnum)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setType(YLE)"},{"p":"net.yadaframework.security.components","c":"YadaUserSetup","l":"setupApplication()"},{"p":"net.yadaframework.security.components","c":"YadaUserSetup","l":"setupUsers(List<Map<String, Object>>)","u":"setupUsers(java.util.List)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"setUserCredentials(YadaUserCredentials)","u":"setUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setUsername(String)","u":"setUsername(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"setVersion(long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"setVersion(long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"setVersion(long)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketStatus","l":"setYadaPersistentEnum(YadaPersistentEnum)","u":"setYadaPersistentEnum(net.yadaframework.persistence.entity.YadaPersistentEnum)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketType","l":"setYadaPersistentEnum(YadaPersistentEnum<YadaTicketType>)","u":"setYadaPersistentEnum(net.yadaframework.persistence.entity.YadaPersistentEnum)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessageType","l":"setYadaPersistentEnum(YadaPersistentEnum<YadaUserMessageType>)","u":"setYadaPersistentEnum(net.yadaframework.persistence.entity.YadaPersistentEnum)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"setYadaSocialCredentialsList(List<YadaSocialCredentials>)","u":"setYadaSocialCredentialsList(java.util.List)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketMessage","l":"setYadaTicket(YadaTicket)","u":"setYadaTicket(net.yadaframework.security.persistence.entity.YadaTicket)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"setYadaUserCredentials(YadaUserCredentials)","u":"setYadaUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"setYadaUserCredentials(YadaUserCredentials)","u":"setYadaUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"setYadaUserCredentials(YadaUserCredentials)","u":"setYadaUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials)"},{"p":"net.yadaframework.security.web","c":"YadaSocialRegistrationData","l":"socialId"},{"p":"net.yadaframework.security.web","c":"YadaSocialRegistrationData","l":"socialType"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"stackable"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"stackSize"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"status"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"status"},{"p":"net.yadaframework.security.web","c":"YadaLoginController","l":"successHandler"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"successHandler"},{"p":"net.yadaframework.security.web","c":"YadaSocialRegistrationData","l":"surname"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketType","l":"TECHNICAL_SUPPORT"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessageType","l":"TICKET"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"timezone"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"timezoneSetByUser"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"title"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"title"},{"p":"net.yadaframework.security","c":"TooManyFailedAttemptsException","l":"TooManyFailedAttemptsException()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security","c":"TooManyFailedAttemptsException","l":"TooManyFailedAttemptsException(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"net.yadaframework.security","c":"TooManyFailedAttemptsException","l":"TooManyFailedAttemptsException(String, Throwable)","u":"%3Cinit%3E(java.lang.String,java.lang.Throwable)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"toString()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketStatus","l":"toString()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketStatus","l":"toString(MessageSource, Locale)","u":"toString(org.springframework.context.MessageSource,java.util.Locale)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketType","l":"toString(MessageSource, Locale)","u":"toString(org.springframework.context.MessageSource,java.util.Locale)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessageType","l":"toString(MessageSource, Locale)","u":"toString(org.springframework.context.MessageSource,java.util.Locale)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketStatus","l":"toYadaPersistentEnum()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketType","l":"toYadaPersistentEnum()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessageType","l":"toYadaPersistentEnum()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"type"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"type"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"updateLoginTimestamp(String)","u":"updateLoginTimestamp(java.lang.String)"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"updateTimezone(String, TimeZone)","u":"updateTimezone(java.lang.String,java.util.TimeZone)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaChangeUsernameResult","l":"USER_EXISTS"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationStatus","l":"USER_EXISTS"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"userCanChangeRole(YadaUserProfile, Integer)","u":"userCanChangeRole(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.Integer)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"userCanChangeRole(YadaUserProfile, String)","u":"userCanChangeRole(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"userCanEditUser(YadaUserProfile, List<Integer>, List<Integer>)","u":"userCanEditUser(net.yadaframework.security.persistence.entity.YadaUserProfile,java.util.List,java.util.List)"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"userCanImpersonate(YadaUserProfile, YadaUserProfile)","u":"userCanImpersonate(net.yadaframework.security.persistence.entity.YadaUserProfile,net.yadaframework.security.persistence.entity.YadaUserProfile)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"userCredentials"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationOutcome","l":"userProfile"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"validatePasswordSyntax(String)","u":"validatePasswordSyntax(java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"validatePasswordSyntax(String, int, int)","u":"validatePasswordSyntax(java.lang.String,int,int)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketStatus","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketType","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessageType","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaChangeUsernameResult","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationStatus","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketStatus","l":"values()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketType","l":"values()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessageType","l":"values()"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaChangeUsernameResult","l":"values()"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationStatus","l":"values()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"version"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"version"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"version"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"version"},{"p":"net.yadaframework.security.web","c":"YadaActionUploadAttrProcessor","l":"YadaActionUploadAttrProcessor(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationFailureHandler","l":"YadaAuthenticationFailureHandler()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessFilter","l":"YadaAuthenticationSuccessFilter()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.components","c":"YadaAuthenticationSuccessHandler","l":"YadaAuthenticationSuccessHandler()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaAutoLoginToken","l":"YadaAutoLoginToken()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaAutoLoginTokenDao","l":"YadaAutoLoginTokenDao()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaChangeUsernameOutcome","l":"YadaChangeUsernameOutcome()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaCommentMessage","l":"YadaCommentMessage()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaCropDefinition","l":"YadaCropDefinition()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaDialectWithSecurity","l":"YadaDialectWithSecurity(YadaConfiguration)","u":"%3Cinit%3E(net.yadaframework.core.YadaConfiguration)"},{"p":"net.yadaframework.security.exceptions","c":"YadaInvalidUserException","l":"YadaInvalidUserException()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.exceptions","c":"YadaInvalidUserException","l":"YadaInvalidUserException(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"net.yadaframework.security.exceptions","c":"YadaInvalidUserException","l":"YadaInvalidUserException(String, Object...)","u":"%3Cinit%3E(java.lang.String,java.lang.Object...)"},{"p":"net.yadaframework.security.exceptions","c":"YadaInvalidUserException","l":"YadaInvalidUserException(String, Throwable)","u":"%3Cinit%3E(java.lang.String,java.lang.Throwable)"},{"p":"net.yadaframework.security.exceptions","c":"YadaInvalidUserException","l":"YadaInvalidUserException(Throwable)","u":"%3Cinit%3E(java.lang.Throwable)"},{"p":"net.yadaframework.security.exceptions","c":"YadaInvalidUserException","l":"YadaInvalidUserException(Throwable, String, Object...)","u":"%3Cinit%3E(java.lang.Throwable,java.lang.String,java.lang.Object...)"},{"p":"net.yadaframework.security","c":"YadaLocalePathRequestCache","l":"YadaLocalePathRequestCache()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaLoginController","l":"YadaLoginController()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaLoginController","l":"yadaLoginSuccess(String, Model)","u":"yadaLoginSuccess(java.lang.String,org.springframework.ui.Model)"},{"p":"net.yadaframework.security.components","c":"YadaLogoutSuccessHandler","l":"YadaLogoutSuccessHandler()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaMiscController","l":"YadaMiscController()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController","l":"yadaPasswordResetPost(YadaRegistrationRequest, BindingResult, Locale, RedirectAttributes, HttpServletRequest, HttpServletResponse)","u":"yadaPasswordResetPost(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,org.springframework.validation.BindingResult,java.util.Locale,org.springframework.web.servlet.mvc.support.RedirectAttributes,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController","l":"YadaRegistrationController()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationOutcome","l":"YadaRegistrationOutcome()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaRegistrationController.YadaRegistrationOutcome","l":"yadaRegistrationRequest"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaRegistrationRequest","l":"YadaRegistrationRequest()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaRegistrationRequestDao","l":"YadaRegistrationRequestDao()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityBeans","l":"YadaSecurityBeans()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security","c":"YadaSecurityConfig","l":"YadaSecurityConfig()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.components","c":"YadaSecurityEmailService","l":"YadaSecurityEmailService()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"yadaSecurityUtil"},{"p":"net.yadaframework.security.components","c":"YadaSecurityUtil","l":"YadaSecurityUtil()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"YadaSession()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaSocialCredentials","l":"YadaSocialCredentials()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaSocialCredentialsDao","l":"YadaSocialCredentialsDao()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaSocialRegistrationData","l":"YadaSocialRegistrationData()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketMessage","l":"yadaTicket"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicket","l":"YadaTicket()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaTicketDao","l":"YadaTicketDao()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaTicketMessage","l":"YadaTicketMessage()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaTicketMessageDao","l":"YadaTicketMessageDao()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.components","c":"YadaTokenHandler","l":"YadaTokenHandler()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserCredentials","l":"YadaUserCredentials()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"yadaUserCredentialsDao"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserCredentialsDao","l":"YadaUserCredentialsDao()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"yadaUserDetailsService"},{"p":"net.yadaframework.security.components","c":"YadaUserDetailsService","l":"YadaUserDetailsService()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserMessage","l":"YadaUserMessage()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserMessageDao","l":"YadaUserMessageDao()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.persistence.entity","c":"YadaUserProfile","l":"YadaUserProfile()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"yadaUserProfileDao"},{"p":"net.yadaframework.security.persistence.repository","c":"YadaUserProfileDao","l":"YadaUserProfileDao()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.components","c":"YadaUserSetup","l":"YadaUserSetup()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security.web","c":"YadaSession","l":"yadaUtil"},{"p":"net.yadaframework.security","c":"YadaWebSecurityConfig","l":"YadaWebSecurityConfig()","u":"%3Cinit%3E()"},{"p":"net.yadaframework.security","c":"YadaWrappedSavedRequest","l":"YadaWrappedSavedRequest(SavedRequest, YadaWebUtil)","u":"%3Cinit%3E(org.springframework.security.web.savedrequest.SavedRequest,net.yadaframework.components.YadaWebUtil)"}];updateSearchResults();