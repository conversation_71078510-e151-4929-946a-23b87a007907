package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.Led;

@Repository
@Transactional(readOnly = true)
public class LedDao {

    @PersistenceContext EntityManager em;

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Led", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Led save(Led entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Led> findById(Long entityId) {
        Led result = em.find(Led.class, entityId);
        return Optional.ofNullable(result);
    }

    public Led findOne(Long entityId) {
        return em.find(Led.class, entityId);
    }

    public List<Led> findAll() {
        return em.createQuery("from Led", Led.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Led> batchToSave) {
        for (Led entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Led entity) {
        em.remove(entity);
    }
}
