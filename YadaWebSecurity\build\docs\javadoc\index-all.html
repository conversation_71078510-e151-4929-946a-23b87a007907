<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Index (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="index">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li class="nav-bar-cell1-rev">Index</li>
<li><a href="help-doc.html#index">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>Index</h1>
</div>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:O">O</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:Y">Y</a>&nbsp;<br><a href="allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="allpackages-index.html">All&nbsp;Packages</a><span class="vertical-separator">|</span><a href="constant-values.html">Constant&nbsp;Field&nbsp;Values</a><span class="vertical-separator">|</span><a href="serialized-form.html">Serialized&nbsp;Form</a>
<h2 class="title" id="I:A">A</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html#accessToken" class="member-name-link">accessToken</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#addAttachment(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">addAttachment(YadaAttachedFile)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>
<div class="block">Adds a new attachment to this message</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#addCropQueue(java.lang.String,java.lang.String)" class="member-name-link">addCropQueue(String, String)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Starts a new crop operation deleting any stale images.</div>
</dd>
<dt><a href="net/yadaframework/security/YadaWebSecurityConfig.html#addExtraDialect(org.thymeleaf.spring6.SpringTemplateEngine)" class="member-name-link">addExtraDialect(SpringTemplateEngine)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWebSecurityConfig.html" title="class in net.yadaframework.security">YadaWebSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#addLoginErrorParams(java.lang.String)" class="member-name-link">addLoginErrorParams(String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Add to some url the login error request parameters defined in YadaAuthenticationFailureHandler so that the login modal
 can show them.</div>
</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html#addOrUpdateUrlParameter(java.lang.String,java.lang.String)" class="member-name-link">addOrUpdateUrlParameter(String, String)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></dt>
<dd>
<div class="block">Add a url parameter or change its value if present</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#addRole(java.lang.Integer)" class="member-name-link">addRole(Integer)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>
<div class="block">Add a role if not already present.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#addRoles(java.lang.Integer%5B%5D)" class="member-name-link">addRoles(Integer[])</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>
<div class="block">Add all roles if not already present.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html#addTicket(net.yadaframework.core.YadaLocalEnum,java.lang.String,java.lang.String,net.yadaframework.security.persistence.entity.YadaUserProfile,int,org.springframework.web.multipart.MultipartFile)" class="member-name-link">addTicket(YadaLocalEnum&lt;?&gt;, String, String, YadaUserProfile, int, MultipartFile)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html" title="class in net.yadaframework.security.persistence.repository">YadaTicketDao</a></dt>
<dd>
<div class="block">Opens a new ticket</div>
</dd>
<dt><a href="net/yadaframework/security/YadaWebSecurityConfig.html#addYadaDialect(org.thymeleaf.spring6.SpringTemplateEngine)" class="member-name-link">addYadaDialect(SpringTemplateEngine)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWebSecurityConfig.html" title="class in net.yadaframework.security">YadaWebSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#addYadaSocialCredentials(net.yadaframework.security.persistence.entity.YadaSocialCredentials)" class="member-name-link">addYadaSocialCredentials(YadaSocialCredentials)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#AJAX_LOGGEDIN_HEADER" class="member-name-link">AJAX_LOGGEDIN_HEADER</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#AJAX_LOGGEDIN_PARAM" class="member-name-link">AJAX_LOGGEDIN_PARAM</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaLoginController.html#ajaxLoginForm(org.springframework.ui.Model)" class="member-name-link">ajaxLoginForm(Model)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaLoginController.html" title="class in net.yadaframework.security.web">YadaLoginController</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaLoginController.html#ajaxLoginOk(org.springframework.ui.Model)" class="member-name-link">ajaxLoginOk(Model)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaLoginController.html" title="class in net.yadaframework.security.web">YadaLoginController</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html#ANSWERED" class="member-name-link">ANSWERED</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#assigned" class="member-name-link">assigned</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#attachment" class="member-name-link">attachment</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html#ATTR_NAME" class="member-name-link">ATTR_NAME</a> - Static variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html" title="class in net.yadaframework.security.web">YadaActionUploadAttrProcessor</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html#ATTR_PRECEDENCE" class="member-name-link">ATTR_PRECEDENCE</a> - Static variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html" title="class in net.yadaframework.security.web">YadaActionUploadAttrProcessor</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/AuditFilter.html" class="type-name-link" title="class in net.yadaframework.security">AuditFilter</a> - Class in <a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a></dt>
<dd>
<div class="block">Inietta il session ID nell'MDC di logback, chiamandolo "session".</div>
</dd>
<dt><a href="net/yadaframework/security/AuditFilter.html#%3Cinit%3E()" class="member-name-link">AuditFilter()</a> - Constructor for class net.yadaframework.security.<a href="net/yadaframework/security/AuditFilter.html" title="class in net.yadaframework.security">AuditFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">authenticateAs(YadaUserCredentials)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">because for Spring 5</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,boolean)" class="member-name-link">authenticateAs(YadaUserCredentials, boolean)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">because for Spring 5 we need Request and Response</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,boolean,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">authenticateAs(YadaUserCredentials, boolean, HttpServletRequest, HttpServletResponse)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>
<div class="block">Manual authentication for Spring Security 6.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">authenticateAs(YadaUserCredentials, HttpServletRequest, HttpServletResponse)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>
<div class="block">Manual authentication for Spring Security 6 without setting the login timestamp.</div>
</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#authorizationManagers(java.util.List)" class="member-name-link">authorizationManagers(List&lt;SecurityFilterChain&gt;)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaLoginController.html#autologin(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes,jakarta.servlet.http.HttpSession,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">autologin(String, String, RedirectAttributes, HttpSession, HttpServletRequest, HttpServletResponse)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaLoginController.html" title="class in net.yadaframework.security.web">YadaLoginController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#avatar" class="member-name-link">avatar</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:B">B</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/AuditFilter.html#beforeRequest(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">beforeRequest(HttpServletRequest)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/AuditFilter.html" title="class in net.yadaframework.security">AuditFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/SecurityWebApplicationInitializer.html#beforeSpringSecurityFilterChain(jakarta.servlet.ServletContext)" class="member-name-link">beforeSpringSecurityFilterChain(ServletContext)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/SecurityWebApplicationInitializer.html" title="class in net.yadaframework.security">SecurityWebApplicationInitializer</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html#BILLING" class="member-name-link">BILLING</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#buildLink(java.lang.String)" class="member-name-link">buildLink(String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></dt>
<dd>
<div class="block">Convert a site-relative link to absolute, because in emails we can't use @{}.</div>
</dd>
</dl>
<h2 class="title" id="I:C">C</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#caseAnonAuth(java.lang.String,java.lang.String)" class="member-name-link">caseAnonAuth(String, String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Ritorna uno o l'altro parametro a seconda che l'utente corrente sia autenticato o meno.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#changeCurrentRoles(org.springframework.security.core.Authentication,int%5B%5D)" class="member-name-link">changeCurrentRoles(Authentication, int[])</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>
<div class="block">Change the roles of the currently authenticated user, but not on the database</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#changePassword(java.lang.String,org.springframework.security.crypto.password.PasswordEncoder)" class="member-name-link">changePassword(String, PasswordEncoder)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">because it arbitrarily clears the changePassword flag</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#changePassword(net.yadaframework.security.persistence.entity.YadaUserCredentials,java.lang.String)" class="member-name-link">changePassword(YadaUserCredentials, String)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="block">Change the password</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#changePassword(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.String)" class="member-name-link">changePassword(YadaUserProfile, String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Set a new password using the configured encoder.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#changePasswordIfAuthenticated(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">changePasswordIfAuthenticated(String, String, String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>
<div class="block">Change the old password with the new password, but only if the old password is valid.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html#changeUsername(java.lang.String)" class="member-name-link">changeUsername(String)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></dt>
<dd>
<div class="block">Change a username after the user clicked on the confirmation email link</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#changeUsername(java.lang.String,java.lang.String)" class="member-name-link">changeUsername(String, String)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="block">Change a username</div>
</dd>
<dt><a href="net/yadaframework/security/CheckSessionFilter.html" class="type-name-link" title="class in net.yadaframework.security">CheckSessionFilter</a> - Class in <a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a></dt>
<dd>
<div class="block">Questo filtro viene eseguito prima di qualunque cosa e, se la richiesta è COMMAND, ritorna "active" o "expired" riguardo la session.</div>
</dd>
<dt><a href="net/yadaframework/security/CheckSessionFilter.html#%3Cinit%3E()" class="member-name-link">CheckSessionFilter()</a> - Constructor for class net.yadaframework.security.<a href="net/yadaframework/security/CheckSessionFilter.html" title="class in net.yadaframework.security">CheckSessionFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#checkUrlAccess(jakarta.servlet.http.HttpServletRequest,java.lang.String)" class="member-name-link">checkUrlAccess(HttpServletRequest, String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Checks if the current user has access to the specified path</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#clearAnySavedRequest()" class="member-name-link">clearAnySavedRequest()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#clearCaches()" class="member-name-link">clearCaches()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#clearUserProfileCache()" class="member-name-link">clearUserProfileCache()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html#CLOSED" class="member-name-link">CLOSED</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaLoginController.html#closeModal()" class="member-name-link">closeModal()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaLoginController.html" title="class in net.yadaframework.security.web">YadaLoginController</a></dt>
<dd>
<div class="block">This can be the success handler target url in order to close the login modal after successful login</div>
</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.CustomAuthenticationEntryPoint.html#commence(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,org.springframework.security.core.AuthenticationException)" class="member-name-link">commence(HttpServletRequest, HttpServletResponse, AuthenticationException)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.CustomAuthenticationEntryPoint.html" title="class in net.yadaframework.security">YadaSecurityConfig.CustomAuthenticationEntryPoint</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html#COMMENT" class="member-name-link">COMMENT</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html#COMMERCIAL_SUPPORT" class="member-name-link">COMMERCIAL_SUPPORT</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#computeHash()" class="member-name-link">computeHash()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#config" class="member-name-link">config</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#configure(org.springframework.security.config.annotation.web.builders.HttpSecurity)" class="member-name-link">configure(HttpSecurity)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>
<div class="block">Configures basic security settings.</div>
</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#configureGlobal(org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder)" class="member-name-link">configureGlobal(AuthenticationManagerBuilder)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#contentHash" class="member-name-link">contentHash</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#copyLoginErrorParams(jakarta.servlet.http.HttpServletRequest,org.springframework.ui.Model)" class="member-name-link">copyLoginErrorParams(HttpServletRequest, Model)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Copy all not-null login error parameters to the Model</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html#countAllYadaTicketOpenNative()" class="member-name-link">countAllYadaTicketOpenNative()</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html" title="class in net.yadaframework.security.persistence.repository">YadaTicketDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#create(java.lang.String,java.lang.String,java.util.Set)" class="member-name-link">create(String, String, Set&lt;Integer&gt;)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="block">Create a new YadaUserCredentials object that holds login information for the user</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#create(java.util.Map,java.lang.Class)" class="member-name-link">create(Map&lt;String, Object&gt;, Class&lt;T&gt;)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="block">Creates a user when it doesn't exists, using the configured attributes.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#created" class="member-name-link">created</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html#createNewUser(java.lang.String,java.lang.String,java.lang.String%5B%5D,java.util.Locale,java.lang.Class)" class="member-name-link">createNewUser(String, String, String[], Locale, Class&lt;T&gt;)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></dt>
<dd>
<div class="block">Create a new user.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html#createOrIncrement(net.yadaframework.security.persistence.entity.YadaUserMessage)" class="member-name-link">createOrIncrement(YadaUserMessage&lt;?&gt;)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a></dt>
<dd>
<div class="block">Save a message.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#creationDate" class="member-name-link">creationDate</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaMiscController.html#cropCancel(net.yadaframework.security.web.YadaCropDefinition,org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">cropCancel(YadaCropDefinition, Model, RedirectAttributes)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaMiscController.html" title="class in net.yadaframework.security.web">YadaMiscController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaMiscController.html#cropPerform(net.yadaframework.security.web.YadaCropDefinition,org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">cropPerform(YadaCropDefinition, Model, RedirectAttributes)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaMiscController.html" title="class in net.yadaframework.security.web">YadaMiscController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#cropQueue" class="member-name-link">cropQueue</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.CustomAuthenticationEntryPoint.html#%3Cinit%3E()" class="member-name-link">CustomAuthenticationEntryPoint()</a> - Constructor for class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.CustomAuthenticationEntryPoint.html" title="class in net.yadaframework.security">YadaSecurityConfig.CustomAuthenticationEntryPoint</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:D">D</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#daoAuthenticationProvider()" class="member-name-link">daoAuthenticationProvider()</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>
<div class="block">Creates a DaoAuthenticationProvider bean with custom configuration.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#data" class="member-name-link">data</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#DEFAULT_LOGIN_POST" class="member-name-link">DEFAULT_LOGIN_POST</a> - Static variable in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#DEFAULT_LOGIN_URL" class="member-name-link">DEFAULT_LOGIN_URL</a> - Static variable in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#DEFAULT_LOGIN_URL_AJAX" class="member-name-link">DEFAULT_LOGIN_URL_AJAX</a> - Static variable in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#deimpersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">deimpersonate(HttpServletRequest, HttpServletResponse)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Terminates impersonation.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html#delete(net.yadaframework.security.persistence.entity.YadaRegistrationRequest)" class="member-name-link">delete(YadaRegistrationRequest)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html" title="class in net.yadaframework.security.persistence.repository">YadaRegistrationRequestDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html#deleteBelongingTo(net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">deleteBelongingTo(YadaUserProfile)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a></dt>
<dd>
<div class="block">Delete all messages that do not involve users other than the one specified (no other users as sender o recipient)</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html#deleteByYadaUserCredentialsAndType(net.yadaframework.security.persistence.entity.YadaUserCredentials,int)" class="member-name-link">deleteByYadaUserCredentialsAndType(YadaUserCredentials, int)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaSocialCredentialsDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#deleteCropQueue()" class="member-name-link">deleteCropQueue()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html#deleteExpired()" class="member-name-link">deleteExpired()</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html" title="class in net.yadaframework.security.persistence.repository">YadaAutoLoginTokenDao</a></dt>
<dd>
<div class="block">Delete expired elements</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#depersonate()" class="member-name-link">depersonate()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use 
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#deimpersonate()</code></pre>
</details>
 instead.</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#depersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">depersonate(HttpServletRequest, HttpServletResponse)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use <a href="net/yadaframework/security/web/YadaSession.html#deimpersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"><code><span class="invalid-tag">invalid input: 'instead'</span></code></a></div>
</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#depersonify()" class="member-name-link">depersonify()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use 
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#deimpersonate()</code></pre>
</details>
 instead.</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html#destroy()" class="member-name-link">destroy()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#determineTargetUrl(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">determineTargetUrl(HttpServletRequest, HttpServletResponse)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></dt>
<dd>
<div class="block">Thread-safe way of changing the default target url based on the request type (ajax/normal)</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaLogoutSuccessHandler.html#determineTargetUrl(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">determineTargetUrl(HttpServletRequest, HttpServletResponse)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaLogoutSuccessHandler.html" title="class in net.yadaframework.security.components">YadaLogoutSuccessHandler</a></dt>
<dd>
<div class="block">When the "locale in path" is enabled, ensures that the target logout url has the language in place.</div>
</dd>
<dt><a href="net/yadaframework/security/CheckSessionFilter.html#doFilter(jakarta.servlet.ServletRequest,jakarta.servlet.ServletResponse,jakarta.servlet.FilterChain)" class="member-name-link">doFilter(ServletRequest, ServletResponse, FilterChain)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/CheckSessionFilter.html" title="class in net.yadaframework.security">CheckSessionFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html#doFilter(jakarta.servlet.ServletRequest,jakarta.servlet.ServletResponse,jakarta.servlet.FilterChain)" class="member-name-link">doFilter(ServletRequest, ServletResponse, FilterChain)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/AuditFilter.html#doFilterInternal(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,jakarta.servlet.FilterChain)" class="member-name-link">doFilterInternal(HttpServletRequest, HttpServletResponse, FilterChain)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/AuditFilter.html" title="class in net.yadaframework.security">AuditFilter</a></dt>
<dd>
<div class="block">Forwards the request to the next filter in the chain and delegates down to the subclasses to perform the actual
 request logging both before and after the request is processed.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html#doProcess(org.thymeleaf.context.ITemplateContext,org.thymeleaf.model.IProcessableElementTag,org.thymeleaf.engine.AttributeName,java.lang.String,org.thymeleaf.processor.element.IElementTagStructureHandler)" class="member-name-link">doProcess(ITemplateContext, IProcessableElementTag, AttributeName, String, IElementTagStructureHandler)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html" title="class in net.yadaframework.security.web">YadaActionUploadAttrProcessor</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:E">E</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html#email" class="member-name-link">email</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationOutcome</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html#email" class="member-name-link">email</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#emailed" class="member-name-link">emailed</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#ensureRole(java.lang.Integer)" class="member-name-link">ensureRole(Integer)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>
<div class="block">Add a role if not already present.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#ensureRoles(java.lang.Integer%5B%5D)" class="member-name-link">ensureRoles(Integer[])</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>
<div class="block">Add all roles if not already present.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#equals(java.lang.Object)" class="member-name-link">equals(Object)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#equals(java.lang.Object)" class="member-name-link">equals(Object)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html#ERROR" class="member-name-link">ERROR</a> - Enum constant in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html#ERROR" class="member-name-link">ERROR</a> - Enum constant in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#extendAutologinLink(java.lang.String,java.lang.String)" class="member-name-link">extendAutologinLink(String, String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaTokenHandler.html#extendAutologinLink(java.lang.String,java.lang.String)" class="member-name-link">extendAutologinLink(String, String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></dt>
<dd>
<div class="block">Add a string of parameters to the target action link</div>
</dd>
</dl>
<h2 class="title" id="I:F">F</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#failureHandler" class="member-name-link">failureHandler</a> - Variable in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html#FEEDBACK" class="member-name-link">FEEDBACK</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#find(long)" class="member-name-link">find(long)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>
<div class="block">Find a user profile</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html#find(java.lang.Long)" class="member-name-link">find(Long)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html#find(java.lang.Long,net.yadaframework.web.YadaPageRequest,net.yadaframework.persistence.entity.YadaPersistentEnum...)" class="member-name-link">find(Long, YadaPageRequest, YadaPersistentEnum&lt;?&gt;...)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a></dt>
<dd>
<div class="block">Find a page of notifications</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html#findByEmailAndRegistrationType(java.lang.String,net.yadaframework.core.YadaRegistrationType,java.lang.Class)" class="member-name-link">findByEmailAndRegistrationType(String, YadaRegistrationType, Class&lt;R&gt;)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html" title="class in net.yadaframework.security.persistence.repository">YadaRegistrationRequestDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#findById(java.lang.Long)" class="member-name-link">findById(Long)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html#findByIdAndTokenOrderByTimestampDesc(long,long)" class="member-name-link">findByIdAndTokenOrderByTimestampDesc(long, long)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html" title="class in net.yadaframework.security.persistence.repository">YadaAutoLoginTokenDao</a></dt>
<dd>
<div class="block">Returns the objects that match both id and token (should be no more than one I guess)</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html#findByIdAndTokenOrderByTimestampDesc(long,long,java.lang.Class)" class="member-name-link">findByIdAndTokenOrderByTimestampDesc(long, long, Class&lt;R&gt;)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html" title="class in net.yadaframework.security.persistence.repository">YadaRegistrationRequestDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#findByRoleKey(java.lang.String)" class="member-name-link">findByRoleKey(String)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>
<div class="block">Find all user profiles that have the given role key</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html#findBySocialIdAndType(java.lang.String,int)" class="member-name-link">findBySocialIdAndType(String, int)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaSocialCredentialsDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html#findByTimestampBefore(java.util.Date)" class="member-name-link">findByTimestampBefore(Date)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html" title="class in net.yadaframework.security.persistence.repository">YadaRegistrationRequestDao</a></dt>
<dd>
<div class="block">Trova tutti gli elementi con timestamp antecedente la data indicata</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#findByUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials,net.yadaframework.web.YadaPageRequest)" class="member-name-link">findByUserCredentials(YadaUserCredentials, YadaPageRequest)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#findByUserCredentialsId(java.lang.Long)" class="member-name-link">findByUserCredentialsId(Long)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>
<div class="block">Find the profile for the given user credentials id</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#findByUserCredentialsUsername(java.lang.String,net.yadaframework.web.YadaPageRequest)" class="member-name-link">findByUserCredentialsUsername(String, YadaPageRequest)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#findByUsername(java.lang.String)" class="member-name-link">findByUsername(String)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">because there can be no more than one result</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#findByUsername(java.lang.String,net.yadaframework.web.YadaPageRequest)" class="member-name-link">findByUsername(String, YadaPageRequest)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">because there can be no more than one result</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#findByUserProfileId(java.lang.Long)" class="member-name-link">findByUserProfileId(Long)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="block">Find the credentials for the given user profile id</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaTicketMessageDao.html#findByYadaTicketOrderByCreatedDesc(net.yadaframework.security.persistence.entity.YadaTicket)" class="member-name-link">findByYadaTicketOrderByCreatedDesc(YadaTicket)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaTicketMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaTicketMessageDao</a></dt>
<dd>
<div class="block">Find all YadaTicketMessage by one yadaTicket.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html#findByYadaUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">findByYadaUserCredentials(YadaUserCredentials)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html" title="class in net.yadaframework.security.persistence.repository">YadaAutoLoginTokenDao</a></dt>
<dd>
<div class="block">Returns the list of objects associated with the YadaUserCredentials</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html#findByYadaUserCredentialsAndType(net.yadaframework.security.persistence.entity.YadaUserCredentials,int)" class="member-name-link">findByYadaUserCredentialsAndType(YadaUserCredentials, int)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaSocialCredentialsDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#findEnabledUsers()" class="member-name-link">findEnabledUsers()</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>
<div class="block">Find by enabled flag</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#findEnabledUsersWithRole(java.lang.Integer)" class="member-name-link">findEnabledUsersWithRole(Integer)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>
<div class="block">Find by role</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#findFirstByUsername(java.lang.String)" class="member-name-link">findFirstByUsername(String)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="block">Find the first user with the given username</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaTicketMessageDao.html#findMessagesAndAttachmentByYadaTicketOrderByModifiedDesc(net.yadaframework.security.persistence.entity.YadaTicket)" class="member-name-link">findMessagesAndAttachmentByYadaTicketOrderByModifiedDesc(YadaTicket)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaTicketMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaTicketMessageDao</a></dt>
<dd>
<div class="block">Find all YadaTicketMessage and Attachments by one yadaTicket.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html#findOldAnsweredYadaTicketNative()" class="member-name-link">findOldAnsweredYadaTicketNative()</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html" title="class in net.yadaframework.security.persistence.repository">YadaTicketDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#findRoleIds(java.lang.Long)" class="member-name-link">findRoleIds(Long)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#findUserProfileByUsername(java.lang.String)" class="member-name-link">findUserProfileByUsername(String)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#findUserProfileIdByUsername(java.lang.String)" class="member-name-link">findUserProfileIdByUsername(String)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>
<div class="block">Retrieve the userprofile id given the username (email)</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#firstName" class="member-name-link">firstName</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:G">G</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#generateClearPassword()" class="member-name-link">generateClearPassword()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Generate a 32 characters random password</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#generateClearPassword(int)" class="member-name-link">generateClearPassword(int)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Generate a random password</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getAssigned()" class="member-name-link">getAssigned()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getAssignedName()" class="member-name-link">getAssignedName()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getAttachment()" class="member-name-link">getAttachment()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#getAvatar()" class="member-name-link">getAvatar()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getConfirmPassword()" class="member-name-link">getConfirmPassword()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getContentHash()" class="member-name-link">getContentHash()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html#getCookies()" class="member-name-link">getCookies()</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getCreated()" class="member-name-link">getCreated()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getCreationDate()" class="member-name-link">getCreationDate()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getCreationDate()" class="member-name-link">getCreationDate()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#getCropQueue()" class="member-name-link">getCropQueue()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Returns the current YadaCropQueue</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#getCurrentRoles()" class="member-name-link">getCurrentRoles()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Returns Spring-formatted roles, like "ROLE_USER" i.e.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#getCurrentUserProfile()" class="member-name-link">getCurrentUserProfile()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Returns the currently logged-in user profile or null</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#getCurrentUserProfileId()" class="member-name-link">getCurrentUserProfileId()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Returns the id of the YadaUserProfile for the currently logged-in user, if any</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getData()" class="member-name-link">getData()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#getDefaultTargetUrlAjaxRequest()" class="member-name-link">getDefaultTargetUrlAjaxRequest()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#getDefaultTargetUrlNormalRequest()" class="member-name-link">getDefaultTargetUrlNormalRequest()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaCropDefinition.html#getDesktopCrop()" class="member-name-link">getDesktopCrop()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaCropDefinition.html" title="class in net.yadaframework.security.web">YadaCropDefinition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getDT_RowClass()" class="member-name-link">getDT_RowClass()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>
<div class="block">Used in Datatables to define the row class.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getEmail()" class="member-name-link">getEmail()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#getEmail()" class="member-name-link">getEmail()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#getExpiration()" class="member-name-link">getExpiration()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getFailedAttempts()" class="member-name-link">getFailedAttempts()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#getFailureUrlAjaxRequest()" class="member-name-link">getFailureUrlAjaxRequest()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#getFailureUrlNormalRequest()" class="member-name-link">getFailureUrlNormalRequest()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#getFirstName()" class="member-name-link">getFirstName()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html#getHeaderNames()" class="member-name-link">getHeaderNames()</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html#getHeaderValues(java.lang.String)" class="member-name-link">getHeaderValues(String)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#getId()" class="member-name-link">getId()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getId()" class="member-name-link">getId()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#getId()" class="member-name-link">getId()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getId()" class="member-name-link">getId()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getId()" class="member-name-link">getId()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getId()" class="member-name-link">getId()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#getId()" class="member-name-link">getId()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getLastDate()" class="member-name-link">getLastDate()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>
<div class="block">Returns the most recent date of the message stack - which is the initial date if the message is not stackable</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html#getLastDate(net.yadaframework.security.persistence.entity.YadaUserMessage)" class="member-name-link">getLastDate(YadaUserMessage)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a></dt>
<dd>
<div class="block">Returns the most recent date of the message stack - which is the initial date if the message is not stackable</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getLastFailedAttempt()" class="member-name-link">getLastFailedAttempt()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#getLastName()" class="member-name-link">getLastName()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getLastSuccessfulLogin()" class="member-name-link">getLastSuccessfulLogin()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html#getLikers()" class="member-name-link">getLikers()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#getLocale()" class="member-name-link">getLocale()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html#getLocales()" class="member-name-link">getLocales()</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#getLoggedInUserRoleKeys()" class="member-name-link">getLoggedInUserRoleKeys()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#getLoggedInUserRoles()" class="member-name-link">getLoggedInUserRoles()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#getLoginErrorParams(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">getLoginErrorParams(HttpServletRequest)</a> - Static method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>
<div class="block">Returns a sequential list of name and value pairs for the login error parameters/attributes that are not null</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getMessage()" class="member-name-link">getMessage()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getMessages()" class="member-name-link">getMessages()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html#getMethod()" class="member-name-link">getMethod()</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#getMiddleName()" class="member-name-link">getMiddleName()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaCropDefinition.html#getMobileCrop()" class="member-name-link">getMobileCrop()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaCropDefinition.html" title="class in net.yadaframework.security.web">YadaCropDefinition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getModified()" class="member-name-link">getModified()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getNewPassword()" class="member-name-link">getNewPassword()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getOwner()" class="member-name-link">getOwner()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getOwnerName()" class="member-name-link">getOwnerName()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html#getParameterMap()" class="member-name-link">getParameterMap()</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html#getParameterValues(java.lang.String)" class="member-name-link">getParameterValues(String)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getPassword()" class="member-name-link">getPassword()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getPassword()" class="member-name-link">getPassword()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getPasswordDate()" class="member-name-link">getPasswordDate()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaCropDefinition.html#getPdfCrop()" class="member-name-link">getPdfCrop()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaCropDefinition.html" title="class in net.yadaframework.security.web">YadaCropDefinition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getPriority()" class="member-name-link">getPriority()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getPriority()" class="member-name-link">getPriority()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaDialectWithSecurity.html#getProcessors(java.lang.String)" class="member-name-link">getProcessors(String)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaDialectWithSecurity.html" title="class in net.yadaframework.security.web">YadaDialectWithSecurity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getReceiverName()" class="member-name-link">getReceiverName()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getRecipient()" class="member-name-link">getRecipient()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html#getRedirectUrl()" class="member-name-link">getRedirectUrl()</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getRegistrationType()" class="member-name-link">getRegistrationType()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getRoles()" class="member-name-link">getRoles()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#getSavedRequestUrl()" class="member-name-link">getSavedRequestUrl()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Ritorna la richiesta che era stata salvata da Spring Security prima del login, bloccata perchè l'utente non era autenticato</div>
</dd>
<dt><a href="net/yadaframework/security/SecurityWebApplicationInitializer.html#getSecurityDispatcherTypes()" class="member-name-link">getSecurityDispatcherTypes()</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/SecurityWebApplicationInitializer.html" title="class in net.yadaframework.security">SecurityWebApplicationInitializer</a></dt>
<dd>
<div class="block">We need to add FORWARD to the filter mapping otherwise yadaLocalePathVariableFilter will skip security on forward (and all the following filters too)</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getSender()" class="member-name-link">getSender()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getSenderName()" class="member-name-link">getSenderName()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#getSocialId()" class="member-name-link">getSocialId()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getStackSize()" class="member-name-link">getStackSize()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getStatus()" class="member-name-link">getStatus()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getStatus()" class="member-name-link">getStatus()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#getTimestamp()" class="member-name-link">getTimestamp()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getTimestamp()" class="member-name-link">getTimestamp()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getTimestampAsRelative(java.util.Locale)" class="member-name-link">getTimestampAsRelative(Locale)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>
<div class="block">Returns the timestamp formatted as a relative time from now, in the recipient's timezone</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getTimezone()" class="member-name-link">getTimezone()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#getTimezone()" class="member-name-link">getTimezone()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getTitle()" class="member-name-link">getTitle()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getTitle()" class="member-name-link">getTitle()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#getToken()" class="member-name-link">getToken()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getToken()" class="member-name-link">getToken()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html#getTotLikes()" class="member-name-link">getTotLikes()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html#getTotReplies()" class="member-name-link">getTotReplies()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getTrattamentoDati()" class="member-name-link">getTrattamentoDati()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#getType()" class="member-name-link">getType()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getType()" class="member-name-link">getType()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getType()" class="member-name-link">getType()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#getUserCredentials()" class="member-name-link">getUserCredentials()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#getUsername()" class="member-name-link">getUsername()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getUsername()" class="member-name-link">getUsername()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#getVersion()" class="member-name-link">getVersion()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getVersion()" class="member-name-link">getVersion()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#getVersion()" class="member-name-link">getVersion()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#getVersion()" class="member-name-link">getVersion()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getVersion()" class="member-name-link">getVersion()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#getVersion()" class="member-name-link">getVersion()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#getVersion()" class="member-name-link">getVersion()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#getYadaSocialCredentialsList()" class="member-name-link">getYadaSocialCredentialsList()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html#getYadaTicket()" class="member-name-link">getYadaTicket()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaTicketMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#getYadaUserCredentials()" class="member-name-link">getYadaUserCredentials()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getYadaUserCredentials()" class="member-name-link">getYadaUserCredentials()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#getYadaUserCredentials()" class="member-name-link">getYadaUserCredentials()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:H">H</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html#handleRegistrationConfirmation(java.lang.String,java.lang.String%5B%5D,java.util.Locale,jakarta.servlet.http.HttpSession,java.lang.Class,java.lang.Class)" class="member-name-link">handleRegistrationConfirmation(String, String[], Locale, HttpSession, Class&lt;T&gt;, Class&lt;R&gt;)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></dt>
<dd>
<div class="block">To be called when the link in the registration confirmation email has been clicked.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html#handleRegistrationRequest(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,org.springframework.validation.BindingResult,org.springframework.ui.Model,jakarta.servlet.http.HttpServletRequest,java.util.Locale)" class="member-name-link">handleRegistrationRequest(YadaRegistrationRequest, BindingResult, Model, HttpServletRequest, Locale)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></dt>
<dd>
<div class="block">This method should be called by a registration controller to perform the actual registration</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#hasAnyRole(java.lang.String...)" class="member-name-link">hasAnyRole(String...)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Check if the current user has any of the provided roles, case sensitive.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#hasAnyRoleId(java.lang.Integer...)" class="member-name-link">hasAnyRoleId(Integer...)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>
<div class="block">Check if the user has any of the specified roles</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#hasAnyRoleKey(java.lang.String...)" class="member-name-link">hasAnyRoleKey(String...)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>
<div class="block">Check if the user has any of the specified roles</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#hasCropQueue()" class="member-name-link">hasCropQueue()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Returns true if there are images to be cropped</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#hasCurrentRole(java.lang.String)" class="member-name-link">hasCurrentRole(String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Check if the current user has the provided role, case sensitive.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#hasCurrentRole(java.lang.String%5B%5D)" class="member-name-link">hasCurrentRole(String[])</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use <a href="net/yadaframework/security/components/YadaSecurityUtil.html#hasAnyRole(java.lang.String...)"><code>YadaSecurityUtil.hasAnyRole(String...)</code></a> instead</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#hashCode()" class="member-name-link">hashCode()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#hashCode()" class="member-name-link">hashCode()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#hasRole(java.lang.Integer)" class="member-name-link">hasRole(Integer)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html#hasUnreadMessage(net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">hasUnreadMessage(YadaUserProfile)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a></dt>
<dd>
<div class="block">Returns true if there exists at least one unread message for the user</div>
</dd>
</dl>
<h2 class="title" id="I:I">I</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#id" class="member-name-link">id</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#id" class="member-name-link">id</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#id" class="member-name-link">id</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#impersonate(java.lang.Long)" class="member-name-link">impersonate(Long)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Assume the identity of the given user</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#impersonate(java.lang.Long,java.lang.String,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">impersonate(Long, String, HttpServletRequest, HttpServletResponse)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Assume the identity of the given user</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#impersonatedUserId" class="member-name-link">impersonatedUserId</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#impersonationStartingLocation" class="member-name-link">impersonationStartingLocation</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#impersonatorUserId" class="member-name-link">impersonatorUserId</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#impersonify(java.lang.Long)" class="member-name-link">impersonify(Long)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#incrementFailedAttempts(java.lang.String)" class="member-name-link">incrementFailedAttempts(String)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="block">Updates the login failed attempts counter for the user</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#incrementStack()" class="member-name-link">incrementStack()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#init()" class="member-name-link">init()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>
<div class="block">Computes the content hash before persisting.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html#init()" class="member-name-link">init()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html#init(jakarta.servlet.FilterConfig)" class="member-name-link">init(FilterConfig)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/exceptions/InternalAuthenticationException.html" class="type-name-link" title="class in net.yadaframework.security.exceptions">InternalAuthenticationException</a> - Exception Class in <a href="net/yadaframework/security/exceptions/package-summary.html">net.yadaframework.security.exceptions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/exceptions/InternalAuthenticationException.html#%3Cinit%3E(java.lang.String)" class="member-name-link">InternalAuthenticationException(String)</a> - Constructor for exception class net.yadaframework.security.exceptions.<a href="net/yadaframework/security/exceptions/InternalAuthenticationException.html" title="class in net.yadaframework.security.exceptions">InternalAuthenticationException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/exceptions/InternalAuthenticationException.html#%3Cinit%3E(java.lang.String,java.lang.Throwable)" class="member-name-link">InternalAuthenticationException(String, Throwable)</a> - Constructor for exception class net.yadaframework.security.exceptions.<a href="net/yadaframework/security/exceptions/InternalAuthenticationException.html" title="class in net.yadaframework.security.exceptions">InternalAuthenticationException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#isAdmin()" class="member-name-link">isAdmin()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Check if the current user has the role "ADMIN"</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#isAnswered()" class="member-name-link">isAnswered()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#isChangePassword()" class="member-name-link">isChangePassword()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#isClosed()" class="member-name-link">isClosed()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#isCurrentRole(java.lang.String)" class="member-name-link">isCurrentRole(String)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Check if the current logged in user (if any) has the specified role.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#isEmailed()" class="member-name-link">isEmailed()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#isEnabled()" class="member-name-link">isEnabled()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#isImpersonationActive()" class="member-name-link">isImpersonationActive()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#isImpersonificationActive()" class="member-name-link">isImpersonificationActive()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#isLockedOut(net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">isLockedOut(YadaUserProfile)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Check if a user has been suspended for excess of login failures</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#isLoggedIn()" class="member-name-link">isLoggedIn()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Check if the current user is authenticated (logged in) not anonymously.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#isLoggedIn()" class="member-name-link">isLoggedIn()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Check if the current user is authenticated (logged in) not anonymously.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#isLoggedInUser(T)" class="member-name-link">isLoggedInUser(T)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="block">Check if the argument userProfile is the same as the currently logged-in one</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#isLoggedUser(T)" class="member-name-link">isLoggedUser(T)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use <a href="net/yadaframework/security/web/YadaSession.html#isLoggedInUser(T)"><code>YadaSession.isLoggedInUser(YadaUserProfile)</code></a> instead</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#isOpen()" class="member-name-link">isOpen()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#isReadByRecipient()" class="member-name-link">isReadByRecipient()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#isStackable()" class="member-name-link">isStackable()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#isTimezoneSetByUser()" class="member-name-link">isTimezoneSetByUser()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#isTrattamentoDatiAccepted()" class="member-name-link">isTrattamentoDatiAccepted()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
</dl>
<h2 class="title" id="I:L">L</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#lastName" class="member-name-link">lastName</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html#LINK_EXPIRED" class="member-name-link">LINK_EXPIRED</a> - Enum constant in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html#LINK_EXPIRED" class="member-name-link">LINK_EXPIRED</a> - Enum constant in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#loadUserByUsername(java.lang.String)" class="member-name-link">loadUserByUsername(String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#locale" class="member-name-link">locale</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html#locale" class="member-name-link">locale</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#loggedIn()" class="member-name-link">loggedIn()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#loggedIn(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">loggedIn(HttpServletRequest)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Check if the current user is logged in.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#loggedInUserProfileId" class="member-name-link">loggedInUserProfileId</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaLoginController.html#loginModal()" class="member-name-link">loginModal()</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaLoginController.html" title="class in net.yadaframework.security.web">YadaLoginController</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#loginPost" class="member-name-link">loginPost</a> - Variable in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#loginUrl" class="member-name-link">loginUrl</a> - Variable in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#loginUrlAjax" class="member-name-link">loginUrlAjax</a> - Variable in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#logout(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">logout(HttpServletRequest)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Logs out the currently logged-in user</div>
</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#logoutSuccessHandler" class="member-name-link">logoutSuccessHandler</a> - Variable in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:M">M</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#makeAutologinLink(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials,java.lang.String)" class="member-name-link">makeAutologinLink(String, YadaUserCredentials, String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#makeAutologinLink(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials,java.util.Date,java.lang.String,jakarta.servlet.http.HttpServletRequest)" class="member-name-link">makeAutologinLink(String, YadaUserCredentials, Date, String, HttpServletRequest)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#makeAutologinLink(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials,java.util.Date,java.lang.String,java.lang.String)" class="member-name-link">makeAutologinLink(String, YadaUserCredentials, Date, String, String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaTokenHandler.html#makeAutologinLink(net.yadaframework.security.persistence.entity.YadaAutoLoginToken,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">makeAutologinLink(YadaAutoLoginToken, String, String, String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></dt>
<dd>
<div class="block">Return the autologin link generated from the given parameters</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaTokenHandler.html#makeAutologinLink(net.yadaframework.security.persistence.entity.YadaAutoLoginToken,java.lang.String,java.lang.String,java.lang.String,jakarta.servlet.http.HttpServletRequest)" class="member-name-link">makeAutologinLink(YadaAutoLoginToken, String, String, String, HttpServletRequest)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></dt>
<dd>
<div class="block">Return the autologin link generated from the given parameters</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaTokenHandler.html#makeAutoLoginToken(net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">makeAutoLoginToken(YadaUserCredentials)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></dt>
<dd>
<div class="block">Create a new YadaAutoLoginToken for the given user that expires after the configured amount of hours (config/security/autologinExpirationHours)</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaTokenHandler.html#makeAutoLoginToken(net.yadaframework.security.persistence.entity.YadaUserCredentials,java.util.Date)" class="member-name-link">makeAutoLoginToken(YadaUserCredentials, Date)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></dt>
<dd>
<div class="block">Create a new YadaAutoLoginToken for the given user</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaTokenHandler.html#makeLink(long,long,java.util.Map)" class="member-name-link">makeLink(long, long, Map&lt;String, String&gt;)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></dt>
<dd>
<div class="block">Create a token-link, used both for autologin links and registration links.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaTokenHandler.html#makeLink(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,java.util.Map)" class="member-name-link">makeLink(YadaRegistrationRequest, Map&lt;String, String&gt;)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></dt>
<dd>
<div class="block">Create a token-link</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html#markAsRead(java.util.List)" class="member-name-link">markAsRead(List&lt;YadaUserMessage&gt;)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#message" class="member-name-link">message</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#messages" class="member-name-link">messages</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#middleName" class="member-name-link">middleName</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#modified" class="member-name-link">modified</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#multipartResolver()" class="member-name-link">multipartResolver()</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#mvcHandlerMappingIntrospector()" class="member-name-link">mvcHandlerMappingIntrospector()</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:N">N</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html#name" class="member-name-link">name</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a> - package net.yadaframework.security</dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a> - package net.yadaframework.security.components</dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/exceptions/package-summary.html">net.yadaframework.security.exceptions</a> - package net.yadaframework.security.exceptions</dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a> - package net.yadaframework.security.persistence.entity</dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/package-summary.html">net.yadaframework.security.persistence.repository</a> - package net.yadaframework.security.persistence.repository</dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a> - package net.yadaframework.security.web</dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameOutcome.html#newUsername" class="member-name-link">newUsername</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameOutcome</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:O">O</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html#OK" class="member-name-link">OK</a> - Enum constant in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html#OK" class="member-name-link">OK</a> - Enum constant in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#onAuthenticationFailure(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,org.springframework.security.core.AuthenticationException)" class="member-name-link">onAuthenticationFailure(HttpServletRequest, HttpServletResponse, AuthenticationException)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#onAuthenticationSuccess(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,org.springframework.security.core.Authentication)" class="member-name-link">onAuthenticationSuccess(HttpServletRequest, HttpServletResponse, Authentication)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#onAuthenticationSuccessCustom(jakarta.servlet.http.HttpServletRequest,org.springframework.security.core.Authentication)" class="member-name-link">onAuthenticationSuccessCustom(HttpServletRequest, Authentication)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></dt>
<dd>
<div class="block">Custom code to be executed after login or autologin.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html#OPEN" class="member-name-link">OPEN</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html#OTHER" class="member-name-link">OTHER</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html#OTHER" class="member-name-link">OTHER</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#owner" class="member-name-link">owner</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:P">P</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/components/YadaTokenHandler.html#parseLink(java.lang.String)" class="member-name-link">parseLink(String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></dt>
<dd>
<div class="block">Splits a token-link string into the two components: id and token.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html#passwordChangeAfterRequest(net.yadaframework.web.form.YadaFormPasswordChange,org.springframework.validation.BindingResult,org.springframework.ui.Model,java.util.Locale)" class="member-name-link">passwordChangeAfterRequest(YadaFormPasswordChange, BindingResult, Model, Locale)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></dt>
<dd>
<div class="block">Default method to change a user password.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html#passwordChangeModal(java.lang.String,java.lang.String,net.yadaframework.web.form.YadaFormPasswordChange)" class="member-name-link">passwordChangeModal(String, String, YadaFormPasswordChange)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#passwordEncoder" class="member-name-link">passwordEncoder</a> - Variable in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#passwordMatch(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">passwordMatch(String, YadaUserCredentials)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>
<div class="block">Check if some string matches the user password</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html#passwordResetForm(java.lang.String,org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">passwordResetForm(String, Model, RedirectAttributes)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></dt>
<dd>
<div class="block">To be called in the controller that handles the password recovery link in the email.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#performPasswordChange(net.yadaframework.web.form.YadaFormPasswordChange)" class="member-name-link">performPasswordChange(YadaFormPasswordChange)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Change the user password and log in</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#performPasswordChange(net.yadaframework.web.form.YadaFormPasswordChange,java.lang.Boolean)" class="member-name-link">performPasswordChange(YadaFormPasswordChange, Boolean)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Change the user password and log in after eventually checking that the password is actually different from the previous one</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html#pictureUrl" class="member-name-link">pictureUrl</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#priority" class="member-name-link">priority</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#priority" class="member-name-link">priority</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:R">R</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#readByRecipient" class="member-name-link">readByRecipient</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#recipient" class="member-name-link">recipient</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#registrationRequestCleanup(net.yadaframework.security.persistence.entity.YadaRegistrationRequest)" class="member-name-link">registrationRequestCleanup(YadaRegistrationRequest)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Cancello le registration request vecchie o con lo stesso email e tipo.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html#registrationStatus" class="member-name-link">registrationStatus</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationOutcome</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#removeRole(java.lang.Integer)" class="member-name-link">removeRole(Integer)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>
<div class="block">Remove a role if present</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html#repliesTo" class="member-name-link">repliesTo</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html#replyTicket(java.lang.Long,java.lang.String,net.yadaframework.security.persistence.entity.YadaUserProfile,boolean,boolean)" class="member-name-link">replyTicket(Long, String, YadaUserProfile, boolean, boolean)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html" title="class in net.yadaframework.security.persistence.repository">YadaTicketDao</a></dt>
<dd>
<div class="block">Send a reply to a ticket.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html#REQUEST_INVALID" class="member-name-link">REQUEST_INVALID</a> - Enum constant in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html#REQUEST_INVALID" class="member-name-link">REQUEST_INVALID</a> - Enum constant in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_CREDENTIALSEXPIREDFLAG" class="member-name-link">REQUESTATTR_CREDENTIALSEXPIREDFLAG</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_GENERICERRORFLAG" class="member-name-link">REQUESTATTR_GENERICERRORFLAG</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_LOCKOUTMINUTES" class="member-name-link">REQUESTATTR_LOCKOUTMINUTES</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_LOGINERRORFLAG" class="member-name-link">REQUESTATTR_LOGINERRORFLAG</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_PASSWORD" class="member-name-link">REQUESTATTR_PASSWORD</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_PASSWORDERRORFLAG" class="member-name-link">REQUESTATTR_PASSWORDERRORFLAG</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_USERDISABLEDFLAG" class="member-name-link">REQUESTATTR_USERDISABLEDFLAG</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_USERNAME" class="member-name-link">REQUESTATTR_USERNAME</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_USERNAMENOTFOUNDFLAG" class="member-name-link">REQUESTATTR_USERNAMENOTFOUNDFLAG</a> - Static variable in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#resetFailedAttempts(java.lang.String)" class="member-name-link">resetFailedAttempts(String)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="block">Resets the login failed attempts counter for the user</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameOutcome.html#resultCode" class="member-name-link">resultCode</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameOutcome</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:S">S</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html#save(net.yadaframework.security.persistence.entity.YadaAutoLoginToken)" class="member-name-link">save(YadaAutoLoginToken)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html" title="class in net.yadaframework.security.persistence.repository">YadaAutoLoginTokenDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html#save(net.yadaframework.security.persistence.entity.YadaRegistrationRequest)" class="member-name-link">save(YadaRegistrationRequest)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html" title="class in net.yadaframework.security.persistence.repository">YadaRegistrationRequestDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html#save(net.yadaframework.security.persistence.entity.YadaSocialCredentials)" class="member-name-link">save(YadaSocialCredentials)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaSocialCredentialsDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#save(net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">save(YadaUserCredentials)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use a higher-level method instead</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#save(T)" class="member-name-link">save(T)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaLocalePathRequestCache.html#saveRequest(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">saveRequest(HttpServletRequest, HttpServletResponse)</a> - Method in class net.yadaframework.security.<a href="net/yadaframework/security/YadaLocalePathRequestCache.html" title="class in net.yadaframework.security">YadaLocalePathRequestCache</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/SecurityWebApplicationInitializer.html" class="type-name-link" title="class in net.yadaframework.security">SecurityWebApplicationInitializer</a> - Class in <a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/SecurityWebApplicationInitializer.html#%3Cinit%3E()" class="member-name-link">SecurityWebApplicationInitializer()</a> - Constructor for class net.yadaframework.security.<a href="net/yadaframework/security/SecurityWebApplicationInitializer.html" title="class in net.yadaframework.security">SecurityWebApplicationInitializer</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#sendEmailChangeConfirmation(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,jakarta.servlet.http.HttpServletRequest,java.util.Locale)" class="member-name-link">sendEmailChangeConfirmation(YadaRegistrationRequest, HttpServletRequest, Locale)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#sender" class="member-name-link">sender</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#sendPasswordRecovery(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,jakarta.servlet.http.HttpServletRequest,java.util.Locale)" class="member-name-link">sendPasswordRecovery(YadaRegistrationRequest, HttpServletRequest, Locale)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></dt>
<dd>
<div class="block">Invio la mail per il recupero password</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#sendRegistrationConfirmation(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,java.util.Map,jakarta.servlet.http.HttpServletRequest,java.util.Locale)" class="member-name-link">sendRegistrationConfirmation(YadaRegistrationRequest, Map&lt;String, String&gt;, HttpServletRequest, Locale)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></dt>
<dd>
<div class="block">Send a confirmation email when some user wants to register with his email address</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setAssigned(net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">setAssigned(YadaUserProfile)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setAttachment(java.util.List)" class="member-name-link">setAttachment(List&lt;YadaAttachedFile&gt;)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setAvatar(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">setAvatar(YadaAttachedFile)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setChangePassword(boolean)" class="member-name-link">setChangePassword(boolean)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setConfirmPassword(java.lang.String)" class="member-name-link">setConfirmPassword(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setContentHash(int)" class="member-name-link">setContentHash(int)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setCreated(java.util.List)" class="member-name-link">setCreated(List&lt;Date&gt;)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setCreationDate(java.util.Date)" class="member-name-link">setCreationDate(Date)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setCreationDate(java.util.Date)" class="member-name-link">setCreationDate(Date)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setData(java.lang.String)" class="member-name-link">setData(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#setDefaultTargetUrlAjaxRequest(java.lang.String)" class="member-name-link">setDefaultTargetUrlAjaxRequest(String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></dt>
<dd>
<div class="block">Target url to redirect after login when the request is Ajax</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#setDefaultTargetUrlNormalRequest(java.lang.String)" class="member-name-link">setDefaultTargetUrlNormalRequest(String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></dt>
<dd>
<div class="block">Target url to redirect after login when the request is not Ajax</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaCropDefinition.html#setDesktopCrop(java.util.Map)" class="member-name-link">setDesktopCrop(Map&lt;String, String&gt;)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaCropDefinition.html" title="class in net.yadaframework.security.web">YadaCropDefinition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setEmail(java.lang.String)" class="member-name-link">setEmail(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#setEmail(java.lang.String)" class="member-name-link">setEmail(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setEmailed(boolean)" class="member-name-link">setEmailed(boolean)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setEnabled(boolean)" class="member-name-link">setEnabled(boolean)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#setExpiration(java.util.Date)" class="member-name-link">setExpiration(Date)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setFailedAttempts(int)" class="member-name-link">setFailedAttempts(int)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#setFailureUrlAjaxRequest(java.lang.String)" class="member-name-link">setFailureUrlAjaxRequest(String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#setFailureUrlNormalRequest(java.lang.String)" class="member-name-link">setFailureUrlNormalRequest(String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setFirstName(java.lang.String)" class="member-name-link">setFirstName(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#setId(java.lang.Long)" class="member-name-link">setId(Long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setId(java.lang.Long)" class="member-name-link">setId(Long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#setId(java.lang.Long)" class="member-name-link">setId(Long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setId(java.lang.Long)" class="member-name-link">setId(Long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setId(java.lang.Long)" class="member-name-link">setId(Long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setId(java.lang.Long)" class="member-name-link">setId(Long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setId(java.lang.Long)" class="member-name-link">setId(Long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setInitialDate()" class="member-name-link">setInitialDate()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setLastFailedAttempt(java.util.Date)" class="member-name-link">setLastFailedAttempt(Date)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setLastName(java.lang.String)" class="member-name-link">setLastName(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setLastSuccessfulLogin(java.util.Date)" class="member-name-link">setLastSuccessfulLogin(Date)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html#setLikers(java.util.Set)" class="member-name-link">setLikers(Set&lt;YadaUserProfile&gt;)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setLocale(java.lang.String)" class="member-name-link">setLocale(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>
<div class="block">Set the locale from a locale string in the form ll_CC like en_US.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setLocale(java.util.Locale)" class="member-name-link">setLocale(Locale)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setMessage(java.lang.String)" class="member-name-link">setMessage(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setMessageEscaped(java.lang.String)" class="member-name-link">setMessageEscaped(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>
<div class="block">Escape all markup-significant characters</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setMessages(java.util.List)" class="member-name-link">setMessages(List&lt;YadaTicketMessage&gt;)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setMiddleName(java.lang.String)" class="member-name-link">setMiddleName(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaCropDefinition.html#setMobileCrop(java.util.Map)" class="member-name-link">setMobileCrop(Map&lt;String, String&gt;)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaCropDefinition.html" title="class in net.yadaframework.security.web">YadaCropDefinition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setModified(java.util.Date)" class="member-name-link">setModified(Date)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setNewPassword(java.lang.String)" class="member-name-link">setNewPassword(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setOnlyRole(java.lang.Integer)" class="member-name-link">setOnlyRole(Integer)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>
<div class="block">Set one role removing any other roles</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setOwner(net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">setOwner(YadaUserProfile)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setPassword(java.lang.String)" class="member-name-link">setPassword(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setPassword(java.lang.String)" class="member-name-link">setPassword(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setPasswordDate(java.util.Date)" class="member-name-link">setPasswordDate(Date)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaCropDefinition.html#setPdfCrop(java.util.Map)" class="member-name-link">setPdfCrop(Map&lt;String, String&gt;)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaCropDefinition.html" title="class in net.yadaframework.security.web">YadaCropDefinition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setPriority(int)" class="member-name-link">setPriority(int)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setPriority(int)" class="member-name-link">setPriority(int)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setReadByRecipient(boolean)" class="member-name-link">setReadByRecipient(boolean)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setRecipient(net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">setRecipient(YadaUserProfile)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setRegistrationType(net.yadaframework.core.YadaRegistrationType)" class="member-name-link">setRegistrationType(YadaRegistrationType)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setRole(java.lang.Integer)" class="member-name-link">setRole(Integer)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">@see #setOnlyRole(Integer)</div>
</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setRoles(java.util.List)" class="member-name-link">setRoles(List&lt;Integer&gt;)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#setRolesWhenAllowed(net.yadaframework.security.persistence.entity.YadaUserProfile,java.util.List,java.util.List)" class="member-name-link">setRolesWhenAllowed(YadaUserProfile, List&lt;Integer&gt;, List&lt;Integer&gt;)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setSender(net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">setSender(YadaUserProfile)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#setSocialId(java.lang.String)" class="member-name-link">setSocialId(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setStackable(boolean)" class="member-name-link">setStackable(boolean)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setStackSize(int)" class="member-name-link">setStackSize(int)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setStatus(java.lang.Integer)" class="member-name-link">setStatus(Integer)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setStatus(net.yadaframework.persistence.entity.YadaPersistentEnum)" class="member-name-link">setStatus(YadaPersistentEnum&lt;YadaTicketStatus&gt;)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setStatus(net.yadaframework.security.persistence.entity.YadaTicketStatus)" class="member-name-link">setStatus(YadaTicketStatus)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#setTimestamp(java.util.Date)" class="member-name-link">setTimestamp(Date)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setTimestamp(java.util.Date)" class="member-name-link">setTimestamp(Date)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setTimezone(java.lang.String)" class="member-name-link">setTimezone(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setTimezone(java.util.TimeZone)" class="member-name-link">setTimezone(TimeZone)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setTimezone(java.util.TimeZone)" class="member-name-link">setTimezone(TimeZone)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setTimezoneSetByUser(boolean)" class="member-name-link">setTimezoneSetByUser(boolean)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setTitle(java.lang.String)" class="member-name-link">setTitle(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setTitle(java.lang.String)" class="member-name-link">setTitle(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#setToken(long)" class="member-name-link">setToken(long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setToken(long)" class="member-name-link">setToken(long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html#setTotLikes(long)" class="member-name-link">setTotLikes(long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html#setTotReplies(int)" class="member-name-link">setTotReplies(int)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setTrattamentoDati(net.yadaframework.persistence.entity.YadaClause)" class="member-name-link">setTrattamentoDati(YadaClause)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setTrattamentoDatiAccepted(boolean)" class="member-name-link">setTrattamentoDatiAccepted(boolean)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#setType(int)" class="member-name-link">setType(int)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setType(net.yadaframework.core.YadaLocalEnum)" class="member-name-link">setType(YadaLocalEnum&lt;?&gt;)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setType(net.yadaframework.persistence.entity.YadaPersistentEnum)" class="member-name-link">setType(YadaPersistentEnum&lt;?&gt;)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setType(net.yadaframework.persistence.entity.YadaPersistentEnum)" class="member-name-link">setType(YadaPersistentEnum&lt;?&gt;)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setType(YLE)" class="member-name-link">setType(YLE)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaUserSetup.html#setupApplication()" class="member-name-link">setupApplication()</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserSetup.html" title="class in net.yadaframework.security.components">YadaUserSetup</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaUserSetup.html#setupUsers(java.util.List)" class="member-name-link">setupUsers(List&lt;Map&lt;String, Object&gt;&gt;)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserSetup.html" title="class in net.yadaframework.security.components">YadaUserSetup</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#setUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">setUserCredentials(YadaUserCredentials)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setUsername(java.lang.String)" class="member-name-link">setUsername(String)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>
<div class="block">Sets the username after trimming and lowercase conversion in the default locale</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#setVersion(long)" class="member-name-link">setVersion(long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#setVersion(long)" class="member-name-link">setVersion(long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#setVersion(long)" class="member-name-link">setVersion(long)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html#setYadaPersistentEnum(net.yadaframework.persistence.entity.YadaPersistentEnum)" class="member-name-link">setYadaPersistentEnum(YadaPersistentEnum)</a> - Method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html#setYadaPersistentEnum(net.yadaframework.persistence.entity.YadaPersistentEnum)" class="member-name-link">setYadaPersistentEnum(YadaPersistentEnum&lt;YadaTicketType&gt;)</a> - Method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html#setYadaPersistentEnum(net.yadaframework.persistence.entity.YadaPersistentEnum)" class="member-name-link">setYadaPersistentEnum(YadaPersistentEnum&lt;YadaUserMessageType&gt;)</a> - Method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setYadaSocialCredentialsList(java.util.List)" class="member-name-link">setYadaSocialCredentialsList(List&lt;YadaSocialCredentials&gt;)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html#setYadaTicket(net.yadaframework.security.persistence.entity.YadaTicket)" class="member-name-link">setYadaTicket(YadaTicket)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaTicketMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#setYadaUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">setYadaUserCredentials(YadaUserCredentials)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setYadaUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">setYadaUserCredentials(YadaUserCredentials)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#setYadaUserCredentials(net.yadaframework.security.persistence.entity.YadaUserCredentials)" class="member-name-link">setYadaUserCredentials(YadaUserCredentials)</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html#socialId" class="member-name-link">socialId</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html#socialType" class="member-name-link">socialType</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#stackable" class="member-name-link">stackable</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#stackSize" class="member-name-link">stackSize</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#status" class="member-name-link">status</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#status" class="member-name-link">status</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaLoginController.html#successHandler" class="member-name-link">successHandler</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaLoginController.html" title="class in net.yadaframework.security.web">YadaLoginController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#successHandler" class="member-name-link">successHandler</a> - Variable in class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html#surname" class="member-name-link">surname</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:T">T</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html#TECHNICAL_SUPPORT" class="member-name-link">TECHNICAL_SUPPORT</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html#TICKET" class="member-name-link">TICKET</a> - Enum constant in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#timezone" class="member-name-link">timezone</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#timezoneSetByUser" class="member-name-link">timezoneSetByUser</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#title" class="member-name-link">title</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#title" class="member-name-link">title</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/TooManyFailedAttemptsException.html" class="type-name-link" title="class in net.yadaframework.security">TooManyFailedAttemptsException</a> - Exception Class in <a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/TooManyFailedAttemptsException.html#%3Cinit%3E()" class="member-name-link">TooManyFailedAttemptsException()</a> - Constructor for exception class net.yadaframework.security.<a href="net/yadaframework/security/TooManyFailedAttemptsException.html" title="class in net.yadaframework.security">TooManyFailedAttemptsException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/TooManyFailedAttemptsException.html#%3Cinit%3E(java.lang.String)" class="member-name-link">TooManyFailedAttemptsException(String)</a> - Constructor for exception class net.yadaframework.security.<a href="net/yadaframework/security/TooManyFailedAttemptsException.html" title="class in net.yadaframework.security">TooManyFailedAttemptsException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/TooManyFailedAttemptsException.html#%3Cinit%3E(java.lang.String,java.lang.Throwable)" class="member-name-link">TooManyFailedAttemptsException(String, Throwable)</a> - Constructor for exception class net.yadaframework.security.<a href="net/yadaframework/security/TooManyFailedAttemptsException.html" title="class in net.yadaframework.security">TooManyFailedAttemptsException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#toString()" class="member-name-link">toString()</a> - Method in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html#toString()" class="member-name-link">toString()</a> - Method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html#toString(org.springframework.context.MessageSource,java.util.Locale)" class="member-name-link">toString(MessageSource, Locale)</a> - Method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></dt>
<dd>
<div class="block">Return the localized text for this enum</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html#toString(org.springframework.context.MessageSource,java.util.Locale)" class="member-name-link">toString(MessageSource, Locale)</a> - Method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></dt>
<dd>
<div class="block">Return the localized text for this enum</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html#toString(org.springframework.context.MessageSource,java.util.Locale)" class="member-name-link">toString(MessageSource, Locale)</a> - Method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a></dt>
<dd>
<div class="block">Return the localized text for this enum</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html#toYadaPersistentEnum()" class="member-name-link">toYadaPersistentEnum()</a> - Method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html#toYadaPersistentEnum()" class="member-name-link">toYadaPersistentEnum()</a> - Method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html#toYadaPersistentEnum()" class="member-name-link">toYadaPersistentEnum()</a> - Method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#type" class="member-name-link">type</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#type" class="member-name-link">type</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:U">U</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#updateLoginTimestamp(java.lang.String)" class="member-name-link">updateLoginTimestamp(String)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>
<div class="block">Updates the login timestamp of the user</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#updateTimezone(java.lang.String,java.util.TimeZone)" class="member-name-link">updateTimezone(String, TimeZone)</a> - Method in class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html#USER_EXISTS" class="member-name-link">USER_EXISTS</a> - Enum constant in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html#USER_EXISTS" class="member-name-link">USER_EXISTS</a> - Enum constant in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#userCanChangeRole(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.Integer)" class="member-name-link">userCanChangeRole(YadaUserProfile, Integer)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Returns true if the given user can change the role targetRoleId on users, based on its own roles</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#userCanChangeRole(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.String)" class="member-name-link">userCanChangeRole(YadaUserProfile, String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Returns true if the given user can change the role targetRoleId on users, based on its own roles</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#userCanEditUser(net.yadaframework.security.persistence.entity.YadaUserProfile,java.util.List,java.util.List)" class="member-name-link">userCanEditUser(YadaUserProfile, List&lt;Integer&gt;, List&lt;Integer&gt;)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Check if the roles of the actingUser allow it to change some target user based on its roles, as configured by &lt;handles&gt;
 A target user can be changed only when both its current roles and its new roles can all be changed by any of the roles of the acting user.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#userCanImpersonate(net.yadaframework.security.persistence.entity.YadaUserProfile,net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">userCanImpersonate(YadaUserProfile, YadaUserProfile)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>
<div class="block">Check if the roles of the actingUser allow it to impersonate the targetUser based on its roles, as configured by &lt;handles&gt;
 The actingUser can impersonate the targetUser only when there is at least one role of actingUser that can change all roles of targetUser.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#userCredentials" class="member-name-link">userCredentials</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html#userProfile" class="member-name-link">userProfile</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationOutcome</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:V">V</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#validatePasswordSyntax(java.lang.String)" class="member-name-link">validatePasswordSyntax(String)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#validatePasswordSyntax(java.lang.String,int,int)" class="member-name-link">validatePasswordSyntax(String, int, int)</a> - Method in class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - Static method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - Static method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - Static method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - Static method in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - Static method in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html#values()" class="member-name-link">values()</a> - Static method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html#values()" class="member-name-link">values()</a> - Static method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html#values()" class="member-name-link">values()</a> - Static method in enum class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html#values()" class="member-name-link">values()</a> - Static method in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html#values()" class="member-name-link">values()</a> - Static method in enum class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#version" class="member-name-link">version</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#version" class="member-name-link">version</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#version" class="member-name-link">version</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#version" class="member-name-link">version</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:Y">Y</h2>
<dl class="index">
<dt><a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaActionUploadAttrProcessor</a> - Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>
<div class="block">Serve solo se il csrf è attivo.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html#%3Cinit%3E(java.lang.String)" class="member-name-link">YadaActionUploadAttrProcessor(String)</a> - Constructor for class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html" title="class in net.yadaframework.security.web">YadaActionUploadAttrProcessor</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a> - Class in <a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a></dt>
<dd>
<div class="block">Questa classe aggiunge un pò di informazioni in request quando il login fallisce.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#%3Cinit%3E()" class="member-name-link">YadaAuthenticationFailureHandler()</a> - Constructor for class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessFilter</a> - Class in <a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a></dt>
<dd>
<div class="block">Converts the AJAX_LOGGEDIN_PARAM request parameter into a AJAX_LOGGEDIN_HEADER response header
 so that the ajax target, after login, knows it has to close the login modal somehow (with a reload)</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html#%3Cinit%3E()" class="member-name-link">YadaAuthenticationSuccessFilter()</a> - Constructor for class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessFilter</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a> - Class in <a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#%3Cinit%3E()" class="member-name-link">YadaAuthenticationSuccessHandler()</a> - Constructor for class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a> - Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>
<div class="block">Used to create an url to access the site with an automatic login.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html#%3Cinit%3E()" class="member-name-link">YadaAutoLoginToken()</a> - Constructor for class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaAutoLoginTokenDao</a> - Class in <a href="net/yadaframework/security/persistence/repository/package-summary.html">net.yadaframework.security.persistence.repository</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html#%3Cinit%3E()" class="member-name-link">YadaAutoLoginTokenDao()</a> - Constructor for class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html" title="class in net.yadaframework.security.persistence.repository">YadaAutoLoginTokenDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameOutcome.html#%3Cinit%3E()" class="member-name-link">YadaChangeUsernameOutcome()</a> - Constructor for class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameOutcome</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a> - Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html#%3Cinit%3E()" class="member-name-link">YadaCommentMessage()</a> - Constructor for class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaCropDefinition.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaCropDefinition</a> - Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaCropDefinition.html#%3Cinit%3E()" class="member-name-link">YadaCropDefinition()</a> - Constructor for class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaCropDefinition.html" title="class in net.yadaframework.security.web">YadaCropDefinition</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaDialectWithSecurity.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaDialectWithSecurity</a> - Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaDialectWithSecurity.html#%3Cinit%3E(net.yadaframework.core.YadaConfiguration)" class="member-name-link">YadaDialectWithSecurity(YadaConfiguration)</a> - Constructor for class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaDialectWithSecurity.html" title="class in net.yadaframework.security.web">YadaDialectWithSecurity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html" class="type-name-link" title="class in net.yadaframework.security.exceptions">YadaInvalidUserException</a> - Exception Class in <a href="net/yadaframework/security/exceptions/package-summary.html">net.yadaframework.security.exceptions</a></dt>
<dd>
<div class="block">The current session user can't handle the object passed as parameter.</div>
</dd>
<dt><a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html#%3Cinit%3E()" class="member-name-link">YadaInvalidUserException()</a> - Constructor for exception class net.yadaframework.security.exceptions.<a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html" title="class in net.yadaframework.security.exceptions">YadaInvalidUserException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html#%3Cinit%3E(java.lang.String)" class="member-name-link">YadaInvalidUserException(String)</a> - Constructor for exception class net.yadaframework.security.exceptions.<a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html" title="class in net.yadaframework.security.exceptions">YadaInvalidUserException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html#%3Cinit%3E(java.lang.String,java.lang.Object...)" class="member-name-link">YadaInvalidUserException(String, Object...)</a> - Constructor for exception class net.yadaframework.security.exceptions.<a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html" title="class in net.yadaframework.security.exceptions">YadaInvalidUserException</a></dt>
<dd>
<div class="block">Build the message using slf4j log format syntax</div>
</dd>
<dt><a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html#%3Cinit%3E(java.lang.String,java.lang.Throwable)" class="member-name-link">YadaInvalidUserException(String, Throwable)</a> - Constructor for exception class net.yadaframework.security.exceptions.<a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html" title="class in net.yadaframework.security.exceptions">YadaInvalidUserException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html#%3Cinit%3E(java.lang.Throwable)" class="member-name-link">YadaInvalidUserException(Throwable)</a> - Constructor for exception class net.yadaframework.security.exceptions.<a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html" title="class in net.yadaframework.security.exceptions">YadaInvalidUserException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html#%3Cinit%3E(java.lang.Throwable,java.lang.String,java.lang.Object...)" class="member-name-link">YadaInvalidUserException(Throwable, String, Object...)</a> - Constructor for exception class net.yadaframework.security.exceptions.<a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html" title="class in net.yadaframework.security.exceptions">YadaInvalidUserException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaLocalePathRequestCache.html" class="type-name-link" title="class in net.yadaframework.security">YadaLocalePathRequestCache</a> - Class in <a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a></dt>
<dd>
<div class="block">This is needed to store the original request on access failure so that after login the browser is redirected to 
 the url with locale in path.</div>
</dd>
<dt><a href="net/yadaframework/security/YadaLocalePathRequestCache.html#%3Cinit%3E()" class="member-name-link">YadaLocalePathRequestCache()</a> - Constructor for class net.yadaframework.security.<a href="net/yadaframework/security/YadaLocalePathRequestCache.html" title="class in net.yadaframework.security">YadaLocalePathRequestCache</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaLoginController.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaLoginController</a> - Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>
<div class="block">This controller handles the opening of the login form/modal, autologin links, and the result of an ajax login.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaLoginController.html#%3Cinit%3E()" class="member-name-link">YadaLoginController()</a> - Constructor for class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaLoginController.html" title="class in net.yadaframework.security.web">YadaLoginController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaLoginController.html#yadaLoginSuccess(java.lang.String,org.springframework.ui.Model)" class="member-name-link">yadaLoginSuccess(String, Model)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaLoginController.html" title="class in net.yadaframework.security.web">YadaLoginController</a></dt>
<dd>
<div class="block">This method can be set as the default ajax login target in a Security Config, and will redirect to the targetUrl when specified.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaLogoutSuccessHandler.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaLogoutSuccessHandler</a> - Class in <a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a></dt>
<dd>
<div class="block">Handler called during logout</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaLogoutSuccessHandler.html#%3Cinit%3E()" class="member-name-link">YadaLogoutSuccessHandler()</a> - Constructor for class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaLogoutSuccessHandler.html" title="class in net.yadaframework.security.components">YadaLogoutSuccessHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaMiscController.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaMiscController</a> - Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>
<div class="block">Miscellaneous @RequestMapping methods</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaMiscController.html#%3Cinit%3E()" class="member-name-link">YadaMiscController()</a> - Constructor for class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaMiscController.html" title="class in net.yadaframework.security.web">YadaMiscController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html#yadaPasswordResetPost(net.yadaframework.security.persistence.entity.YadaRegistrationRequest,org.springframework.validation.BindingResult,java.util.Locale,org.springframework.web.servlet.mvc.support.RedirectAttributes,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">yadaPasswordResetPost(YadaRegistrationRequest, BindingResult, Locale, RedirectAttributes, HttpServletRequest, HttpServletResponse)</a> - Method in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></dt>
<dd>
<div class="block">Handles the password reset form</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaRegistrationController</a> - Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.html#%3Cinit%3E()" class="member-name-link">YadaRegistrationController()</a> - Constructor for class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" title="class in net.yadaframework.security.web">YadaRegistrationController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameOutcome.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameOutcome</a> - Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html" class="type-name-link" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a> - Enum Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationOutcome&lt;T,<wbr>R&gt;</a> - Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>
<div class="block">The outcome of a registration.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html" class="type-name-link" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a> - Enum Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html#%3Cinit%3E()" class="member-name-link">YadaRegistrationOutcome()</a> - Constructor for class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationOutcome</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html#yadaRegistrationRequest" class="member-name-link">yadaRegistrationRequest</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationOutcome</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a> - Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>
<div class="block">Data to submit during user registration.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#%3Cinit%3E()" class="member-name-link">YadaRegistrationRequest()</a> - Constructor for class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaRegistrationRequestDao</a> - Class in <a href="net/yadaframework/security/persistence/repository/package-summary.html">net.yadaframework.security.persistence.repository</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html#%3Cinit%3E()" class="member-name-link">YadaRegistrationRequestDao()</a> - Constructor for class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html" title="class in net.yadaframework.security.persistence.repository">YadaRegistrationRequestDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityBeans.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaSecurityBeans</a> - Class in <a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityBeans.html#%3Cinit%3E()" class="member-name-link">YadaSecurityBeans()</a> - Constructor for class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityBeans.html" title="class in net.yadaframework.security.components">YadaSecurityBeans</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html" class="type-name-link" title="class in net.yadaframework.security">YadaSecurityConfig</a> - Class in <a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a></dt>
<dd>
<div class="block">Basic security configuration.</div>
</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.html#%3Cinit%3E()" class="member-name-link">YadaSecurityConfig()</a> - Constructor for class net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaSecurityConfig.CustomAuthenticationEntryPoint.html" class="type-name-link" title="class in net.yadaframework.security">YadaSecurityConfig.CustomAuthenticationEntryPoint</a> - Class in <a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a></dt>
<dd>
<div class="block">Needed to redirect to a language-specific login url when a protected page is requested</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityEmailService.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a> - Class in <a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#%3Cinit%3E()" class="member-name-link">YadaSecurityEmailService()</a> - Constructor for class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityEmailService.html" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#yadaSecurityUtil" class="member-name-link">yadaSecurityUtil</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaSecurityUtil</a> - Class in <a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaSecurityUtil.html#%3Cinit%3E()" class="member-name-link">YadaSecurityUtil()</a> - Constructor for class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaSession&lt;T&gt;</a> - Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>
<div class="block">Base class for application session.</div>
</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#%3Cinit%3E()" class="member-name-link">YadaSession()</a> - Constructor for class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a> - Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>
<div class="block">Rappresenta le credenziali generate alla registrazione con un social login</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html#%3Cinit%3E()" class="member-name-link">YadaSocialCredentials()</a> - Constructor for class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaSocialCredentialsDao</a> - Class in <a href="net/yadaframework/security/persistence/repository/package-summary.html">net.yadaframework.security.persistence.repository</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html#%3Cinit%3E()" class="member-name-link">YadaSocialCredentialsDao()</a> - Constructor for class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaSocialCredentialsDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a> - Class in <a href="net/yadaframework/security/web/package-summary.html">net.yadaframework.security.web</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSocialRegistrationData.html#%3Cinit%3E()" class="member-name-link">YadaSocialRegistrationData()</a> - Constructor for class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html#yadaTicket" class="member-name-link">yadaTicket</a> - Variable in class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaTicketMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a> - Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicket.html#%3Cinit%3E()" class="member-name-link">YadaTicket()</a> - Constructor for class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaTicketDao</a> - Class in <a href="net/yadaframework/security/persistence/repository/package-summary.html">net.yadaframework.security.persistence.repository</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html#%3Cinit%3E()" class="member-name-link">YadaTicketDao()</a> - Constructor for class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html" title="class in net.yadaframework.security.persistence.repository">YadaTicketDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaTicketMessage</a> - Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>
<div class="block">A message inside a YadaTicket.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html#%3Cinit%3E()" class="member-name-link">YadaTicketMessage()</a> - Constructor for class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaTicketMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaTicketMessageDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaTicketMessageDao</a> - Class in <a href="net/yadaframework/security/persistence/repository/package-summary.html">net.yadaframework.security.persistence.repository</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaTicketMessageDao.html#%3Cinit%3E()" class="member-name-link">YadaTicketMessageDao()</a> - Constructor for class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaTicketMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaTicketMessageDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" class="type-name-link" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a> - Enum Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>
<div class="block">The localized state of a YadaTicket</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" class="type-name-link" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a> - Enum Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>
<div class="block">The localized type of a YadaTicket.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaTokenHandler.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaTokenHandler</a> - Class in <a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a></dt>
<dd>
<div class="block">Handles autologin links: creation and parsing.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaTokenHandler.html#%3Cinit%3E()" class="member-name-link">YadaTokenHandler()</a> - Constructor for class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaTokenHandler.html" title="class in net.yadaframework.security.components">YadaTokenHandler</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a> - Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#%3Cinit%3E()" class="member-name-link">YadaUserCredentials()</a> - Constructor for class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#yadaUserCredentialsDao" class="member-name-link">yadaUserCredentialsDao</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a> - Class in <a href="net/yadaframework/security/persistence/repository/package-summary.html">net.yadaframework.security.persistence.repository</a></dt>
<dd>
<div class="block">YadaUserCredentials database operations.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#%3Cinit%3E()" class="member-name-link">YadaUserCredentialsDao()</a> - Constructor for class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#yadaUserDetailsService" class="member-name-link">yadaUserDetailsService</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaUserDetailsService</a> - Class in <a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaUserDetailsService.html#%3Cinit%3E()" class="member-name-link">YadaUserDetailsService()</a> - Constructor for class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage&lt;YLE&gt;</a> - Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>
<div class="block">A message sent to some user by another user or by the system.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html#%3Cinit%3E()" class="member-name-link">YadaUserMessage()</a> - Constructor for class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a> - Class in <a href="net/yadaframework/security/persistence/repository/package-summary.html">net.yadaframework.security.persistence.repository</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html#%3Cinit%3E()" class="member-name-link">YadaUserMessageDao()</a> - Constructor for class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" class="type-name-link" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a> - Enum Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>
<div class="block">The localized type of a YadaUserMessage - can be replaced by a different one in the application.</div>
</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a> - Class in <a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html#%3Cinit%3E()" class="member-name-link">YadaUserProfile()</a> - Constructor for class net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#yadaUserProfileDao" class="member-name-link">yadaUserProfileDao</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao&lt;T&gt;</a> - Class in <a href="net/yadaframework/security/persistence/repository/package-summary.html">net.yadaframework.security.persistence.repository</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html#%3Cinit%3E()" class="member-name-link">YadaUserProfileDao()</a> - Constructor for class net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/components/YadaUserSetup.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaUserSetup&lt;T&gt;</a> - Class in <a href="net/yadaframework/security/components/package-summary.html">net.yadaframework.security.components</a></dt>
<dd>
<div class="block">Convenience method to create configured application users.</div>
</dd>
<dt><a href="net/yadaframework/security/components/YadaUserSetup.html#%3Cinit%3E()" class="member-name-link">YadaUserSetup()</a> - Constructor for class net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserSetup.html" title="class in net.yadaframework.security.components">YadaUserSetup</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/web/YadaSession.html#yadaUtil" class="member-name-link">yadaUtil</a> - Variable in class net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" title="class in net.yadaframework.security.web">YadaSession</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWebSecurityConfig.html" class="type-name-link" title="class in net.yadaframework.security">YadaWebSecurityConfig</a> - Class in <a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWebSecurityConfig.html#%3Cinit%3E()" class="member-name-link">YadaWebSecurityConfig()</a> - Constructor for class net.yadaframework.security.<a href="net/yadaframework/security/YadaWebSecurityConfig.html" title="class in net.yadaframework.security">YadaWebSecurityConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html" class="type-name-link" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a> - Class in <a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a></dt>
<dd>
<div class="block">A wrapper for the saved request that allows to add url parameters.</div>
</dd>
<dt><a href="net/yadaframework/security/YadaWrappedSavedRequest.html#%3Cinit%3E(org.springframework.security.web.savedrequest.SavedRequest,net.yadaframework.components.YadaWebUtil)" class="member-name-link">YadaWrappedSavedRequest(SavedRequest, YadaWebUtil)</a> - Constructor for class net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:O">O</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:Y">Y</a>&nbsp;<br><a href="allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="allpackages-index.html">All&nbsp;Packages</a><span class="vertical-separator">|</span><a href="constant-values.html">Constant&nbsp;Field&nbsp;Values</a><span class="vertical-separator">|</span><a href="serialized-form.html">Serialized&nbsp;Form</a></main>
</div>
</div>
</body>
</html>
