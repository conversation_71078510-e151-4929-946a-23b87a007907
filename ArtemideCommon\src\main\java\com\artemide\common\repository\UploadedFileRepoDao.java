package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.babka37.entity.UploadedFile;

@Repository
@Transactional(readOnly = true)
public class UploadedFileRepoDao {

    @PersistenceContext EntityManager em;

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from UploadedFile", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public UploadedFile save(UploadedFile entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<UploadedFile> findById(Long entityId) {
        UploadedFile result = em.find(UploadedFile.class, entityId);
        return Optional.ofNullable(result);
    }

    public UploadedFile findOne(Long entityId) {
        return em.find(UploadedFile.class, entityId);
    }

    public List<UploadedFile> findAll() {
        return em.createQuery("from UploadedFile", UploadedFile.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<UploadedFile> batchToSave) {
        for (UploadedFile entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(UploadedFile entity) {
        em.remove(entity);
    }
}
