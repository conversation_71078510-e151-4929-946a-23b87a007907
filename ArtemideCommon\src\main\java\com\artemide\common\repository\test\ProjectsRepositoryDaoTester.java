package com.artemide.common.repository.test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.Project;
import com.artemide.common.repository.ProjectsRepoDao;
import com.artemide.common.repository.ProjectsRepository;
import com.yr.entity.Subfamily;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies ProjectsRepoDao behaves exactly like ProjectsRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class ProjectsRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private ProjectsRepository projectsRepository;

    @Autowired
    private ProjectsRepoDao projectsRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    Project entity = entityManager.find(Project.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            // Project uses Map for titlePartOne, so we need a different cleanup approach
            entityManager.createQuery("DELETE FROM Project p WHERE p.id IN (SELECT p2.id FROM Project p2 JOIN p2.titlePartOne t WHERE KEY(t) = :locale AND VALUE(t) LIKE 'TEST_%' OR VALUE(t) LIKE 'INTEGRATION_%')")
                .setParameter("locale", java.util.Locale.ENGLISH)
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private Project createTestEntity(String title) {
        Project entity = new Project();
        // Project uses Map<Locale, String> for titlePartOne
        Map<java.util.Locale, String> titleMap = new HashMap<>();
        titleMap.put(java.util.Locale.ENGLISH, title);
        entity.setTitlePartOne(titleMap);
        entity.setEnabled(true);
        return entity;
    }

    private void trackEntity(Project entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = projectsRepository.count();
            long initialDaoCount = projectsRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            Project testEntity = createTestEntity("INTEGRATION_SAVE_001");

            Project repoSaved = projectsRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            Project testEntity2 = createTestEntity("INTEGRATION_SAVE_002");
            Project daoSaved = projectsRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<Project> repoFound = projectsRepository.findById(repoSaved.getId());
            Optional<Project> daoFoundOptional = projectsRepoDao.findById(daoSaved.getId());
            Project daoFoundDirect = projectsRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<Project> repoResults = projectsRepository.findAll();
            List<Project> daoResults = projectsRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            Project entityForRepo = createTestEntity("INTEGRATION_DELETE_001");
            Project entityForDao = createTestEntity("INTEGRATION_DELETE_002");
            
            Project savedForRepo = projectsRepository.save(entityForRepo);
            Project savedForDao = projectsRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!projectsRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!projectsRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            projectsRepository.delete(savedForRepo);
            projectsRepoDao.delete(savedForDao);
            entityManager.flush();

            if (projectsRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (projectsRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindByTagPublished() {
        setUp();
        try {
            // Get a real tag ID from database - try to find any entity that might represent tags
            // Since we don't have a specific ProjectTag entity, we'll use a simple approach
            Optional<Long> tagIdOpt = DbUtil.findFirstId(entityManager, com.artemide.common.persistence.entity.Project.class);
            if (!tagIdOpt.isPresent()) {
                return "SKIP: No Project records found for findByTagPublished test";
            }
            Long testTagId = tagIdOpt.get(); // Use a project ID as tag ID for testing
            
            List<Project> repoResult = projectsRepository.findByTagPublished(testTagId);
            List<Project> daoResult = projectsRepoDao.findByTagPublished(testTagId);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findByTagPublished results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findByTagPublished() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindPublished() {
        setUp();
        try {
            // Test with pageable
            org.springframework.data.domain.Pageable pageable = org.springframework.data.domain.PageRequest.of(0, 5);
            
            org.springframework.data.domain.Slice<Project> repoResult = projectsRepository.findPublished(pageable);
            org.springframework.data.domain.Slice<Project> daoResult = projectsRepoDao.findPublished(pageable);

            if (repoResult.getSize() != daoResult.getSize()) {
                return "FAIL: findPublished results don't match - Repository: " + repoResult.getSize() + ", DAO: " + daoResult.getSize();
            }
            
            return "PASS: findPublished() test successful";
        } finally {
            tearDown();
        }
    }

    public String testSearch() {
        setUp();
        try {
            // Test with search string and locale
            String searchString = "%test%";
            java.util.Locale locale = java.util.Locale.US;
            
            List<Project> repoResult = projectsRepository.search(searchString, locale);
            List<Project> daoResult = projectsRepoDao.search(searchString, locale);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: search results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: search() test successful";
        } finally {
            tearDown();
        }
    }

    @Transactional(readOnly = false)
    public String testSetBigImage() {
        setUp();
        try {
            // Create test entity first
            Project testEntity = createTestEntity("INTEGRATION_IMAGE_001");
            Project saved = projectsRepository.save(testEntity);
            trackEntity(saved);
            entityManager.flush();

            // Test the method
            Long testProjectId = saved.getId();
            // Get a real image ID from database
            Optional<Long> imageIdOpt = DbUtil.findFirstId(entityManager, com.yr.babka37.entity.UploadedFile.class);
            if (!imageIdOpt.isPresent()) {
                return "SKIP: No UploadedFile records found for setBigImage test";
            }
            Long testImageId = imageIdOpt.get();
            
            projectsRepository.setBigImage(testProjectId, testImageId);
            projectsRepoDao.setBigImage(testProjectId, testImageId);
            entityManager.flush();
            
            // Both methods should execute without error
            return "PASS: setBigImage() test successful";
        } finally {
            tearDown();
        }
    }

    @Transactional(readOnly = false)
    public String testSetSmallImage() {
        setUp();
        try {
            // Create test entity first
            Project testEntity = createTestEntity("INTEGRATION_IMAGE_002");
            Project saved = projectsRepository.save(testEntity);
            trackEntity(saved);
            entityManager.flush();

            // Test the method
            Long testProjectId = saved.getId();
            // Get a real image ID from database
            Optional<Long> imageIdOpt = DbUtil.findFirstId(entityManager, com.yr.babka37.entity.UploadedFile.class);
            if (!imageIdOpt.isPresent()) {
                return "SKIP: No UploadedFile records found for setSmallImage test";
            }
            Long testImageId = imageIdOpt.get();
            
            projectsRepository.setSmallImage(testProjectId, testImageId);
            projectsRepoDao.setSmallImage(testProjectId, testImageId);
            entityManager.flush();
            
            // Both methods should execute without error
            return "PASS: setSmallImage() test successful";
        } finally {
            tearDown();
        }
    }

    @Transactional(readOnly = false)
    public String testSetFalseTopProject() {
        setUp();
        try {
            // Create test entities
            Project testEntity1 = createTestEntity("INTEGRATION_TOP_001");
            Project testEntity2 = createTestEntity("INTEGRATION_TOP_002");
            
            Project saved1 = projectsRepository.save(testEntity1);
            Project saved2 = projectsRepository.save(testEntity2);
            trackEntity(saved1);
            trackEntity(saved2);
            entityManager.flush();

            // Test the method - this modifies all records
            projectsRepository.setFalseTopProject();
            projectsRepoDao.setFalseTopProject();
            entityManager.flush();
            
            // Both methods should execute without error
            return "PASS: setFalseTopProject() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAllWithLocalitas() {
        setUp();
        try {
            // Get first 3 project IDs using DbUtil
            List<Long> projectIds = DbUtil.findMultipleIds(entityManager, Project.class, 3);
            if (projectIds.size() < 3) {
                return "SKIP: Need at least 3 projects for testing, found: " + projectIds.size();
            }
            java.util.Collection<Long> testIds = projectIds;
            
            List<Project> repoResult = projectsRepository.findAllWithLocalitas(testIds);
            List<Project> daoResult = projectsRepoDao.findAllWithLocalitas(testIds);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findAllWithLocalitas results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findAllWithLocalitas() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindSottofamiglie() {
        setUp();
        try {
            // Get first available project ID using DbUtil
            java.util.Optional<Long> projectIdOpt = DbUtil.findFirstId(entityManager, Project.class);
            if (!projectIdOpt.isPresent()) {
                return "SKIP: No Project records found in database for testing";
            }
            Long testProjectId = projectIdOpt.get();
            
            List<Subfamily> repoResult = projectsRepository.findSottofamiglie(testProjectId);
            List<Subfamily> daoResult = projectsRepoDao.findSottofamiglie(testProjectId);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findSottofamiglie results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findSottofamiglie() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindWithLocalitas() {
        setUp();
        try {
            // Get first available project ID using DbUtil
            java.util.Optional<Long> projectIdOpt = DbUtil.findFirstId(entityManager, Project.class);
            if (!projectIdOpt.isPresent()) {
                return "SKIP: No Project records found in database for testing";
            }
            Long testProjectId = projectIdOpt.get();
            
            Project repoResult = projectsRepository.findWithLocalitas(testProjectId);
            Project daoResult = projectsRepoDao.findWithLocalitas(testProjectId);

            if ((repoResult == null && daoResult != null) || (repoResult != null && daoResult == null)) {
                return "FAIL: findWithLocalitas results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            if (repoResult != null && daoResult != null && !repoResult.getId().equals(daoResult.getId())) {
                return "FAIL: findWithLocalitas entity IDs don't match - Repository: " + repoResult.getId() + ", DAO: " + daoResult.getId();
            }
            
            return "PASS: findWithLocalitas() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== ProjectsRepository vs ProjectsRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String findByTagPublishedResult = testFindByTagPublished();
            report.append("5. FindByTagPublished Test: ").append(findByTagPublishedResult).append("\n");
            
            String findPublishedResult = testFindPublished();
            report.append("6. FindPublished Test: ").append(findPublishedResult).append("\n");
            
            String searchResult = testSearch();
            report.append("7. Search Test: ").append(searchResult).append("\n");
            
            String setBigImageResult = testSetBigImage();
            report.append("8. SetBigImage Test: ").append(setBigImageResult).append("\n");
            
            String setSmallImageResult = testSetSmallImage();
            report.append("9. SetSmallImage Test: ").append(setSmallImageResult).append("\n");
            
            String setFalseTopProjectResult = testSetFalseTopProject();
            report.append("10. SetFalseTopProject Test: ").append(setFalseTopProjectResult).append("\n");
            
            String findAllWithLocalitasResult = testFindAllWithLocalitas();
            report.append("11. FindAllWithLocalitas Test: ").append(findAllWithLocalitasResult).append("\n");
            
            String findSottofamiglieResult = testFindSottofamiglie();
            report.append("12. FindSottofamiglie Test: ").append(findSottofamiglieResult).append("\n");
            
            String findWithLocalitasResult = testFindWithLocalitas();
            report.append("13. FindWithLocalitas Test: ").append(findWithLocalitasResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - ProjectsRepoDao behaves exactly like ProjectsRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
