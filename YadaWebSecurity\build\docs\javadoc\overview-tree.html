<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Class Hierarchy (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="class tree">
<meta name="generator" content="javadoc/TreeWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="tree-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For All Packages</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="net/yadaframework/security/package-tree.html">net.yadaframework.security</a>, </li>
<li><a href="net/yadaframework/security/components/package-tree.html">net.yadaframework.security.components</a>, </li>
<li><a href="net/yadaframework/security/exceptions/package-tree.html">net.yadaframework.security.exceptions</a>, </li>
<li><a href="net/yadaframework/security/persistence/entity/package-tree.html">net.yadaframework.security.persistence.entity</a>, </li>
<li><a href="net/yadaframework/security/persistence/repository/package-tree.html">net.yadaframework.security.persistence.repository</a>, </li>
<li><a href="net/yadaframework/security/web/package-tree.html">net.yadaframework.security.web</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.springframework.security.web.authentication.AbstractAuthenticationTargetUrlRequestHandler
<ul>
<li class="circle">org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler (implements org.springframework.security.web.authentication.AuthenticationSuccessHandler)
<ul>
<li class="circle">org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler
<ul>
<li class="circle">net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.springframework.security.web.authentication.logout.SimpleUrlLogoutSuccessHandler (implements org.springframework.security.web.authentication.logout.LogoutSuccessHandler)
<ul>
<li class="circle">net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaLogoutSuccessHandler.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaLogoutSuccessHandler</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.thymeleaf.dialect.AbstractDialect (implements org.thymeleaf.dialect.IDialect)
<ul>
<li class="circle">org.thymeleaf.dialect.AbstractProcessorDialect (implements org.thymeleaf.dialect.IProcessorDialect)
<ul>
<li class="circle">net.yadaframework.web.dialect.YadaDialect
<ul>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaDialectWithSecurity.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaDialectWithSecurity</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.thymeleaf.processor.AbstractProcessor (implements org.thymeleaf.processor.IProcessor)
<ul>
<li class="circle">org.thymeleaf.processor.element.AbstractElementTagProcessor (implements org.thymeleaf.processor.element.IElementTagProcessor)
<ul>
<li class="circle">org.thymeleaf.processor.element.AbstractAttributeTagProcessor
<ul>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaActionUploadAttrProcessor</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.springframework.security.web.context.AbstractSecurityWebApplicationInitializer (implements org.springframework.web.WebApplicationInitializer)
<ul>
<li class="circle">net.yadaframework.security.<a href="net/yadaframework/security/SecurityWebApplicationInitializer.html" class="type-name-link" title="class in net.yadaframework.security">SecurityWebApplicationInitializer</a></li>
</ul>
</li>
<li class="circle">org.springframework.web.filter.GenericFilterBean (implements org.springframework.beans.factory.BeanNameAware, org.springframework.beans.factory.DisposableBean, org.springframework.context.EnvironmentAware, org.springframework.core.env.EnvironmentCapable, jakarta.servlet.Filter, org.springframework.beans.factory.InitializingBean, org.springframework.web.context.ServletContextAware)
<ul>
<li class="circle">net.yadaframework.security.<a href="net/yadaframework/security/CheckSessionFilter.html" class="type-name-link" title="class in net.yadaframework.security">CheckSessionFilter</a> (implements jakarta.servlet.Filter)</li>
<li class="circle">org.springframework.web.filter.OncePerRequestFilter
<ul>
<li class="circle">net.yadaframework.security.<a href="net/yadaframework/security/AuditFilter.html" class="type-name-link" title="class in net.yadaframework.security">AuditFilter</a> (implements jakarta.servlet.Filter)</li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.springframework.security.web.savedrequest.HttpSessionRequestCache (implements org.springframework.security.web.savedrequest.RequestCache)
<ul>
<li class="circle">net.yadaframework.security.<a href="net/yadaframework/security/YadaLocalePathRequestCache.html" class="type-name-link" title="class in net.yadaframework.security">YadaLocalePathRequestCache</a></li>
</ul>
</li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Throwable.html" class="type-name-link external-link" title="class or interface in java.lang">Throwable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Exception.html" class="type-name-link external-link" title="class or interface in java.lang">Exception</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" class="type-name-link external-link" title="class or interface in java.lang">RuntimeException</a>
<ul>
<li class="circle">org.springframework.security.core.AuthenticationException
<ul>
<li class="circle">org.springframework.security.core.userdetails.UsernameNotFoundException
<ul>
<li class="circle">net.yadaframework.security.exceptions.<a href="net/yadaframework/security/exceptions/InternalAuthenticationException.html" class="type-name-link" title="class in net.yadaframework.security.exceptions">InternalAuthenticationException</a></li>
<li class="circle">net.yadaframework.security.<a href="net/yadaframework/security/TooManyFailedAttemptsException.html" class="type-name-link" title="class in net.yadaframework.security">TooManyFailedAttemptsException</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">net.yadaframework.security.exceptions.<a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html" class="type-name-link" title="class in net.yadaframework.security.exceptions">YadaInvalidUserException</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a> (implements org.springframework.security.web.authentication.AuthenticationFailureHandler)</li>
<li class="circle">net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessFilter.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessFilter</a> (implements jakarta.servlet.Filter)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaAutoLoginTokenDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaAutoLoginTokenDao</a></li>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaCropDefinition.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaCropDefinition</a></li>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaLoginController.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaLoginController</a></li>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaMiscController.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaMiscController</a></li>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaRegistrationController</a></li>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameOutcome.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameOutcome</a></li>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationOutcome.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationOutcome</a>&lt;T,<wbr>R&gt;</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaRegistrationRequestDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaRegistrationRequestDao</a></li>
<li class="circle">net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityBeans.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaSecurityBeans</a></li>
<li class="circle">net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" class="type-name-link" title="class in net.yadaframework.security">YadaSecurityConfig</a></li>
<li class="circle">net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.CustomAuthenticationEntryPoint.html" class="type-name-link" title="class in net.yadaframework.security">YadaSecurityConfig.CustomAuthenticationEntryPoint</a> (implements org.springframework.security.web.AuthenticationEntryPoint)</li>
<li class="circle">net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityEmailService.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaSecurityEmailService</a></li>
<li class="circle">net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaSecurityUtil.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></li>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSession.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaSession</a>&lt;T&gt;</li>
<li class="circle">net.yadaframework.components.YadaSetup
<ul>
<li class="circle">net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserSetup.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaUserSetup</a>&lt;T&gt;</li>
</ul>
</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaSocialCredentialsDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaSocialCredentialsDao</a></li>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaSocialRegistrationData.html" class="type-name-link" title="class in net.yadaframework.security.web">YadaSocialRegistrationData</a></li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaTicketDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaTicketDao</a></li>
<li class="circle">net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaTicketMessageDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaTicketMessageDao</a></li>
<li class="circle">net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaTokenHandler.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaTokenHandler</a></li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></li>
<li class="circle">net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaUserDetailsService.html" class="type-name-link" title="class in net.yadaframework.security.components">YadaUserDetailsService</a> (implements org.springframework.security.core.userdetails.UserDetailsService)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&lt;YLE&gt; (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaTicketMessage</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
</ul>
</li>
<li class="circle">net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserMessageDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaUserMessageDao</a></li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" class="type-name-link" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)</li>
<li class="circle">net.yadaframework.security.persistence.repository.<a href="net/yadaframework/security/persistence/repository/YadaUserProfileDao.html" class="type-name-link" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a>&lt;T&gt;</li>
<li class="circle">net.yadaframework.core.YadaWebConfig (implements org.springframework.web.servlet.config.annotation.WebMvcConfigurer)
<ul>
<li class="circle">net.yadaframework.security.<a href="net/yadaframework/security/YadaWebSecurityConfig.html" class="type-name-link" title="class in net.yadaframework.security">YadaWebSecurityConfig</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.security.<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" class="type-name-link" title="class in net.yadaframework.security">YadaWrappedSavedRequest</a> (implements org.springframework.security.web.savedrequest.SavedRequest)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="class or interface in java.lang">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/constant/Constable.html" title="class or interface in java.lang.constant" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaChangeUsernameResult.html" class="type-name-link" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaChangeUsernameResult</a></li>
<li class="circle">net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaRegistrationController.YadaRegistrationStatus.html" class="type-name-link" title="enum class in net.yadaframework.security.web">YadaRegistrationController.YadaRegistrationStatus</a></li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" class="type-name-link" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a> (implements net.yadaframework.core.YadaLocalEnum&lt;E&gt;)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaTicketType.html" class="type-name-link" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a> (implements net.yadaframework.core.YadaLocalEnum&lt;E&gt;)</li>
<li class="circle">net.yadaframework.security.persistence.entity.<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" class="type-name-link" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a> (implements net.yadaframework.core.YadaLocalEnum&lt;E&gt;)</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
