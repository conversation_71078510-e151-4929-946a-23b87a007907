<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.security.persistence.entity (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.security.persistence.entity">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li>Related Packages</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li>Related Packages&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package net.yadaframework.security.persistence.entity" class="title">Package net.yadaframework.security.persistence.entity</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">net.yadaframework.security.persistence.entity</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button><button id="class-summary-tab3" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab3', 2)" class="table-tab">Enum Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">YadaAutoLoginToken</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Used to create an url to access the site with an automatic login.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Data to submit during user registration.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Rappresenta le credenziali generate alla registrazione con un social login</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaTicketMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaTicketMessage</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A message inside a YadaTicket.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">The localized state of a YadaTicket</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="YadaTicketType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketType</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">The localized type of a YadaTicket.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&lt;YLE extends net.yadaframework.core.YadaLocalEnum&lt;?&gt;&gt;</div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A message sent to some user by another user or by the system.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab3"><a href="YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a></div>
<div class="col-last even-row-color class-summary class-summary-tab3">
<div class="block">The localized type of a YadaUserMessage - can be replaced by a different one in the application.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
