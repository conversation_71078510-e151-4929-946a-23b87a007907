# Tabelle per il Configuratore
# Emissione associata alla sottofamiglia di una shape

create table ConfiguratorShape_emissions (ConfiguratorShape_id bigint not null, emissions varchar(255), Subfamily_id bigint not null, primary key (ConfiguratorShape_id, Subfamily_id)) engine=InnoDB;

alter table ConfiguratorShape_emissions add constraint FKmjach16r2807xfay54flupme4 foreign key (Subfamily_id) references Subfamily (id);
alter table ConfiguratorShape_emissions add constraint FKi0baik8i58xio18ggiblojnk2 foreign key (ConfiguratorShape_id) references ConfiguratorShape (id);
