<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaSession (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.security.web, class: YadaSession">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.security.web</a></div>
<h1 title="Class YadaSession" class="title">Class YadaSession&lt;T extends <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&gt;</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.security.web.YadaSession&lt;T&gt;</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Component
@SessionScope
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaSession&lt;T extends <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&gt;</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Base class for application session. The subclass must be annotated with "@Primary" otherwise two different instances are created for the two classes</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected net.yadaframework.core.YadaConfiguration</code></div>
<div class="col-second even-row-color"><code><a href="#config" class="member-name-link">config</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected net.yadaframework.web.YadaCropQueue</code></div>
<div class="col-second odd-row-color"><code><a href="#cropQueue" class="member-name-link">cropQueue</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a></code></div>
<div class="col-second even-row-color"><code><a href="#impersonatedUserId" class="member-name-link">impersonatedUserId</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#impersonationStartingLocation" class="member-name-link">impersonationStartingLocation</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a></code></div>
<div class="col-second even-row-color"><code><a href="#impersonatorUserId" class="member-name-link">impersonatorUserId</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a></code></div>
<div class="col-second odd-row-color"><code><a href="#loggedInUserProfileId" class="member-name-link">loggedInUserProfileId</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></code></div>
<div class="col-second even-row-color"><code><a href="#yadaSecurityUtil" class="member-name-link">yadaSecurityUtil</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="../persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></code></div>
<div class="col-second odd-row-color"><code><a href="#yadaUserCredentialsDao" class="member-name-link">yadaUserCredentialsDao</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="../components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></code></div>
<div class="col-second even-row-color"><code><a href="#yadaUserDetailsService" class="member-name-link">yadaUserDetailsService</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="../persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a><wbr>&lt;<a href="YadaSession.html" title="type parameter in YadaSession">T</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#yadaUserProfileDao" class="member-name-link">yadaUserProfileDao</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected net.yadaframework.components.YadaUtil</code></div>
<div class="col-second even-row-color"><code><a href="#yadaUtil" class="member-name-link">yadaUtil</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaSession</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>net.yadaframework.web.YadaCropQueue</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addCropQueue(java.lang.String,java.lang.String)" class="member-name-link">addCropQueue</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cropRedirect,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;destinationRedirect)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Starts a new crop operation deleting any stale images.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clearCaches()" class="member-name-link">clearCaches</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clearUserProfileCache()" class="member-name-link">clearUserProfileCache</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deimpersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">deimpersonate</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Terminates impersonation.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteCropQueue()" class="member-name-link">deleteCropQueue</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#depersonate()" class="member-name-link">depersonate</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use 
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#deimpersonate()</code></pre>
</details>
 instead.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#depersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">depersonate</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use <a href="#deimpersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"><code><span class="invalid-tag">invalid input: 'instead'</span></code></a></div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#depersonify()" class="member-name-link">depersonify</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use 
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#deimpersonate()</code></pre>
</details>
 instead.</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>net.yadaframework.web.YadaCropQueue</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCropQueue()" class="member-name-link">getCropQueue</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the current YadaCropQueue</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaSession.html" title="type parameter in YadaSession">T</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentUserProfile()" class="member-name-link">getCurrentUserProfile</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the currently logged-in user profile or null</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentUserProfileId()" class="member-name-link">getCurrentUserProfileId</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the id of the YadaUserProfile for the currently logged-in user, if any</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLoggedInUserRoleKeys()" class="member-name-link">getLoggedInUserRoleKeys</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLoggedInUserRoles()" class="member-name-link">getLoggedInUserRoles</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasCropQueue()" class="member-name-link">hasCropQueue</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if there are images to be cropped</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#impersonate(java.lang.Long)" class="member-name-link">impersonate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;targetUserProfileId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Assume the identity of the given user</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#impersonate(java.lang.Long,java.lang.String,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">impersonate</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;targetUserProfileId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;currentLocation,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Assume the identity of the given user</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#impersonify(java.lang.Long)" class="member-name-link">impersonify</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;targetUserProfileId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isAdmin()" class="member-name-link">isAdmin</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the current user has the role "ADMIN"</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isCurrentRole(java.lang.String)" class="member-name-link">isCurrentRole</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;roleString)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the current logged in user (if any) has the specified role.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isImpersonationActive()" class="member-name-link">isImpersonationActive</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#isImpersonificationActive()" class="member-name-link">isImpersonificationActive</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isLoggedIn()" class="member-name-link">isLoggedIn</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the current user is authenticated (logged in) not anonymously.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isLoggedInUser(T)" class="member-name-link">isLoggedInUser</a><wbr>(<a href="YadaSession.html" title="type parameter in YadaSession">T</a>&nbsp;someUserProfile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the argument userProfile is the same as the currently logged-in one</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#isLoggedUser(T)" class="member-name-link">isLoggedUser</a><wbr>(<a href="YadaSession.html" title="type parameter in YadaSession">T</a>&nbsp;someUserProfile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use <a href="#isLoggedInUser(T)"><code>isLoggedInUser(YadaUserProfile)</code></a> instead</div>
</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="config">
<h3>config</h3>
<div class="member-signature"><span class="annotations">@Autowired
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">net.yadaframework.core.YadaConfiguration</span>&nbsp;<span class="element-name">config</span></div>
</section>
</li>
<li>
<section class="detail" id="yadaSecurityUtil">
<h3>yadaSecurityUtil</h3>
<div class="member-signature"><span class="annotations">@Autowired
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../components/YadaSecurityUtil.html" title="class in net.yadaframework.security.components">YadaSecurityUtil</a></span>&nbsp;<span class="element-name">yadaSecurityUtil</span></div>
</section>
</li>
<li>
<section class="detail" id="yadaUserProfileDao">
<h3>yadaUserProfileDao</h3>
<div class="member-signature"><span class="annotations">@Autowired
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../persistence/repository/YadaUserProfileDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserProfileDao</a>&lt;<a href="YadaSession.html" title="type parameter in YadaSession">T</a> extends <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&gt;</span>&nbsp;<span class="element-name">yadaUserProfileDao</span></div>
</section>
</li>
<li>
<section class="detail" id="yadaUserDetailsService">
<h3>yadaUserDetailsService</h3>
<div class="member-signature"><span class="annotations">@Autowired
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../components/YadaUserDetailsService.html" title="class in net.yadaframework.security.components">YadaUserDetailsService</a></span>&nbsp;<span class="element-name">yadaUserDetailsService</span></div>
</section>
</li>
<li>
<section class="detail" id="yadaUserCredentialsDao">
<h3>yadaUserCredentialsDao</h3>
<div class="member-signature"><span class="annotations">@Autowired
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../persistence/repository/YadaUserCredentialsDao.html" title="class in net.yadaframework.security.persistence.repository">YadaUserCredentialsDao</a></span>&nbsp;<span class="element-name">yadaUserCredentialsDao</span></div>
</section>
</li>
<li>
<section class="detail" id="yadaUtil">
<h3>yadaUtil</h3>
<div class="member-signature"><span class="annotations">@Autowired
</span><span class="modifiers">protected</span>&nbsp;<span class="return-type">net.yadaframework.components.YadaUtil</span>&nbsp;<span class="element-name">yadaUtil</span></div>
</section>
</li>
<li>
<section class="detail" id="impersonatorUserId">
<h3>impersonatorUserId</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a></span>&nbsp;<span class="element-name">impersonatorUserId</span></div>
</section>
</li>
<li>
<section class="detail" id="impersonatedUserId">
<h3>impersonatedUserId</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a></span>&nbsp;<span class="element-name">impersonatedUserId</span></div>
</section>
</li>
<li>
<section class="detail" id="loggedInUserProfileId">
<h3>loggedInUserProfileId</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a></span>&nbsp;<span class="element-name">loggedInUserProfileId</span></div>
</section>
</li>
<li>
<section class="detail" id="impersonationStartingLocation">
<h3>impersonationStartingLocation</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">impersonationStartingLocation</span></div>
</section>
</li>
<li>
<section class="detail" id="cropQueue">
<h3>cropQueue</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">net.yadaframework.web.YadaCropQueue</span>&nbsp;<span class="element-name">cropQueue</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaSession</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaSession</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="clearUserProfileCache()">
<h3>clearUserProfileCache</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clearUserProfileCache</span>()</div>
</section>
</li>
<li>
<section class="detail" id="clearCaches()">
<h3>clearCaches</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clearCaches</span>()</div>
</section>
</li>
<li>
<section class="detail" id="isImpersonificationActive()">
<h3>isImpersonificationActive</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isImpersonificationActive</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</section>
</li>
<li>
<section class="detail" id="isImpersonationActive()">
<h3>isImpersonationActive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isImpersonationActive</span>()</div>
</section>
</li>
<li>
<section class="detail" id="impersonify(java.lang.Long)">
<h3>impersonify</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">impersonify</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;targetUserProfileId)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Assume the identity of the given user. Deprecated: use impersonate()</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetUserProfileId</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="impersonate(java.lang.Long)">
<h3>impersonate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">impersonate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;targetUserProfileId)</span></div>
<div class="block">Assume the identity of the given user</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetUserProfileId</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="impersonate(java.lang.Long,java.lang.String,jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)">
<h3>impersonate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">impersonate</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;targetUserProfileId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;currentLocation,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span></div>
<div class="block">Assume the identity of the given user</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>targetUserProfileId</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="depersonify()">
<h3>depersonify</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">depersonify</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use 
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#deimpersonate()</code></pre>
</details>
 instead.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="depersonate()">
<h3>depersonate</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">depersonate</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use 
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#deimpersonate()</code></pre>
</details>
 instead.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="depersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)">
<h3>depersonate</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">depersonate</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use <a href="#deimpersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"><code><span class="invalid-tag">invalid input: 'instead'</span></code></a></div>
</div>
</section>
</li>
<li>
<section class="detail" id="deimpersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)">
<h3>deimpersonate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">deimpersonate</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span></div>
<div class="block">Terminates impersonation.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the browser location where impersonation was started, if saved, null otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isCurrentRole(java.lang.String)">
<h3>isCurrentRole</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isCurrentRole</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;roleString)</span></div>
<div class="block">Check if the current logged in user (if any) has the specified role.
 This method must load the current user profile from database when not already
 cached in the user session, so it might be slower than <a href="../components/YadaSecurityUtil.html#hasCurrentRole(java.lang.String)"><code>YadaSecurityUtil.hasCurrentRole(String)</code></a>
 for time-critical use cases.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>roleString</code> - the role name, case insensitive, e.g. "MANAGER" or "admin"</dd>
<dt>Returns:</dt>
<dd>true if there is a logged in user with the specified role name</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isAdmin()">
<h3>isAdmin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isAdmin</span>()</div>
<div class="block">Check if the current user has the role "ADMIN"</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isLoggedUser(T)">
<h3 id="isLoggedUser(net.yadaframework.security.persistence.entity.YadaUserProfile)">isLoggedUser</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isLoggedUser</span><wbr><span class="parameters">(<a href="YadaSession.html" title="type parameter in YadaSession">T</a>&nbsp;someUserProfile)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use <a href="#isLoggedInUser(T)"><code>isLoggedInUser(YadaUserProfile)</code></a> instead</div>
</div>
<div class="block">Check if the argument userProfile is the same as the currently logged-in one</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>someUserProfile</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isLoggedInUser(T)">
<h3 id="isLoggedInUser(net.yadaframework.security.persistence.entity.YadaUserProfile)">isLoggedInUser</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isLoggedInUser</span><wbr><span class="parameters">(<a href="YadaSession.html" title="type parameter in YadaSession">T</a>&nbsp;someUserProfile)</span></div>
<div class="block">Check if the argument userProfile is the same as the currently logged-in one</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>someUserProfile</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isLoggedIn()">
<h3>isLoggedIn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isLoggedIn</span>()</div>
<div class="block">Check if the current user is authenticated (logged in) not anonymously.</div>
</section>
</li>
<li>
<section class="detail" id="getLoggedInUserRoles()">
<h3>getLoggedInUserRoles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</span>&nbsp;<span class="element-name">getLoggedInUserRoles</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Returns the numeric roles that the currently logged in user has, or null when not logged in</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getLoggedInUserRoleKeys()">
<h3>getLoggedInUserRoleKeys</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getLoggedInUserRoleKeys</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>Returns the sorted role names that the currently logged in user has, or null when not logged in</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentUserProfileId()">
<h3>getCurrentUserProfileId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a></span>&nbsp;<span class="element-name">getCurrentUserProfileId</span>()</div>
<div class="block">Returns the id of the YadaUserProfile for the currently logged-in user, if any</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the id or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentUserProfile()">
<h3>getCurrentUserProfile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaSession.html" title="type parameter in YadaSession">T</a></span>&nbsp;<span class="element-name">getCurrentUserProfile</span>()</div>
<div class="block">Returns the currently logged-in user profile or null</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasCropQueue()">
<h3>hasCropQueue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasCropQueue</span>()</div>
<div class="block">Returns true if there are images to be cropped</div>
</section>
</li>
<li>
<section class="detail" id="getCropQueue()">
<h3>getCropQueue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">net.yadaframework.web.YadaCropQueue</span>&nbsp;<span class="element-name">getCropQueue</span>()</div>
<div class="block">Returns the current YadaCropQueue</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the YadaCropQueue or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteCropQueue()">
<h3>deleteCropQueue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">deleteCropQueue</span>()</div>
</section>
</li>
<li>
<section class="detail" id="addCropQueue(java.lang.String,java.lang.String)">
<h3>addCropQueue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">net.yadaframework.web.YadaCropQueue</span>&nbsp;<span class="element-name">addCropQueue</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cropRedirect,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;destinationRedirect)</span></div>
<div class="block">Starts a new crop operation deleting any stale images.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cropRedirect</code> - where to go to perform the crop, e.g. "/some/controller/cropPage"</dd>
<dd><code>destinationRedirect</code> - where to go after all the crop has been done, e.g. "/some/controller/afterCrop"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
