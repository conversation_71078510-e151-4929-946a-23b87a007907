package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.Country;

@Repository
@Transactional(readOnly = true)
public class CountryRepoDao {

    @PersistenceContext EntityManager em;

    public Optional<Country> findByIso3(String iso3) {
        String sql = "from Country where iso3 = :iso3";
        try {
            Country result = em.createQuery(sql, Country.class)
                .setMaxResults(1)
                .setParameter("iso3", iso3)
                .getSingleResult();
            return Optional.ofNullable(result);
        } catch (NonUniqueResultException | NoResultException e) {
            return Optional.empty();
        }
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Country", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Country save(Country entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Country> findById(Long entityId) {
        Country result = em.find(Country.class, entityId);
        return Optional.ofNullable(result);
    }

    public Country findOne(Long entityId) {
        return em.find(Country.class, entityId);
    }

    public List<Country> findAll() {
        return em.createQuery("from Country", Country.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Country> batchToSave) {
        for (Country entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Country entity) {
        em.remove(entity);
    }
}
