package com.artemide.web.manager;

import java.util.Locale;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import com.artemide.common.repository.test.EtichettaRepositoryDaoTester;

import net.yadaframework.components.YadaNotify;

@Controller
@RequestMapping("/manager/test")
public class TestRepoManagerController {
	private final transient Logger log = LoggerFactory.getLogger(getClass());

	@Autowired private YadaNotify yadaNotify;
	@Autowired private EtichettaRepositoryDaoTester etichettaRepositoryDaoTester;

	@RequestMapping("/runRepoRefactoringTest")
	public String runRepoRefactoringTest(Model model, Locale locale) {
		log.info("Starting repository refactoring test");
		
		try {
			String testResult = etichettaRepositoryDaoTester.runAllTests();
			log.info("Repository refactoring test completed:\n{}", testResult);
			
			// Check if all tests passed
			boolean allTestsPassed = !testResult.contains("FAIL") && testResult.contains("ALL TESTS PASSED");
			
			if (allTestsPassed) {
				return yadaNotify.title("Repository Refactoring Test", model)
					.ok()
					.message("✅ All tests passed! EtichettaRepoDao behaves exactly like EtichettaRepository")
					.add();
			} else {
				return yadaNotify.title("Repository Refactoring Test", model)
					.error()
					.message("❌ Some tests failed. Check the logs for detailed results.")
					.add();
			}
			
		} catch (Exception e) {
			log.error("Repository refactoring test failed with exception", e);
			return yadaNotify.title("Repository Refactoring Test", model)
				.error()
				.message("Test failed with exception: " + e.getMessage())
				.add();
		}
	}
}
