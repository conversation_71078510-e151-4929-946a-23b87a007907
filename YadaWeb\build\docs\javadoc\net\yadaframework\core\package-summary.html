<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.core (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.core">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li>Related Packages</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li>Related Packages&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package net.yadaframework.core" class="title">Package net.yadaframework.core</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">net.yadaframework.core</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button><button id="class-summary-tab3" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab3', 2)" class="table-tab">Enum Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="CloneableDeep.html" title="interface in net.yadaframework.core">CloneableDeep</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">Marker interface that tells that if this object is inside a collection,
 it will be recursively cloned before being added to the cloned collection of the parent.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Marker interface to allow an object to be cloned with YadaUtil.copyEntity().</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaAjaxInterceptor.html" title="class in net.yadaframework.core">YadaAjaxInterceptor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaAppConfig.html" title="class in net.yadaframework.core">YadaAppConfig</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaConfiguration.html" title="class in net.yadaframework.core">YadaConfiguration</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Classe che estende CombinedConfiguration aggiungendo metodi di gestione della configurazione specifici.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="YadaConstants.html" title="interface in net.yadaframework.core">YadaConstants</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaDummyDatasource.html" title="class in net.yadaframework.core">YadaDummyDatasource</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaDummyEntityManagerFactory.html" title="class in net.yadaframework.core">YadaDummyEntityManagerFactory</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaDummyJpaConfig.html" title="class in net.yadaframework.core">YadaDummyJpaConfig</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;T&gt;</div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Base class for fluent interface implementations that allows nesting</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaJpaConfig.html" title="class in net.yadaframework.core">YadaJpaConfig</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaLinkBuilder.html" title="class in net.yadaframework.core">YadaLinkBuilder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Called when @{/somePath} is found.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="YadaLocalEnum.html" title="interface in net.yadaframework.core">YadaLocalEnum</a>&lt;E extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Enum.html" title="class or interface in java.lang" class="external-link">Enum</a>&lt;E&gt;&gt;</div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">This interface must be applied to a normal enum that needs to be localized in order to perform sort and search operations
 on the localized text.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaLocalePathChangeInterceptor.html" title="class in net.yadaframework.core">YadaLocalePathChangeInterceptor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Locale in the path
 See https://stackoverflow.com/a/23847484/587641</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaLocalePathLinkBuilder.html" title="class in net.yadaframework.core">YadaLocalePathLinkBuilder</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">Deprecated.</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="YadaRegistrationType.html" title="enum class in net.yadaframework.core">YadaRegistrationType</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">Una YadaRegistrationRequest può essere usata anche per altri scopi oltre alla registrazione.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaTomcatServer.html" title="class in net.yadaframework.core">YadaTomcatServer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Tomcat Embedded.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaWebApplicationInitializer.html" title="class in net.yadaframework.core">YadaWebApplicationInitializer</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Adds configuration from Yada optional projects.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaWebConfig.html" title="class in net.yadaframework.core">YadaWebConfig</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
