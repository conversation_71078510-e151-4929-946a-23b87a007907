<?xml version="1.0" encoding="UTF-8" ?>
<!-- This file contains select-tag options -->
<options>
	<!-- Questi valori vengono memorizzati tali quali nel DB, per cui non serve avere questo file anche nell'e-commerce e nel sito. 
	     Per questa ragione non sono "core" options -->
	<!-- 
		ATTENZIONE:
		se si intende usare la funzione di localizzazione dei valori (quindi gli altri file) è necessario trattare i valori come mappe
		(vedi conf.coreoptions.xml) altrimenti al cambio lingua il valore scompare (se si usano le LocalString) o si sovrascrive (se si usano le String)
	 -->
	<series>
		<value>Design Collection,Architectural Indoor,MyWhiteLight,Hydro,Nord Light,Metamorfosi,INTEGRALIS®,Architectural Outdoor,Modern Classic,Luxerion,A.L.S.O.,Danese</value>
	</series>
	<answers>
		<value>Yes,No</value>
	</answers>
	<insulations>
		<value>I,II,III</value>
	</insulations>
	<homologations>
		<value>EPD,Enec15,IMQ,F-mark,PCT,CCC,CE</value>
	</homologations>
	<lampClass>
		<value>A,B,C,D,E,F,G,H,K,J,I,L,M,N,O,P,Q,R,S,T,U,V,Z,</value>
	</lampClass>
	<!-- Attenzione: queste sono anche nel conf.core.xml ma servono anche qui [xtian] -->
	<lampCategory>
		<value>FLUO,HALO,HQI,HIR</value>
	</lampCategory>
	<dimmerTypology>
		<value>-, Microswitch Dimmer,Microswitch Dimmer su cavo, Push button Dimmer,Push button Dimmer su cavo,Infrared Dimmer,Infrared Dimmer su cavo,Optical Touch Dimmer,Slider Dimmer,Slider Dimmer su cavo,Touch Dimmer,Touch Dimmer su cavo,Push Dimmer - App,Optical Sensor</value>
	</dimmerTypology>
	<dimmableTypology>
		<value>-,APP,Push,1.10V,Dali,taglio di fase,Push &#38; APP,Push&#47;DALI</value>
	</dimmableTypology>
</options>
