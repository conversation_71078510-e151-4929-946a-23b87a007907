<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaNotify (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.components, class: YadaNotify">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.components</a></div>
<h1 title="Class YadaNotify" class="title">Class YadaNotify</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.components.YadaNotify</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Component
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaNotify</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Build a notification dialog to be shown on the response page or after a redirect.
 More than one notification can be saved for the same request: all messages will be shown in one dialog.
 A single instance can be reused after changing its parameters (but not the title). Multiple instances can be used too.
 By setting the title you create a new instance so previous parameters are reset.
 The last method called on the instance must be add() to store the configuration values.
 Example: <br/>
 <pre>
return yadaNotify.title("All good").ok()
        .message("Thank you for your support")
        .autoclose(2000).reloadOnClose().add();
</pre></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaNotify</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clearAll(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes,jakarta.servlet.http.HttpServletRequest)" class="member-name-link">clearAll</a><wbr>(org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes,
 jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Clear all previously added messages.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#empty(org.springframework.ui.Model)" class="member-name-link">empty</a><wbr>(org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise an empty instance</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#empty(org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">empty</a><wbr>(org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise an empty instance</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getViewName()" class="member-name-link">getViewName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the view name for the yada notification modal.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static <a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#instance(org.springframework.ui.Model)" class="member-name-link">instance</a><wbr>(org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use "@Autowired YadaNotify yadaNotify", followed by "yadaNotify.title(model)..."</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static <a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#instance(org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">instance</a><wbr>(org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use "@Autowired YadaNotify yadaNotify", followed by "yadaNotify.title(redirectAttributes)..."</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isErrorSet(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">isErrorSet</a><wbr>(org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return true if error() has been called before</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isNotificationPending(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">isNotificationPending</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Test if a modal is going to be opened when back to the view (usually after a redirect)</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isNotificationPending(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">isNotificationPending</a><wbr>(org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Test if a modal is going to be opened when back to the view</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#isNotifyModalPending(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">isNotifyModalPending</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#isYadaError(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">isYadaError</a><wbr>(org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code>static boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#isYadaNotifySaved(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">isYadaNotifySaved</a><wbr>(org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#redirectOnClose(java.lang.String)" class="member-name-link">redirectOnClose</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#script(java.lang.String)" class="member-name-link">script</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;scriptId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#title(java.lang.String,org.springframework.ui.Model)" class="member-name-link">title</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise the instance with a title</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#title(java.lang.String,org.springframework.ui.Model,java.util.Locale)" class="member-name-link">title</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 org.springframework.ui.Model&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise the instance with a title and a specific locale</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#title(java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">title</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise the instance with a title, to be displayed after a redirect</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#title(java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes,java.util.Locale)" class="member-name-link">title</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise the instance with a title and a specific locale, to be displayed after a redirect</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#titleKey(org.springframework.ui.Model,java.lang.String...)" class="member-name-link">titleKey</a><wbr>(org.springframework.ui.Model&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise the instance with a title formed with a key and its optional parameters for the message.properties in the Request locale</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#titleKey(org.springframework.ui.Model,java.util.Locale,java.lang.String...)" class="member-name-link">titleKey</a><wbr>(org.springframework.ui.Model&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise the instance</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#titleKey(org.springframework.web.servlet.mvc.support.RedirectAttributes,java.lang.String...)" class="member-name-link">titleKey</a><wbr>(org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise the instance</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#titleKey(org.springframework.web.servlet.mvc.support.RedirectAttributes,java.util.Locale,java.lang.String...)" class="member-name-link">titleKey</a><wbr>(org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Initialise the instance</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaAutoclose(long)" class="member-name-link">yadaAutoclose</a><wbr>(long&nbsp;milliseconds)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaError()" class="member-name-link">yadaError</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaInfo()" class="member-name-link">yadaInfo</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaMessage(java.lang.String)" class="member-name-link">yadaMessage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaMessageKey(java.lang.String...)" class="member-name-link">yadaMessageKey</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;messageKeyAndArgs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaMessageSource(org.springframework.context.MessageSource,java.util.Locale)" class="member-name-link">yadaMessageSource</a><wbr>(org.springframework.context.MessageSource&nbsp;messageSource,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaOk()" class="member-name-link">yadaOk</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaReloadOnClose()" class="member-name-link">yadaReloadOnClose</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaSave()" class="member-name-link">yadaSave</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaTitle(java.lang.String)" class="member-name-link">yadaTitle</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#yadaTitleKey(java.lang.String...)" class="member-name-link">yadaTitleKey</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaNotify</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaNotify</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="empty(org.springframework.ui.Model)">
<h3>empty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">empty</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model)</span></div>
<div class="block">Initialise an empty instance</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="empty(org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>empty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">empty</span><wbr><span class="parameters">(org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="block">Initialise an empty instance</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="title(java.lang.String,org.springframework.ui.Model)">
<h3>title</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">title</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 org.springframework.ui.Model&nbsp;model)</span></div>
<div class="block">Initialise the instance with a title</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - the notification title</dd>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="title(java.lang.String,org.springframework.ui.Model,java.util.Locale)">
<h3>title</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">title</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 org.springframework.ui.Model&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</span></div>
<div class="block">Initialise the instance with a title and a specific locale</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - the notification title</dd>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="title(java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>title</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">title</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="block">Initialise the instance with a title, to be displayed after a redirect</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - the notification title</dd>
<dd><code>redirectAttributes</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="title(java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes,java.util.Locale)">
<h3>title</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">title</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</span></div>
<div class="block">Initialise the instance with a title and a specific locale, to be displayed after a redirect</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - the notification title</dd>
<dd><code>redirectAttributes</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="titleKey(org.springframework.ui.Model,java.lang.String...)">
<h3>titleKey</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">titleKey</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</span></div>
<div class="block">Initialise the instance with a title formed with a key and its optional parameters for the message.properties in the Request locale</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - </dd>
<dd><code>titleKeyAndArgs</code> - the title key, followed by optional arguments to be replaced in the localized value</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="titleKey(org.springframework.ui.Model,java.util.Locale,java.lang.String...)">
<h3>titleKey</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">titleKey</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</span></div>
<div class="block">Initialise the instance</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - </dd>
<dd><code>locale</code> - </dd>
<dd><code>titleKeyAndArgs</code> - the title key, followed by optional arguments to be replaced in the localized value</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="titleKey(org.springframework.web.servlet.mvc.support.RedirectAttributes,java.lang.String...)">
<h3>titleKey</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">titleKey</span><wbr><span class="parameters">(org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</span></div>
<div class="block">Initialise the instance</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>titleKeyAndArgs</code> - the title key, followed by optional arguments to be replaced in the localized value</dd>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="titleKey(org.springframework.web.servlet.mvc.support.RedirectAttributes,java.util.Locale,java.lang.String...)">
<h3>titleKey</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></span>&nbsp;<span class="element-name">titleKey</span><wbr><span class="parameters">(org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</span></div>
<div class="block">Initialise the instance</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>titleKeyAndArgs</code> - the title key, followed by optional arguments to be replaced in the localized value</dd>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="clearAll(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes,jakarta.servlet.http.HttpServletRequest)">
<h3>clearAll</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">clearAll</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes,
 jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="block">Clear all previously added messages.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - can be null</dd>
<dd><code>redirectAttributes</code> - can be null</dd>
<dd><code>request</code> - can be null</dd>
<dt>Returns:</dt>
<dd>true if some previous message was found before deleting it</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isNotificationPending(jakarta.servlet.http.HttpServletRequest)">
<h3>isNotificationPending</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isNotificationPending</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="block">Test if a modal is going to be opened when back to the view (usually after a redirect)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dt>Returns:</dt>
<dd>true if a modal is going to be opened</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isNotificationPending(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>isNotificationPending</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isNotificationPending</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="block">Test if a modal is going to be opened when back to the view</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - can be null</dd>
<dd><code>redirectAttributes</code> - can be null if model is not null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isErrorSet(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>isErrorSet</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isErrorSet</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="block">Return true if error() has been called before</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - can be null</dd>
<dd><code>redirectAttributes</code> - can be null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="instance(org.springframework.ui.Model)">
<h3>instance</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">instance</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use "@Autowired YadaNotify yadaNotify", followed by "yadaNotify.title(model)..."</div>
</div>
<div class="block">Create a new YadaNotify instance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="instance(org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>instance</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">instance</span><wbr><span class="parameters">(org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use "@Autowired YadaNotify yadaNotify", followed by "yadaNotify.title(redirectAttributes)..."</div>
</div>
<div class="block">Create a new YadaNotify instance to be shown after a redirect</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>redirectAttributes</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaSave()">
<h3>yadaSave</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">yadaSave</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Makes the notification active. Can be called many times to add different notifications, even on the same instance.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>If used with a Model, returns the view of the notification modal.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaTitle(java.lang.String)">
<h3>yadaTitle</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">yadaTitle</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set the notification title</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaMessage(java.lang.String)">
<h3>yadaMessage</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">yadaMessage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set the notification message. Can be HTML.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaAutoclose(long)">
<h3>yadaAutoclose</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">yadaAutoclose</span><wbr><span class="parameters">(long&nbsp;milliseconds)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set the autoclose time in milliseconds - no close button is rendered</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>milliseconds</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaReloadOnClose()">
<h3>yadaReloadOnClose</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">yadaReloadOnClose</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set the page to reload when the modal is closed</div>
</section>
</li>
<li>
<section class="detail" id="yadaMessageSource(org.springframework.context.MessageSource,java.util.Locale)">
<h3>yadaMessageSource</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">yadaMessageSource</span><wbr><span class="parameters">(org.springframework.context.MessageSource&nbsp;messageSource,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set localization parameters</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>messageSource</code> - </dd>
<dd><code>locale</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaTitleKey(java.lang.String...)">
<h3>yadaTitleKey</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">yadaTitleKey</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;titleKeyAndArgs)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set the notification title using localization</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>titleKeyAndArgs</code> - the title key, followed by optional arguments to be replaced in the localized value</dd>
<dt>Returns:</dt>
<dt>Throws:</dt>
<dd><code><a href="../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - if yadaMessageSource() hasn't been called</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaMessageKey(java.lang.String...)">
<h3>yadaMessageKey</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">yadaMessageKey</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;messageKeyAndArgs)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set the notification message using localization. Can be HTML.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>messageKeyAndArgs</code> - the message key, followed by optional arguments to be replaced in the localized value</dd>
<dt>Returns:</dt>
<dt>Throws:</dt>
<dd><code><a href="../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - if yadaMessageSource() hasn't been called</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="redirectOnClose(java.lang.String)">
<h3>redirectOnClose</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">redirectOnClose</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">The page will redirect on modal close</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>path</code> - the last part of the url after the servlet context, like "/user/profile"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="script(java.lang.String)">
<h3>script</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">script</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;scriptId)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Add a script id to call when opening the notification modal. The script must be inserted into "/script.html"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>scriptId</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaOk()">
<h3>yadaOk</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">yadaOk</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set the notification severity</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaInfo()">
<h3>yadaInfo</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">yadaInfo</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set the notification severity</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="yadaError()">
<h3>yadaError</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></span>&nbsp;<span class="element-name">yadaError</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Set the notification severity</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isNotifyModalPending(jakarta.servlet.http.HttpServletRequest)">
<h3>isNotifyModalPending</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isNotifyModalPending</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Test if a modal is going to be opened when back to the view (usually after a redirect)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dt>Returns:</dt>
<dd>true if a modal is going to be opened</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isYadaError(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>isYadaError</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isYadaError</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Return true if yadaError() has been called before</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - can be null</dd>
<dd><code>redirectAttributes</code> - can be null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isYadaNotifySaved(org.springframework.ui.Model,org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>isYadaNotifySaved</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public static</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isYadaNotifySaved</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Check if the YadaNotify.yadaSave() has been called in this request</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - can be null</dd>
<dd><code>redirectAttributes</code> - can be null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getViewName()">
<h3>getViewName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getViewName</span>()</div>
<div class="block">Return the view name for the yada notification modal. Can be used when adding many messages and the last
 message is not at the method end. Also used in html to get the correct modal.
 Note: this always returns the yada notification modal. Use <code>yadaConfiguration.getNotifyModalView()</code> if
 your application has a custom notification modal configured in conf.webapp.prod.xml</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the view name for the notification modal</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="../core/YadaConfiguration.html#getNotifyModalView()"><code>YadaConfiguration.getNotifyModalView()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
