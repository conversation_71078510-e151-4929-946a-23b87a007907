package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.Etichetta;

@Repository
@Transactional(readOnly = true)
public class EtichettaRepoDao {

    @PersistenceContext EntityManager em;

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Etichetta", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Etichetta save(Etichetta entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Etichetta> findById(Long entityId) {
        Etichetta result = em.find(Etichetta.class, entityId);
        return Optional.ofNullable(result);
    }

    public Etichetta findOne(Long entityId) {
        return em.find(Etichetta.class, entityId);
    }

    public List<Etichetta> findAll() {
        return em.createQuery("from Etichetta", Etichetta.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Etichetta> batchToSave) {
        for (Etichetta entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Etichetta entity) {
        em.remove(entity);
    }
}
