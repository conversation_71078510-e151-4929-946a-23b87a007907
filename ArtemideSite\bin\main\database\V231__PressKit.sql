create table PressKit (id bigint not null auto_increment, size varchar(32), version bigint not null, visibleFrom datetime, visibleTo datetime, kit_id bigint, primary key (id)) engine=InnoDB;
create table PressKit_title (PressKit_id bigint not null, title varchar(255), locale varchar(32) not null, primary key (PressKit_id, locale)) engine=InnoDB;

alter table PressKit add constraint FKppqxvdorio7pyj1428dqetb9t foreign key (kit_id) references YadaAttachedFile (id);
alter table PressKit_title add constraint FKk4i7qq04rsjty0rd22yeakqou foreign key (PressKit_id) references PressKit (id);
