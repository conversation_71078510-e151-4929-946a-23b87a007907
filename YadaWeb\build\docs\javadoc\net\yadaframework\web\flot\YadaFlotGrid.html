<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaFlotGrid (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.flot, class: YadaFlotGrid">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.flot</a></div>
<h1 title="Class YadaFlotGrid" class="title">Class YadaFlotGrid</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.web.flot.YadaFlotGrid</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaFlotGrid</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">The grid is the thing with the axes and a number of ticks. Many of the
 things in the grid are configured under the individual axes, but not
 all.
 <p>
 Using <a href="#setHoverable(java.lang.Boolean)"><code>setHoverable(Boolean)</code></a> or <a href="#setClickable(java.lang.Boolean)"><code>setClickable(Boolean)</code></a>,
 You can use "plotclick" and "plothover" events like this:
 <pre>
   $.plot($("#placeholder"), [ d ], { grid: { clickable: true } });

   $("#placeholder").bind("plotclick", function (event, pos, item) {
     alert("You clicked at " + pos.x + ", " + pos.y);
     // axis coordinates for other axes, if present, are in pos.x2, pos.x3, ...
     // if you need global screen coordinates, they are pos.pageX, pos.pageY

     if (item) {
       highlight(item.series, item.datapoint);
       alert("You clicked a point!");
     }
 });
 </pre>
 The item object in this example is either null or a nearby object on the form:
 <pre>
   item: {
     datapoint: the point, e.g. [0, 2]
     dataIndex: the index of the point in the data array
     series: the series object
     seriesIndex: the index of the series
     pageX, pageY: the global screen coordinates of the point
   }
 </pre>
 For instance, if you have specified the data like this
 <pre>
   $.plot($("#placeholder"), [ { label: "Foo", data: [[0, 10], [7, 3]] } ], ...);
 </pre>
 and the mouse is near the point (7, 3), "datapoint" is [7, 3],
 "dataIndex" will be 1, "series" is a normalized series object with
 among other things the "Foo" label in series.label and the color in
 series.color, and "seriesIndex" is 0. Note that plugins and options
 that transform the data can shift the indexes from what you specified
 in the original data array.
 <p>
 If you use the above events to update some other information and want
 to clear out that info in case the mouse goes away, you'll probably
 also need to listen to "mouseout" events on the placeholder div.
 <p>
 <b>JSON Data format for Grid:</b>
 <pre>
 grid: {
   show: boolean
   aboveData: boolean
   color: color
   backgroundColor: color/gradient or null
   labelMargin: number
   axisMargin: number
   markings: array of markings or (fn: axes -&gt; array of markings)
   borderWidth: number
   borderColor: color or null
   minBorderMargin: number or null
   clickable: boolean
   hoverable: boolean
   autoHighlight: boolean
   mouseActiveRadius: number
 }
 </pre>
 This class has been constructed as per flot API documentation.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><A href="http://flot.googlecode.com/svn/trunk/API.txt" target="_blank">Flot API.txt</A></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaFlotGrid</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAboveData(java.lang.Boolean)" class="member-name-link">setAboveData</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;aboveData)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"aboveData" determines whether the grid is drawn
 above the data or below (below is default).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAutoHighlight(java.lang.Boolean)" class="member-name-link">setAutoHighlight</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;autoHighlight)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If "autoHighlight" is true (the default), nearby data items are
 highlighted automatically.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setAxisMargin(java.lang.Integer)" class="member-name-link">setAxisMargin</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;axisMargin)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"axisMargin" is the space in pixels between axes when there are two next
 to each other.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBackgroundColor(java.lang.String)" class="member-name-link">setBackgroundColor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;backgroundColor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"backgroundColor" specifies the background colour inside the grid area,
 here null means that the background is transparent.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBorderColor(java.lang.String)" class="member-name-link">setBorderColor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;borderColor)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">You can also set "borderColor" if you want the
 border to have a different color than the grid lines.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setBorderWidth(java.lang.Integer)" class="member-name-link">setBorderWidth</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;borderWidth)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"borderWidth" is the width of the border around the plot.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setClickable(java.lang.Boolean)" class="member-name-link">setClickable</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;clickable)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If you set "clickable" to true, the plot will listen for click events
 on the plot area and fire a "plotclick" event on the placeholder with
 a position and a nearby data item object as parameters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setColor(java.lang.String)" class="member-name-link">setColor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;color)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"color" is the colour of the grid itself.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setHoverable(java.lang.Boolean)" class="member-name-link">setHoverable</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;hoverable)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Likewise, if you set "hoverable" to true, the plot will listen for
 mouse move events on the plot area and fire a "plothover" event with
 the same parameters as the "plotclick" event.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLabelMargin(java.lang.Integer)" class="member-name-link">setLabelMargin</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;labelMargin)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"labelMargin" is the space in pixels between tick labels and axis line.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMarkings(java.lang.String)" class="member-name-link">setMarkings</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;markings)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"markings" is used to draw simple lines and rectangular areas in the
 background of the plot.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMinBorderMargin(java.lang.Integer)" class="member-name-link">setMinBorderMargin</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;minBorderMargin)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"minBorderMargin" controls the default minimum margin around the
 border - it's used to make sure that points aren't accidentally
 clipped by the canvas edge so by default the value is computed from
 the point radius.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setMouseActiveRadius(java.lang.Integer)" class="member-name-link">setMouseActiveRadius</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mouseActiveRadius)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">"mouseActiveRadius" specifies how far the mouse can be from an item
 and still activate it.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setShow(java.lang.Boolean)" class="member-name-link">setShow</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;show)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">You can turn off the whole grid including tick labels by setting
 "show" to false.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaFlotGrid</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaFlotGrid</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setShow(java.lang.Boolean)">
<h3>setShow</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setShow</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;show)</span></div>
<div class="block">You can turn off the whole grid including tick labels by setting
 "show" to false.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>show</code> - boolean</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAboveData(java.lang.Boolean)">
<h3>setAboveData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAboveData</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;aboveData)</span></div>
<div class="block">"aboveData" determines whether the grid is drawn
 above the data or below (below is default).
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>aboveData</code> - boolean</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setColor(java.lang.String)">
<h3>setColor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setColor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;color)</span></div>
<div class="block">"color" is the colour of the grid itself.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>color</code> - colour</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBackgroundColor(java.lang.String)">
<h3>setBackgroundColor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBackgroundColor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;backgroundColor)</span></div>
<div class="block">"backgroundColor" specifies the background colour inside the grid area,
 here null means that the background is transparent.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>backgroundColor</code> - colour/gradient or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLabelMargin(java.lang.Integer)">
<h3>setLabelMargin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLabelMargin</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;labelMargin)</span></div>
<div class="block">"labelMargin" is the space in pixels between tick labels and axis line.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>labelMargin</code> - number</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAxisMargin(java.lang.Integer)">
<h3>setAxisMargin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAxisMargin</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;axisMargin)</span></div>
<div class="block">"axisMargin" is the space in pixels between axes when there are two next
 to each other.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>axisMargin</code> - number</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMarkings(java.lang.String)">
<h3>setMarkings</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMarkings</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;markings)</span></div>
<div class="block">"markings" is used to draw simple lines and rectangular areas in the
 background of the plot. You can either specify an array of ranges on
 the form { xaxis: { from, to }, yaxis: { from, to } } (with multiple
 axes, you can specify coordinates for other axes instead, e.g. as
 x2axis/x3axis/...) or with a function that returns such an array given
 the axes for the plot in an object as the first parameter.
 <p>
 You can set the color of markings by specifying "color" in the ranges
 object. Here's an example array:
 <pre>
   markings: [ { xaxis: { from: 0, to: 2 }, yaxis: { from: 10, to: 10 }, color: "#bb0000" }, ... ]
 </pre>
 If you leave out one of the values, that value is assumed to go to the
 border of the plot. So for example if you only specify { xaxis: {
 from: 0, to: 2 } } it means an area that extends from the top to the
 bottom of the plot in the x range 0-2.
 <p>
 A line is drawn if from and to are the same, e.g.
 <pre>
   markings: [ { yaxis: { from: 1, to: 1 } }, ... ]
 </pre>
 would draw a line parallel to the x axis at y = 1. You can control the
 line width with "lineWidth" in the range object.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>markings</code> - array of markings or (fn: axes -&gt; array of markings)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBorderWidth(java.lang.Integer)">
<h3>setBorderWidth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBorderWidth</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;borderWidth)</span></div>
<div class="block">"borderWidth" is the width of the border around the plot. Set it to 0
 to disable the border.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>borderWidth</code> - number</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setBorderColor(java.lang.String)">
<h3>setBorderColor</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setBorderColor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;borderColor)</span></div>
<div class="block">You can also set "borderColor" if you want the
 border to have a different color than the grid lines.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>borderColor</code> - color or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMinBorderMargin(java.lang.Integer)">
<h3>setMinBorderMargin</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMinBorderMargin</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;minBorderMargin)</span></div>
<div class="block">"minBorderMargin" controls the default minimum margin around the
 border - it's used to make sure that points aren't accidentally
 clipped by the canvas edge so by default the value is computed from
 the point radius.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>minBorderMargin</code> - number or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setClickable(java.lang.Boolean)">
<h3>setClickable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setClickable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;clickable)</span></div>
<div class="block">If you set "clickable" to true, the plot will listen for click events
 on the plot area and fire a "plotclick" event on the placeholder with
 a position and a nearby data item object as parameters. The coordinates
 are available both in the unit of the axes (not in pixels) and in
 global screen coordinates.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>clickable</code> - boolean</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setHoverable(java.lang.Boolean)">
<h3>setHoverable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setHoverable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;hoverable)</span></div>
<div class="block">Likewise, if you set "hoverable" to true, the plot will listen for
 mouse move events on the plot area and fire a "plothover" event with
 the same parameters as the "plotclick" event. If "autoHighlight" is
 true (the default), nearby data items are highlighted automatically.
 If needed, you can disable highlighting and control it yourself with
 the highlight/unhighlight plot methods described elsewhere.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>hoverable</code> - boolean</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setAutoHighlight(java.lang.Boolean)">
<h3>setAutoHighlight</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setAutoHighlight</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;autoHighlight)</span></div>
<div class="block">If "autoHighlight" is true (the default), nearby data items are
 highlighted automatically. If needed, you can disable highlighting
 and control it yourself with the highlight/unhighlight plot methods
 described elsewhere.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>autoHighlight</code> - boolean</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setMouseActiveRadius(java.lang.Integer)">
<h3>setMouseActiveRadius</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setMouseActiveRadius</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mouseActiveRadius)</span></div>
<div class="block">"mouseActiveRadius" specifies how far the mouse can be from an item
 and still activate it. If there are two or more points within this
 radius, Flot chooses the closest item. For bars, the top-most bar
 (from the latest specified data series) is chosen.
 <p></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>mouseActiveRadius</code> - number</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
