<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.web.dialect Class Hierarchy (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="tree: package: net.yadaframework.web.dialect">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#tree">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package net.yadaframework.web.dialect</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">org.thymeleaf.dialect.AbstractDialect (implements org.thymeleaf.dialect.IDialect)
<ul>
<li class="circle">org.thymeleaf.dialect.AbstractProcessorDialect (implements org.thymeleaf.dialect.IProcessorDialect)
<ul>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaDialect.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaDialect</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">org.thymeleaf.processor.AbstractProcessor (implements org.thymeleaf.processor.IProcessor)
<ul>
<li class="circle">org.thymeleaf.processor.element.AbstractElementModelProcessor (implements org.thymeleaf.processor.element.IElementModelProcessor)
<ul>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaDataTableTagProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaDataTableTagProcessor</a></li>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaInputCounterTagProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaInputCounterTagProcessor</a></li>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaInputTagProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaInputTagProcessor</a></li>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaTextareaTagProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaTextareaTagProcessor</a></li>
</ul>
</li>
<li class="circle">org.thymeleaf.processor.element.AbstractElementTagProcessor (implements org.thymeleaf.processor.element.IElementTagProcessor)
<ul>
<li class="circle">org.thymeleaf.processor.element.AbstractAttributeTagProcessor
<ul>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaAjaxAttrProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaAjaxAttrProcessor</a>
<ul>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaHrefAttrProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaHrefAttrProcessor</a></li>
</ul>
</li>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaBrOnFirstSpaceAttrProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaBrOnFirstSpaceAttrProcessor</a></li>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaNewlineTextAttrProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaNewlineTextAttrProcessor</a></li>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaSimpleAttrProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaSimpleAttrProcessor</a></li>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaSrcAttrProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaSrcAttrProcessor</a></li>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaSrcsetAttrProcessor.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaSrcsetAttrProcessor</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaDialectUtil.html" class="type-name-link" title="class in net.yadaframework.web.dialect">YadaDialectUtil</a></li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">net.yadaframework.web.dialect.<a href="YadaInputTagSuggestion.html" class="type-name-link" title="interface in net.yadaframework.web.dialect">YadaInputTagSuggestion</a></li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
