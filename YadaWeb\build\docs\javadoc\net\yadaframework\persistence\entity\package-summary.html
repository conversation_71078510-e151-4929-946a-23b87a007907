<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.persistence.entity (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.persistence.entity">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package net.yadaframework.persistence.entity" class="title">Package net.yadaframework.persistence.entity</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">net.yadaframework.persistence.entity</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">net.yadaframework.persistence</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../repository/package-summary.html">net.yadaframework.persistence.repository</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button><button id="class-summary-tab3" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab3', 2)" class="table-tab">Enum Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A "pointer" to a file that has been copied into the "contents" folder.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="YadaAttachedFile.YadaAttachedFileType.html" title="enum class in net.yadaframework.persistence.entity">YadaAttachedFile.YadaAttachedFileType</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaBrowserId.html" title="class in net.yadaframework.persistence.entity">YadaBrowserId</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This class uniquely identifies the user browser.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaClause.html" title="class in net.yadaframework.persistence.entity">YadaClause</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">The base class for jobs handled by the YadaScheduler.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">The localized state of a YadaJob:
 
 ACTIVE: the job is waiting to be run
 RUNNING: the job is running
 PAUSED: scheduling on this job has been paused (by the user) and the job should not run
 COMPLETED: the job has completed its purpose and should not run again
 DISABLED: the job has been disabled because of errors
 </div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A "pointer" to a file that has been uploaded to the "uploads" folder.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;E extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Enum.html" title="class or interface in java.lang" class="external-link">Enum</a>&lt;E&gt;&gt;</div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Needed to store a localized enum in the database, on which to perform localized search and sort operations.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaRateLog.html" title="class in net.yadaframework.persistence.entity">YadaRateLog</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Keeps a timestamped log of events for rate-limiting purposes
 NOT USED YET!!! Aggiungere a persistence.xml poi
 TODO vedi quanto fatto per FeccMe_Proxy</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
