# Modifiche tabelle per gestire meglio i moduli del Journal 
# - Cancella la vecchia versione dove NewsModule non estendeva PageModule
# - Rinomina Tag per usarlo solo con il journal
# - Crea PageModule che diventa comune tra journal e progetti

Drop table NewsModule_Subfamily, NewsModule_textOne, NewsModule_textTwo, NewsModule_YadaAttachedFile, Tag_name, Tag_NewsJournal, Tag, NewsModule;

create table NewsTag (id bigint not null auto_increment, pos bigint not null, version bigint not null, primary key (id)) engine=InnoDB;
create table NewsTag_name (NewsTag_id bigint not null, name varchar(255), locale varchar(32) not null, primary key (NewsTag_id, locale)) engine=InnoDB;
create table NewsTag_NewsJournal (tags_id bigint not null, news_id bigint not null) engine=InnoDB;

create table PageModule (DTYPE varchar(31) not null, id bigint not null auto_increment, pos bigint, swap bit not null, type integer not null, version bigint not null, video varchar(1024), image_id bigint, pdfFile_id bigint, news_id bigint, primary key (id)) engine=InnoDB;
create table PageModule_Subfamily (PageModule_id bigint not null, subfamilies_id bigint not null) engine=InnoDB;
create table PageModule_textOne (PageModule_id bigint not null, textOne varchar(8192), locale varchar(32) not null, primary key (PageModule_id, locale)) engine=InnoDB;
create table PageModule_textTwo (PageModule_id bigint not null, textTwo varchar(8192), locale varchar(32) not null, primary key (PageModule_id, locale)) engine=InnoDB;
create table PageModule_YadaAttachedFile (PageModule_id bigint not null, carrouselImages_id bigint not null) engine=InnoDB;

alter table NewsTag_name add constraint UKmsm7jwkic0x5fxnnhalmki48r unique (name, locale);
alter table NewsTag_NewsJournal add constraint UKd65pxw3kpl2xelkltjijjgn76 unique (tags_id, news_id);
alter table PageModule_YadaAttachedFile add constraint UK_b2jxditsyu2mr1krxu2mffeml unique (carrouselImages_id);

alter table NewsTag_name add constraint FKhit21d73m1q0owpje0vyn3sd2 foreign key (NewsTag_id) references NewsTag (id);
alter table NewsTag_NewsJournal add constraint FKr1jxf95eqr51by36o45e9xvb0 foreign key (news_id) references NewsJournal (id);
alter table NewsTag_NewsJournal add constraint FKxy9wfd5a7itvsmnpq020wy4n foreign key (tags_id) references NewsTag (id);
