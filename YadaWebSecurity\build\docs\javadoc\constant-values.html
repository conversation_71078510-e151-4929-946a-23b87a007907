<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Constant Field Values (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="summary of constants">
<meta name="generator" content="javadoc/ConstantsSummaryWriterImpl">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="constants-summary-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#constant-values">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Constant Field Values" class="title">Constant Field Values</h1>
</div>
<section class="packages">
<h2 title="Contents">Contents</h2>
<ul class="contents-list">
<li><a href="#net.yadaframework">net.yadaframework.*</a></li>
</ul>
</section>
<section class="constants-summary" id="net.yadaframework">
<h2 title="net.yadaframework.*">net.yadaframework.*</h2>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.security.<a href="net/yadaframework/security/YadaSecurityConfig.html" title="class in net.yadaframework.security">YadaSecurityConfig</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.security.YadaSecurityConfig.DEFAULT_LOGIN_POST">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/security/YadaSecurityConfig.html#DEFAULT_LOGIN_POST">DEFAULT_LOGIN_POST</a></code></div>
<div class="col-last even-row-color"><code>"/loginPost"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.security.YadaSecurityConfig.DEFAULT_LOGIN_URL">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/security/YadaSecurityConfig.html#DEFAULT_LOGIN_URL">DEFAULT_LOGIN_URL</a></code></div>
<div class="col-last odd-row-color"><code>"/login"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.security.YadaSecurityConfig.DEFAULT_LOGIN_URL_AJAX">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/security/YadaSecurityConfig.html#DEFAULT_LOGIN_URL_AJAX">DEFAULT_LOGIN_URL_AJAX</a></code></div>
<div class="col-last even-row-color"><code>"/ajaxLogin"</code></div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationFailureHandler</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationFailureHandler.REQUESTATTR_CREDENTIALSEXPIREDFLAG">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_CREDENTIALSEXPIREDFLAG">REQUESTATTR_CREDENTIALSEXPIREDFLAG</a></code></div>
<div class="col-last even-row-color"><code>"credentialsExpiredException"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationFailureHandler.REQUESTATTR_GENERICERRORFLAG">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_GENERICERRORFLAG">REQUESTATTR_GENERICERRORFLAG</a></code></div>
<div class="col-last odd-row-color"><code>"loginError"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationFailureHandler.REQUESTATTR_LOCKOUTMINUTES">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_LOCKOUTMINUTES">REQUESTATTR_LOCKOUTMINUTES</a></code></div>
<div class="col-last even-row-color"><code>"lockoutMinutes"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationFailureHandler.REQUESTATTR_LOGINERRORFLAG">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_LOGINERRORFLAG">REQUESTATTR_LOGINERRORFLAG</a></code></div>
<div class="col-last odd-row-color"><code>"loginErrorFlag"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationFailureHandler.REQUESTATTR_PASSWORD">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_PASSWORD">REQUESTATTR_PASSWORD</a></code></div>
<div class="col-last even-row-color"><code>"password"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationFailureHandler.REQUESTATTR_PASSWORDERRORFLAG">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_PASSWORDERRORFLAG">REQUESTATTR_PASSWORDERRORFLAG</a></code></div>
<div class="col-last odd-row-color"><code>"passwordError"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationFailureHandler.REQUESTATTR_USERDISABLEDFLAG">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_USERDISABLEDFLAG">REQUESTATTR_USERDISABLEDFLAG</a></code></div>
<div class="col-last even-row-color"><code>"userDisabled"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationFailureHandler.REQUESTATTR_USERNAME">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_USERNAME">REQUESTATTR_USERNAME</a></code></div>
<div class="col-last odd-row-color"><code>"username"</code></div>
<div class="col-first even-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationFailureHandler.REQUESTATTR_USERNAMENOTFOUNDFLAG">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationFailureHandler.html#REQUESTATTR_USERNAMENOTFOUNDFLAG">REQUESTATTR_USERNAMENOTFOUNDFLAG</a></code></div>
<div class="col-last even-row-color"><code>"usernameNotFound"</code></div>
</div>
</li>
<li>
<div class="caption"><span>net.yadaframework.security.components.<a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html" title="class in net.yadaframework.security.components">YadaAuthenticationSuccessHandler</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationSuccessHandler.AJAX_LOGGEDIN_HEADER">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#AJAX_LOGGEDIN_HEADER">AJAX_LOGGEDIN_HEADER</a></code></div>
<div class="col-last even-row-color"><code>"Yada-Ajax-Just-LoggedIn"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.security.components.YadaAuthenticationSuccessHandler.AJAX_LOGGEDIN_PARAM">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/security/components/YadaAuthenticationSuccessHandler.html#AJAX_LOGGEDIN_PARAM">AJAX_LOGGEDIN_PARAM</a></code></div>
<div class="col-last odd-row-color"><code>"yadaAjaxJustLoggedIn"</code></div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div class="caption"><span>net.yadaframework.security.web.<a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html" title="class in net.yadaframework.security.web">YadaActionUploadAttrProcessor</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="net.yadaframework.security.web.YadaActionUploadAttrProcessor.ATTR_NAME">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html#ATTR_NAME">ATTR_NAME</a></code></div>
<div class="col-last even-row-color"><code>"actionUpload"</code></div>
<div class="col-first odd-row-color"><code id="net.yadaframework.security.web.YadaActionUploadAttrProcessor.ATTR_PRECEDENCE">public&nbsp;static&nbsp;final&nbsp;int</code></div>
<div class="col-second odd-row-color"><code><a href="net/yadaframework/security/web/YadaActionUploadAttrProcessor.html#ATTR_PRECEDENCE">ATTR_PRECEDENCE</a></code></div>
<div class="col-last odd-row-color"><code>10000</code></div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
