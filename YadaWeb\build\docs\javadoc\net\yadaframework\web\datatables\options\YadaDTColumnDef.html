<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaDTColumnDef (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.datatables.options, class: YadaDTColumnDef">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.datatables.options</a></div>
<h1 title="Class YadaDTColumnDef" class="title">Class YadaDTColumnDef</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">net.yadaframework.core.YadaFluentBase</a>&lt;<a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>&gt;
<div class="inheritance"><a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">net.yadaframework.web.datatables.options.YadaDTColumns</a>
<div class="inheritance">net.yadaframework.web.datatables.options.YadaDTColumnDef</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../proxy/YadaDTColumnDefProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTColumnDefProxy</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaDTColumnDef</span>
<span class="extends-implements">extends <a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span></div>
<div class="block">Represents column definition configuration for DataTables.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/columnDefs">ColumnDefs</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color"><code><a href="#targets" class="member-name-link">targets</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.web.datatables.options.YadaDTColumns">Fields inherited from class&nbsp;net.yadaframework.web.datatables.options.<a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></h3>
<code><a href="YadaDTColumns.html#ariaTitle">ariaTitle</a>, <a href="YadaDTColumns.html#cellType">cellType</a>, <a href="YadaDTColumns.html#className">className</a>, <a href="YadaDTColumns.html#contentPadding">contentPadding</a>, <a href="YadaDTColumns.html#createdCell">createdCell</a>, <a href="YadaDTColumns.html#data">data</a>, <a href="YadaDTColumns.html#dataFunction">dataFunction</a>, <a href="YadaDTColumns.html#defaultContent">defaultContent</a>, <a href="YadaDTColumns.html#footer">footer</a>, <a href="YadaDTColumns.html#name">name</a>, <a href="YadaDTColumns.html#orderable">orderable</a>, <a href="YadaDTColumns.html#orderData">orderData</a>, <a href="YadaDTColumns.html#orderDataType">orderDataType</a>, <a href="YadaDTColumns.html#orderSequence">orderSequence</a>, <a href="YadaDTColumns.html#render">render</a>, <a href="YadaDTColumns.html#responsivePriority">responsivePriority</a>, <a href="YadaDTColumns.html#searchable">searchable</a>, <a href="YadaDTColumns.html#title">title</a>, <a href="YadaDTColumns.html#visible">visible</a>, <a href="YadaDTColumns.html#width">width</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.core.YadaFluentBase">Fields inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#parent">parent</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(net.yadaframework.web.datatables.options.YadaDTOptions)" class="member-name-link">YadaDTColumnDef</a><wbr>(<a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>&nbsp;parent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTargets(int...)" class="member-name-link">dtTargets</a><wbr>(int...&nbsp;columnIndex)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the target for the column definition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTargetsAll()" class="member-name-link">dtTargetsAll</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Target all columns.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTargetsCss(java.lang.String)" class="member-name-link">dtTargetsCss</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the target for the column definition.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTargetsName(java.lang.String)" class="member-name-link">dtTargetsName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;columnName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the target for the column definition.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-net.yadaframework.web.datatables.options.YadaDTColumns">Methods inherited from class&nbsp;net.yadaframework.web.datatables.options.<a href="YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></h3>
<code><a href="YadaDTColumns.html#dtAriaTitle(java.lang.String)">dtAriaTitle</a>, <a href="YadaDTColumns.html#dtCellType(java.lang.String)">dtCellType</a>, <a href="YadaDTColumns.html#dtClassName(java.lang.String)">dtClassName</a>, <a href="YadaDTColumns.html#dtContentPadding(java.lang.String)">dtContentPadding</a>, <a href="YadaDTColumns.html#dtCreatedCell(java.lang.String)">dtCreatedCell</a>, <a href="YadaDTColumns.html#dtData(java.lang.Integer)">dtData</a>, <a href="YadaDTColumns.html#dtData(java.lang.String)">dtData</a>, <a href="YadaDTColumns.html#dtDataFunction(java.lang.String)">dtDataFunction</a>, <a href="YadaDTColumns.html#dtDataNull()">dtDataNull</a>, <a href="YadaDTColumns.html#dtDefaultContent(java.lang.String)">dtDefaultContent</a>, <a href="YadaDTColumns.html#dtFooter(java.lang.String)">dtFooter</a>, <a href="YadaDTColumns.html#dtName(java.lang.String)">dtName</a>, <a href="YadaDTColumns.html#dtOrderableOff()">dtOrderableOff</a>, <a href="YadaDTColumns.html#dtOrderData(java.lang.Integer)">dtOrderData</a>, <a href="YadaDTColumns.html#dtOrderDataType(java.lang.String)">dtOrderDataType</a>, <a href="YadaDTColumns.html#dtOrderSequence(java.lang.String)">dtOrderSequence</a>, <a href="YadaDTColumns.html#dtRender(java.lang.String)">dtRender</a>, <a href="YadaDTColumns.html#dtResponsivePriority(int)">dtResponsivePriority</a>, <a href="YadaDTColumns.html#dtSearchableOff()">dtSearchableOff</a>, <a href="YadaDTColumns.html#dtTitle(java.lang.String)">dtTitle</a>, <a href="YadaDTColumns.html#dtVisibleOff()">dtVisibleOff</a>, <a href="YadaDTColumns.html#dtWidth(java.lang.String)">dtWidth</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-net.yadaframework.core.YadaFluentBase">Methods inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#back()">back</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="targets">
<h3>targets</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">targets</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(net.yadaframework.web.datatables.options.YadaDTOptions)">
<h3>YadaDTColumnDef</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">YadaDTColumnDef</span><wbr><span class="parameters">(<a href="YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a>&nbsp;parent)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="dtTargets(int...)">
<h3>dtTargets</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></span>&nbsp;<span class="element-name">dtTargets</span><wbr><span class="parameters">(int...&nbsp;columnIndex)</span></div>
<div class="block">Set the target for the column definition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>columnIndex</code> - the target column or an array of target columns:
               <ul>
               <li>0 or a positive integer - column index counting from the left</li>
                   <li>a negative integer - column index counting from the right</li>
               </ul></dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/columnDefs.targets">targets</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtTargetsCss(java.lang.String)">
<h3>dtTargetsCss</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></span>&nbsp;<span class="element-name">dtTargetsCss</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector)</span></div>
<div class="block">Set the target for the column definition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cssSelector</code> - a CSS selector - columns that match the selector will be used</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/columnDefs.targets">targets</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtTargetsName(java.lang.String)">
<h3>dtTargetsName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></span>&nbsp;<span class="element-name">dtTargetsName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;columnName)</span></div>
<div class="block">Set the target for the column definition.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>columnName</code> - the name of the column, without ":name" at the end</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/columnDefs.targets">targets</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtTargetsAll()">
<h3>dtTargetsAll</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></span>&nbsp;<span class="element-name">dtTargetsAll</span>()</div>
<div class="block">Target all columns.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/columnDefs.targets">targets</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
