package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.NewsJournal;
import com.artemide.common.persistence.entity.NewsModule;

@Repository
@Transactional(readOnly = true)
public class NewsModuleRepoDao {

    @PersistenceContext EntityManager em;

    public List<NewsModule> findAllOrderByNewsJournal(NewsJournal news) {
        String sql = "select nm from NewsModule nm where nm.news = :news order by nm.pos asc";
        return em.createQuery(sql, NewsModule.class)
            .setParameter("news", news)
            .getResultList();
    }

    /**
     * Remove the association of a gallery image from a product
     * @param newsModuleId
     * @param yadaAttachedFileId
     */
    @Transactional(readOnly = false)
    public int removeImageFromCarrousel(Long newsModuleId, Long yadaAttachedFileId) {
        String sql = "delete from PageModule_YadaAttachedFile where PageModule_id = :newsModuleId and carrouselImages_id = :yadaAttachedFileId";
        return em.createNativeQuery(sql)
            .setParameter("newsModuleId", newsModuleId)
            .setParameter("yadaAttachedFileId", yadaAttachedFileId)
            .executeUpdate();
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from NewsModule", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public NewsModule save(NewsModule entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<NewsModule> findById(Long entityId) {
        NewsModule result = em.find(NewsModule.class, entityId);
        return Optional.ofNullable(result);
    }

    public NewsModule findOne(Long entityId) {
        return em.find(NewsModule.class, entityId);
    }

    public List<NewsModule> findAll() {
        return em.createQuery("from NewsModule", NewsModule.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<NewsModule> batchToSave) {
        for (NewsModule entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(NewsModule entity) {
        em.remove(entity);
    }
}
