package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.Store;

@Repository
@Transactional(readOnly = true)
public class StoreRepoDao {

    @PersistenceContext EntityManager em;

    /**
     * Trova tutte le nazioni per una data tipologia di store (showroom, web partner...)
     * e le ordina per nome esteso (inglese)
     * @param tipologia
     * @return Lista di [ codice, nome ]
     */
    @SuppressWarnings("unchecked")
    public List<Object[]> getNazioniForTipologia(String[] tipologia) {
        String sql = "select distinct a.nazione, c.name from Store s join Address a on a.id = s.indirizzo_id join Store_tipologia t on t.Store_id = s.id " +
                "join Countries c on a.nazione = c.iso3 where t.type in :tipologia and s.hidden=false order by c.name";
        return em.createNativeQuery(sql)
            .setParameter("tipologia", tipologia)
            .getResultList();
    }

    /**
     * Searches for a store by its address line 1 and city, returning the first match found or an empty Optional if none exists.
     * Previously, name and country were also considered in the search, but now the search is strictly based on address and city to avoid
     * adding stores with slightly different names or incorrect country codes.
     *
     * @param indirizzoLinea1 The first line of the store's address.
     * @param citta           The city of the store.
     * @return An Optional containing the first matching Store entity if found, or an empty Optional if no match is found.
     */
    public Optional<Store> findFirstByIndirizzoIndirizzoLinea1AndIndirizzoCitta(String indirizzoLinea1, String citta) {
        String sql = "from Store where indirizzo.indirizzoLinea1 = :indirizzoLinea1 and indirizzo.citta = :citta";
        try {
            Store result = em.createQuery(sql, Store.class)
                .setMaxResults(1)
                .setParameter("indirizzoLinea1", indirizzoLinea1)
                .setParameter("citta", citta)
                .getSingleResult();
            return Optional.ofNullable(result);
        } catch (NonUniqueResultException | NoResultException e) {
            return Optional.empty();
        }
    }

    public List<Store> findByHiddenTrue() {
        String sql = "from Store where hidden = true";
        return em.createQuery(sql, Store.class)
            .getResultList();
    }

    public List<Store> findByNations(List<String> arrayNations) {
        String sql = "SELECT s FROM Store s INNER JOIN FETCH s.indirizzo i WHERE i.nazione IN :arrayNations";
        return em.createQuery(sql, Store.class)
            .setParameter("arrayNations", arrayNations)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public int deleteAllByHiddenTrue() {
        String sql = "DELETE FROM Store s WHERE s.hidden = true";
        return em.createQuery(sql)
            .executeUpdate();
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Store", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Store save(Store entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Store> findById(Long entityId) {
        Store result = em.find(Store.class, entityId);
        return Optional.ofNullable(result);
    }

    public Store findOne(Long entityId) {
        return em.find(Store.class, entityId);
    }

    public List<Store> findAll() {
        return em.createQuery("from Store", Store.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Store> batchToSave) {
        for (Store entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Store entity) {
        em.remove(entity);
    }
}
