<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Serialized Form (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="serialized forms">
<meta name="generator" content="javadoc/SerializedFormWriterImpl">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="serialized-form-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#serialized-form">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Serialized Form" class="title">Serialized Form</h1>
</div>
<ul class="block-list">
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/components/package-summary.html">net.yadaframework.components</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.components.YadaJsonMapper">
<h3>Class&nbsp;<a href="net/yadaframework/components/YadaJsonMapper.html" title="class in net.yadaframework.components">net.yadaframework.components.YadaJsonMapper</a></h3>
<div class="type-signature">class YadaJsonMapper extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/HashMap.html" title="class or interface in java.util" class="external-link">HashMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/exceptions/package-summary.html">net.yadaframework.exceptions</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.InternalException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/InternalException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.InternalException</a></h3>
<div class="type-signature">class InternalException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.InvalidValueException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/InvalidValueException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.InvalidValueException</a></h3>
<div class="type-signature">class InvalidValueException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.SystemException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/SystemException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.SystemException</a></h3>
<div class="type-signature">class SystemException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.YadaAlreadyRunningException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/YadaAlreadyRunningException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.YadaAlreadyRunningException</a></h3>
<div class="type-signature">class YadaAlreadyRunningException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-7485304713501926089L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>runningUsername</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> runningUsername</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.YadaConfigurationException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/YadaConfigurationException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.YadaConfigurationException</a></h3>
<div class="type-signature">class YadaConfigurationException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.YadaEmailException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/YadaEmailException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.YadaEmailException</a></h3>
<div class="type-signature">class YadaEmailException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.YadaInternalException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/YadaInternalException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.YadaInternalException</a></h3>
<div class="type-signature">class YadaInternalException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.YadaInvalidUsageException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.YadaInvalidUsageException</a></h3>
<div class="type-signature">class YadaInvalidUsageException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.YadaInvalidValueException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/YadaInvalidValueException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.YadaInvalidValueException</a></h3>
<div class="type-signature">class YadaInvalidValueException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.YadaJobFailedException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/YadaJobFailedException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.YadaJobFailedException</a></h3>
<div class="type-signature">class YadaJobFailedException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.YadaSocialException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/YadaSocialException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.YadaSocialException</a></h3>
<div class="type-signature">class YadaSocialException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.exceptions.YadaSystemException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/exceptions/YadaSystemException.html" title="class in net.yadaframework.exceptions">net.yadaframework.exceptions.YadaSystemException</a></h3>
<div class="type-signature">class YadaSystemException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-1L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/persistence/entity/package-summary.html">net.yadaframework.persistence.entity</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.persistence.entity.YadaBrowserId">
<h3>Class&nbsp;<a href="net/yadaframework/persistence/entity/YadaBrowserId.html" title="class in net.yadaframework.persistence.entity">net.yadaframework.persistence.entity.YadaBrowserId</a></h3>
<div class="type-signature">class YadaBrowserId extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-5673120637677663672L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>leastSigBits</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> leastSigBits</pre>
</li>
<li class="block-list">
<h5>mostSigBits</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> mostSigBits</pre>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.persistence.entity.YadaClause">
<h3>Class&nbsp;<a href="net/yadaframework/persistence/entity/YadaClause.html" title="class in net.yadaframework.persistence.entity">net.yadaframework.persistence.entity.YadaClause</a></h3>
<div class="type-signature">class YadaClause extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>clauseVersion</h5>
<pre>int clauseVersion</pre>
</li>
<li class="block-list">
<h5>content</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> content</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>name</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> name</pre>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.persistence.entity.YadaRateLog">
<h3>Class&nbsp;<a href="net/yadaframework/persistence/entity/YadaRateLog.html" title="class in net.yadaframework.persistence.entity">net.yadaframework.persistence.entity.YadaRateLog</a></h3>
<div class="type-signature">class YadaRateLog extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>at</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> at</pre>
</li>
<li class="block-list">
<h5>data1</h5>
<pre>long data1</pre>
</li>
<li class="block-list">
<h5>data2</h5>
<pre>long data2</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>type</h5>
<pre>int type</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/raw/package-summary.html">net.yadaframework.raw</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.raw.YadaIntDimension">
<h3>Class&nbsp;<a href="net/yadaframework/raw/YadaIntDimension.html" title="class in net.yadaframework.raw">net.yadaframework.raw.YadaIntDimension</a></h3>
<div class="type-signature">class YadaIntDimension extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>526069244927838614L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>height</h5>
<pre>int height</pre>
<div class="block">The height dimension; negative values can be used.</div>
</li>
<li class="block-list">
<h5>width</h5>
<pre>int width</pre>
<div class="block">The width dimension; negative values can be used.</div>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/web/package-summary.html">net.yadaframework.web</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.web.YadaDatatablesColumn">
<h3>Class&nbsp;<a href="net/yadaframework/web/YadaDatatablesColumn.html" title="class in net.yadaframework.web">net.yadaframework.web.YadaDatatablesColumn</a></h3>
<div class="type-signature">class YadaDatatablesColumn extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/HashMap.html" title="class or interface in java.util" class="external-link">HashMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.web.YadaDatatablesColumnSearch">
<h3>Class&nbsp;<a href="net/yadaframework/web/YadaDatatablesColumnSearch.html" title="class in net.yadaframework.web">net.yadaframework.web.YadaDatatablesColumnSearch</a></h3>
<div class="type-signature">class YadaDatatablesColumnSearch extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/HashMap.html" title="class or interface in java.util" class="external-link">HashMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.web.YadaDatatablesOrder">
<h3>Class&nbsp;<a href="net/yadaframework/web/YadaDatatablesOrder.html" title="class in net.yadaframework.web">net.yadaframework.web.YadaDatatablesOrder</a></h3>
<div class="type-signature">class YadaDatatablesOrder extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/HashMap.html" title="class or interface in java.util" class="external-link">HashMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/web/datatables/package-summary.html">net.yadaframework.web.datatables</a></h2>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/web/exceptions/package-summary.html">net.yadaframework.web.exceptions</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.web.exceptions.YadaHttpNotFoundException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/web/exceptions/YadaHttpNotFoundException.html" title="class in net.yadaframework.web.exceptions">net.yadaframework.web.exceptions.YadaHttpNotFoundException</a></h3>
<div class="type-signature">class YadaHttpNotFoundException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>-357253456084830193L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/web/flot/package-summary.html">net.yadaframework.web.flot</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.web.flot.YadaFlotChart">
<h3>Class&nbsp;<a href="net/yadaframework/web/flot/YadaFlotChart.html" title="class in net.yadaframework.web.flot">net.yadaframework.web.flot.YadaFlotChart</a></h3>
<div class="type-signature">class YadaFlotChart extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html" title="class or interface in java.util" class="external-link">ArrayList</a>&lt;<a href="net/yadaframework/web/flot/YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesObject</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.web.flot.YadaFlotSeriesObject">
<h3>Class&nbsp;<a href="net/yadaframework/web/flot/YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">net.yadaframework.web.flot.YadaFlotSeriesObject</a></h3>
<div class="type-signature">class YadaFlotSeriesObject extends <a href="net/yadaframework/web/flot/YadaFlotSeriesOptions.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesOptions</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>clickable</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a> clickable</pre>
</li>
<li class="block-list">
<h5>color</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> color</pre>
</li>
<li class="block-list">
<h5>data</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html" title="class or interface in java.util" class="external-link">ArrayList</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>[]&gt; data</pre>
</li>
<li class="block-list">
<h5>hoverable</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a> hoverable</pre>
</li>
<li class="block-list">
<h5>label</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> label</pre>
</li>
<li class="block-list">
<h5>xaxis</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a> xaxis</pre>
</li>
<li class="block-list">
<h5>yaxis</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a> yaxis</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.web.flot.YadaFlotSeriesOptions">
<h3>Class&nbsp;<a href="net/yadaframework/web/flot/YadaFlotSeriesOptions.html" title="class in net.yadaframework.web.flot">net.yadaframework.web.flot.YadaFlotSeriesOptions</a></h3>
<div class="type-signature">class YadaFlotSeriesOptions extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</main>
</div>
</div>
</body>
</html>
