package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.repository.SubfamilyRepoDao;
import com.artemide.common.repository.SubfamilyRepository;
import com.yr.entity.Famiglia;
import com.yr.entity.Subfamily;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies SubfamilyRepoDao behaves exactly like SubfamilyRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class SubfamilyRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private SubfamilyRepository subfamilyRepository;

    @Autowired
    private SubfamilyRepoDao subfamilyRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    Subfamily entity = entityManager.find(Subfamily.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            // Subfamily uses a Map for names, similar to Famiglia
            entityManager.createQuery("DELETE FROM Subfamily s WHERE s.id IN (SELECT s2.id FROM Subfamily s2 JOIN s2.names n WHERE VALUE(n).name LIKE 'TEST_%' OR VALUE(n).name LIKE 'INTEGRATION_%')")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private Subfamily createTestEntity(String name) {
        Subfamily entity = new Subfamily();
        entity.setName(name); // Subfamily has setName(String) method
        entity.setPublished(true);
        return entity;
    }

    private void trackEntity(Subfamily entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = subfamilyRepository.count();
            long initialDaoCount = subfamilyRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            Subfamily testEntity = createTestEntity("INTEGRATION_SAVE_001");

            Subfamily repoSaved = subfamilyRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            Subfamily testEntity2 = createTestEntity("INTEGRATION_SAVE_002");
            Subfamily daoSaved = subfamilyRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<Subfamily> repoFound = subfamilyRepository.findById(repoSaved.getId());
            Optional<Subfamily> daoFoundOptional = subfamilyRepoDao.findById(daoSaved.getId());
            Subfamily daoFoundDirect = subfamilyRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<Subfamily> repoResults = subfamilyRepository.findAll();
            List<Subfamily> daoResults = subfamilyRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            Subfamily entityForRepo = createTestEntity("INTEGRATION_DELETE_001");
            Subfamily entityForDao = createTestEntity("INTEGRATION_DELETE_002");
            
            Subfamily savedForRepo = subfamilyRepository.save(entityForRepo);
            Subfamily savedForDao = subfamilyRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!subfamilyRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!subfamilyRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            subfamilyRepository.delete(savedForRepo);
            subfamilyRepoDao.delete(savedForDao);
            entityManager.flush();

            if (subfamilyRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (subfamilyRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindName() {
        setUp();
        try {
            // Get first available subfamily ID using DbUtil
            java.util.Optional<Long> subfamilyIdOpt = DbUtil.findFirstId(entityManager, Subfamily.class);
            if (!subfamilyIdOpt.isPresent()) {
                return "SKIP: No subfamily records found in database for testing";
            }
            Long testSubfamilyId = subfamilyIdOpt.get();
            
            String repoResult = subfamilyRepository.findName(testSubfamilyId, "en_US");
            String daoResult = subfamilyRepoDao.findName(testSubfamilyId, "en_US");

            if ((repoResult == null && daoResult != null) || (repoResult != null && !repoResult.equals(daoResult))) {
                return "FAIL: findName results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: findName() test successful";
        } finally {
            tearDown();
        }
    }

    public String testCountByFamiglia() {
        setUp();
        try {
            // Get first available famiglia ID using DbUtil
            java.util.Optional<Long> famigliaIdOpt = DbUtil.findFirstId(entityManager, Famiglia.class);
            if (!famigliaIdOpt.isPresent()) {
                return "SKIP: No famiglia records found in database for testing";
            }
            Long testFamigliaId = famigliaIdOpt.get();
            
            int repoResult = subfamilyRepository.countByFamiglia(testFamigliaId, false);
            int daoResult = subfamilyRepoDao.countByFamiglia(testFamigliaId, false);

            if (repoResult != daoResult) {
                return "FAIL: countByFamiglia results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: countByFamiglia() test successful";
        } finally {
            tearDown();
        }
    }

    public String testAppCompatibleColorFromFamiglia() {
        setUp();
        try {
            // Get first available famiglia ID using DbUtil
            java.util.Optional<Long> famigliaIdOpt = DbUtil.findFirstId(entityManager, Famiglia.class);
            if (!famigliaIdOpt.isPresent()) {
                return "SKIP: No famiglia records found in database for testing";
            }
            Long testFamigliaId = famigliaIdOpt.get();
            
            int repoResult = subfamilyRepository.appCompatibleColorFromFamiglia(testFamigliaId, false);
            int daoResult = subfamilyRepoDao.appCompatibleColorFromFamiglia(testFamigliaId, false);

            if (repoResult != daoResult) {
                return "FAIL: appCompatibleColorFromFamiglia results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: appCompatibleColorFromFamiglia() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindByFamilyId() {
        setUp();
        try {
            // Get first available famiglia ID using DbUtil
            java.util.Optional<Long> familyIdOpt = DbUtil.findFirstId(entityManager, Famiglia.class);
            if (!familyIdOpt.isPresent()) {
                return "SKIP: No famiglia records found in database for testing";
            }
            Long testFamilyId = familyIdOpt.get();
            
            Set<Long> repoResult = subfamilyRepository.findByFamiglia(testFamilyId);
            Set<Long> daoResult = subfamilyRepoDao.findByFamiglia(testFamilyId);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findByFamiglia(Long) results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findByFamiglia(Long) test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAllSorted() {
        setUp();
        try {
            // Test with locale
            List<Subfamily> repoResult = subfamilyRepository.findAllSorted("en_US");
            List<Subfamily> daoResult = subfamilyRepoDao.findAllSorted("en_US");

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findAllSorted results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findAllSorted() test successful";
        } finally {
            tearDown();
        }
    }

    @Transactional(readOnly = false)
    public String testSetPublishedStatusForSubfamilies() {
        setUp();
        try {
            // Create test entities
            Subfamily testEntity1 = createTestEntity("INTEGRATION_PUB_001");
            Subfamily testEntity2 = createTestEntity("INTEGRATION_PUB_002");
            
            Subfamily saved1 = subfamilyRepository.save(testEntity1);
            Subfamily saved2 = subfamilyRepository.save(testEntity2);
            trackEntity(saved1);
            trackEntity(saved2);
            entityManager.flush();

            // Test the method
            List<Long> ids = java.util.Arrays.asList(saved1.getId(), saved2.getId());
            int repoResult = subfamilyRepository.setPublishedStatusForSubfamilies(ids, true);
            int daoResult = subfamilyRepoDao.setPublishedStatusForSubfamilies(ids, false);
            entityManager.flush();
            
            // Both methods should execute and return count of updated records
            if (repoResult < 0 || daoResult < 0) {
                return "FAIL: setPublishedStatusForSubfamilies should return non-negative count";
            }
            
            return "PASS: setPublishedStatusForSubfamilies() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== SubfamilyRepository vs SubfamilyRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String findNameResult = testFindName();
            report.append("5. FindName Test: ").append(findNameResult).append("\n");
            
            String countByFamigliaResult = testCountByFamiglia();
            report.append("6. CountByFamiglia Test: ").append(countByFamigliaResult).append("\n");
            
            String appCompatibleColorResult = testAppCompatibleColorFromFamiglia();
            report.append("7. AppCompatibleColorFromFamiglia Test: ").append(appCompatibleColorResult).append("\n");
            
            String findByFamilyIdResult = testFindByFamilyId();
            report.append("8. FindByFamilyId Test: ").append(findByFamilyIdResult).append("\n");
            
            String findAllSortedResult = testFindAllSorted();
            report.append("9. FindAllSorted Test: ").append(findAllSortedResult).append("\n");
            
            String setPublishedStatusResult = testSetPublishedStatusForSubfamilies();
            report.append("10. SetPublishedStatusForSubfamilies Test: ").append(setPublishedStatusResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - SubfamilyRepoDao behaves exactly like SubfamilyRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
