<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.web.dialect (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.dialect">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package net.yadaframework.web.dialect" class="title">Package net.yadaframework.web.dialect</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">net.yadaframework.web.dialect</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">net.yadaframework.web</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../datatables/package-summary.html">net.yadaframework.web.datatables</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../exceptions/package-summary.html">net.yadaframework.web.exceptions</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../flot/package-summary.html">net.yadaframework.web.flot</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="../form/package-summary.html">net.yadaframework.web.form</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../social/package-summary.html">net.yadaframework.web.social</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaAjaxAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaAjaxAttrProcessor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Class that handles the convenience attribute yada:ajax="url".</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaBrOnFirstSpaceAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaBrOnFirstSpaceAttrProcessor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Adds the yada:brspace="" attribute that insert a &lt;br&gt; tag after the first space.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaDataTableTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaDataTableTagProcessor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Defines the &lt;yada:dataTable&gt; tag</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaDialect.html" title="class in net.yadaframework.web.dialect">YadaDialect</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaDialectUtil.html" title="class in net.yadaframework.web.dialect">YadaDialectUtil</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaHrefAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaHrefAttrProcessor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Class that handles the convenience attribute yada:href="url".</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaInputCounterTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaInputCounterTagProcessor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Defines the &lt;yada:inputCounter&gt; tag</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaInputTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaInputTagProcessor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Defines the &lt;yada:input&gt; tag</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="YadaInputTagSuggestion.html" title="interface in net.yadaframework.web.dialect">YadaInputTagSuggestion</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">
<div class="block">An object implementing this interface can be returned by the</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaNewlineTextAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaNewlineTextAttrProcessor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Adds the yada:newlinetext="" attribute that converts ASCII newlines to HTML newlines, and the yada:unewlinetext that does not escape the original text</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaSimpleAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaSimpleAttrProcessor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Converts from a "yada:xxx" thymeleaf attribute to a plain html attribute.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaSrcAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaSrcAttrProcessor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaSrcsetAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaSrcsetAttrProcessor</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">Deprecated.</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaTextareaTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaTextareaTagProcessor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Defines the &lt;yada:textarea&gt; tag</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
