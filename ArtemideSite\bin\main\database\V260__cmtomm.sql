# Convert cm to mm
# Ignores empty cells, uses no decimals, handles special case

UPDATE Prodotto SET height = "640/940" where height = "64/94";

UPDATE Prodotto SET
    height = IF(height = '', '', CAST(CAST(height AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    length = IF(length = '', '', CAST(CAST(length AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    depth = IF(depth = '', '', CAST(CAST(depth AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    width = IF(width = '', '', CAST(CAST(width AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    radius = IF(radius = '', '', CAST(CAST(radius AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    diameter = IF(diameter = '', '', CAST(CAST(diameter AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    cutoutLength = IF(cutoutLength = '', '', CAST(CAST(cutoutLength AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    cutoutWidth = IF(cutoutWidth = '', '', CAST(CAST(cutoutWidth AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    cutoutDiameter = IF(cutoutDiameter = '', '', CAST(CAST(cutoutDiameter AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    baseDiameter = IF(baseDiameter = '', '', CAST(CAST(baseDiameter AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    baseWidth = IF(baseWidth = '', '', CAST(CAST(baseWidth AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    baseLength = IF(baseLength = '', '', CAST(CAST(baseLength AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    recessedDepth = IF(recessedDepth = '', '', CAST(CAST(recessedDepth AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    maxExtensionLength = IF(maxExtensionLength = '', '', CAST(CAST(maxExtensionLength AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    maxHeightFromCeiling = IF(maxHeightFromCeiling = '', '', CAST(CAST(maxHeightFromCeiling AS DECIMAL(20, 1)) * 10 AS UNSIGNED)),
    maxExtensionHeight = IF(maxExtensionHeight = '', '', CAST(CAST(maxExtensionHeight AS DECIMAL(20, 1)) * 10 AS UNSIGNED))
    where height != "640/940";;
