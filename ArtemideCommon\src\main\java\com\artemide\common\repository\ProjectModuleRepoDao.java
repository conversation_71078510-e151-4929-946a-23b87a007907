package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.Project;
import com.artemide.common.persistence.entity.ProjectModule;

@Repository
@Transactional(readOnly = true)
public class ProjectModuleRepoDao {

    @PersistenceContext EntityManager em;

    public List<ProjectModule> findAllOrderByProject(Project project) {
        String sql = "select pm from ProjectModule pm where pm.project = :project order by pm.pos asc";
        return em.createQuery(sql, ProjectModule.class)
            .setParameter("project", project)
            .getResultList();
    }

    /**
     * Remove the association of a gallery image from a product
     * @param projectModuleId
     * @param yadaAttachedFileId
     */
    @Transactional(readOnly = false)
    public int removeImageFromCarrousel(Long projectModuleId, Long yadaAttachedFileId) {
        String sql = "delete from PageModule_YadaAttachedFile where PageModule_id = :projectModuleId and carrouselImages_id = :yadaAttachedFileId";
        return em.createNativeQuery(sql)
            .setParameter("projectModuleId", projectModuleId)
            .setParameter("yadaAttachedFileId", yadaAttachedFileId)
            .executeUpdate();
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from ProjectModule", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public ProjectModule save(ProjectModule entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<ProjectModule> findById(Long entityId) {
        ProjectModule result = em.find(ProjectModule.class, entityId);
        return Optional.ofNullable(result);
    }

    public ProjectModule findOne(Long entityId) {
        return em.find(ProjectModule.class, entityId);
    }

    public List<ProjectModule> findAll() {
        return em.createQuery("from ProjectModule", ProjectModule.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<ProjectModule> batchToSave) {
        for (ProjectModule entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(ProjectModule entity) {
        em.remove(entity);
    }
}
