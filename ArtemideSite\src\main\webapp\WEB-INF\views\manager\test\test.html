<!DOCTYPE html>
<html th:with="activePage=Test, bootstrap4=true"
	th:lang="${#locale.language}"
	xmlns:th="http://www.thymeleaf.org" 
	xmlns:yada="http://www.yadaframework.net">
	<head>
		<meta charset="utf-8"/>
		<th:block th:replace="~{/header2 :: head}"/>
		<th:block th:replace="~{/manager/managerHeader :: head}"/>
	</head>
    <body class="amd manager test">
	<th:block th:replace="~{/header2 :: body}"/>
	
	<div class="container-fluid bookingPage">
		<div th:replace="~{/manager/menu::#menu}"></div>
		
		<p class="warning">This page should be used by developers only</p>

		<hr>
		
		<div class="row">
			<div class="col">
				<h1>Repository Refactoring Test</h1>
				<p>This will run integration tests to verify that EtichettaRepoDao behaves exactly like EtichettaRepository. The test creates, reads, updates and deletes test data to ensure both implementations work identically.</p>
		        <a th:href="@{/manager/test/runRepoRefactoringTest}" class="btn btn-primary yadaAjax">Run repo refactoring test</a>
			</div>
		</div>
		
		<hr>
		
		<div class="row">
			<div class="col">
				<h1>Configurator Regression Test</h1>
				<form class="yadaAjax yadaNoLoader jsCaptureToggle" th:action="@{/manager/test/captureToggle}" yada:updateOnSuccess="" role="form">
					<div class="amdSwitch">
						<span>
							<input type="checkbox" name="captureSwitch" value="true" th:checked="${@applicationState.regressionTestCapture}" class="yadaSwitch yadaSwitch-round" id="captureSwitch"/>
							<label th:for="captureSwitch"></label>
						</span>
						<label for="captureSwitch">Enable/Disable the capture</label>
					</div>
					<script>$("[name=captureSwitch]").change(function() {$(".jsCaptureToggle").trigger("submit");})</script>
				</form>
				<p>Enabling capture will save configurator data locally, for later comparison by means of the "Run Regression" button below</p>
				<p th:if="${@config.productionEnvironment}">Regression test always disabled in production</p>
				<a th:if="${!@config.productionEnvironment}" th:href="@{/manager/test/configuratorRegression}" class="btn btn-primary yadaAjax">Run Regression</a>
			</div>
		</div>
		<hr>
		<th:block th:if="${@config.configuratorRegressionTestEnabled}">
            <div class="row">
                <div class="col">
                    <h1>Configurator Visual Test</h1>
                    <p>Run a test on the configurator. It will use Percy if configured, otherwise it will save screenshots locally. It may take some time.</p>
                    <p>The <strong>visual test</strong> should be used to compare the configurator with and without cache:</p>
                    <ul>
                        <li>Clear the cache</li>
                        <li>Start the Percy server, e.g. /home/<USER>/percytest/percyStart.sh</li>
                        <li>Run this test</li>
                        <li>Stop the Percy server so that the snapshots are uploaded to Percy</li>
                        <li>Approve all screenshots on Percy</li>
                        <li>Start the Percy server again</li>
                        <li>Run the same test without clearing the cache</li>
                        <li>Stop the Percy server</li>
                        <li>Check the new screenshots compared to the previous ones</li>
                    </ul>
                    <br>
                    <a th:href="@{/manager/test/seleniumConfigurator}" class="btn btn-primary yadaAjax">Call</a>
                </div>
            </div>
            <hr>
        </th:block>
		<div class="row">
			<div class="col">
				<h1>Regression Visual Test</h1>
				<p>Run a test on all site pages. It will save screenshots locally to [[${@config.basePath}]], not using Percy. It may take some time.</p>
				<p>The <strong>regression visual test</strong> should be used to compare site pages before and after changes:</p>
				<ul>
					<li>delete the contens of visualtestsite and visualtestsite_mobile</li>
					<li>run the tests - this will create the baseline</li>
					<li>deploy the new version of the application</li>
					<li>run the tests again - this will create a new test subfolder with a diff folder in it</li>
					<li>if the test reports a failure, check the diff folder to see where the new application differ</li>
					<li>make the needed changes to the application, deploy again, run the test again and repeat until satisfied</li>
				</ul>
				<br>
		        <th:block th:replace="~{/manager/test/seleniumRegression :: seleniumRegression}" />
			</div>
		</div>
		<hr>
		<div class="row">
			<div class="col">
				<h1>Subfamily Visual Test</h1>
				<p>Run a test on the subfamily pages. It will use Percy if configured, otherwise it will save screenshots locally. It may take some time. The speed and outcome is influenced by the subfamily cache that can be controlled <a href="../manager/tools">here</a>.</p>
				<p>The <strong>visual test</strong> should be used to compare subfamily pages with and without cache:</p>
				<ul>
					<li>Clear the subfamily cache</li>					
					<li>Start the Percy server, e.g. /home/<USER>/percytest/percyStart.sh</li>	
					<li>Run this test</li>
					<li>Stop the Percy server so that the snapshots are uploaded to Percy</li>
					<li>Approve all screenshots on Percy</li>
					<li>Start the Percy server again</li>
					<li>Run the same test without clearing the cache</li>
					<li>Stop the Percy server</li>
					<li>Check the new screenshots compared to the previous ones</li>
				</ul>
				<br>
				<p>The <strong>RAM test</strong> is used to compute the RAM allocation for the cache. It should be run with all subfamilies and languages.</p>
		        <th:block th:replace="~{/manager/test/seleniumSubfamily :: seleniumSubfamily}" />
			</div>
		</div>
		
		<hr>
		
		<div class="row">
			<div class="col">
				<h1>Test Risolutore</h1>
				<p>This will call the Funivia Risolutore python script. No output is expected.</p>
		        <a th:href="@{/manager/test/testRisolutore}" class="btn btn-primary yadaAjax">Call</a>
			</div>
		</div>
	</div>

	<th:block th:replace="~{/footer2 :: body(noModalConfirm=true)}"/>
	<div th:insert="~{/yada/modalConfirmB4 :: modalBlock}" id="yada-confirm"></div>
	
	</body>
</html>