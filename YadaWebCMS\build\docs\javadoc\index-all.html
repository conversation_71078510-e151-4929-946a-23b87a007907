<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Index (YadaWebCMS '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="index">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="nav-bar-cell1-rev">Index</li>
<li><a href="help-doc.html#index">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>Index</h1>
</div>
<a href="#I:A">A</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:W">W</a>&nbsp;<a href="#I:Y">Y</a>&nbsp;<br><a href="allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="allpackages-index.html">All&nbsp;Packages</a><span class="vertical-separator">|</span><a href="serialized-form.html">Serialized&nbsp;Form</a>
<h2 class="title" id="I:A">A</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#accessories" class="member-name-link">accessories</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#accessoryFlag" class="member-name-link">accessoryFlag</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>
<div class="block">true if the YadaProduct is an accessory</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#accessoryOf" class="member-name-link">accessoryOf</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/repository/YadaProductDao.html#addGalleryImage(java.lang.Long,java.lang.Long)" class="member-name-link">addGalleryImage(Long, Long)</a> - Method in class net.yadaframework.cms.persistence.repository.<a href="net/yadaframework/cms/persistence/repository/YadaProductDao.html" title="class in net.yadaframework.cms.persistence.repository">YadaProductDao</a></dt>
<dd>
<div class="block">Adds a new gallery image attachment to a product</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#articles" class="member-name-link">articles</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/YadaCmsConfiguration.html#assetManagerPublicPath()" class="member-name-link">assetManagerPublicPath()</a> - Method in class net.yadaframework.cms.<a href="net/yadaframework/cms/YadaCmsConfiguration.html" title="class in net.yadaframework.cms">YadaCmsConfiguration</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/YadaCmsConfiguration.html#assetManagerPublicUrl()" class="member-name-link">assetManagerPublicUrl()</a> - Method in class net.yadaframework.cms.<a href="net/yadaframework/cms/YadaCmsConfiguration.html" title="class in net.yadaframework.cms">YadaCmsConfiguration</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#attachments" class="member-name-link">attachments</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#attachments" class="member-name-link">attachments</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:C">C</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#categories" class="member-name-link">categories</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#chosenProductId" class="member-name-link">chosenProductId</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#color" class="member-name-link">color</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:D">D</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/web/YadaCmsProductController.html#deleteGalleryImage(java.lang.Long,java.lang.Long)" class="member-name-link">deleteGalleryImage(Long, Long)</a> - Method in class net.yadaframework.cms.web.<a href="net/yadaframework/cms/web/YadaCmsProductController.html" title="class in net.yadaframework.cms.web">YadaCmsProductController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#depth" class="member-name-link">depth</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#description" class="member-name-link">description</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#diameter" class="member-name-link">diameter</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#dimension" class="member-name-link">dimension</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:E">E</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#elements" class="member-name-link">elements</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#equals(java.lang.Object)" class="member-name-link">equals(Object)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#equalsInternal(java.lang.Object)" class="member-name-link">equalsInternal(Object)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:G">G</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#galleryImages" class="member-name-link">galleryImages</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#galleryImages" class="member-name-link">galleryImages</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getAccessories()" class="member-name-link">getAccessories()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getAccessoryOf()" class="member-name-link">getAccessoryOf()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getArticles()" class="member-name-link">getArticles()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/YadaCmsConfiguration.html#getAssetManagerPrivatePath()" class="member-name-link">getAssetManagerPrivatePath()</a> - Method in class net.yadaframework.cms.<a href="net/yadaframework/cms/YadaCmsConfiguration.html" title="class in net.yadaframework.cms">YadaCmsConfiguration</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getAttachments()" class="member-name-link">getAttachments()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getAttachments()" class="member-name-link">getAttachments()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getCategories()" class="member-name-link">getCategories()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getChosenProductId()" class="member-name-link">getChosenProductId()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getColor()" class="member-name-link">getColor()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getColor(java.util.Locale)" class="member-name-link">getColor(Locale)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getData1()" class="member-name-link">getData1()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getData2()" class="member-name-link">getData2()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getData3()" class="member-name-link">getData3()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getData4()" class="member-name-link">getData4()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getData5()" class="member-name-link">getData5()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getData6()" class="member-name-link">getData6()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#getDepth()" class="member-name-link">getDepth()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getDescription()" class="member-name-link">getDescription()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#getDiameter()" class="member-name-link">getDiameter()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getDimension()" class="member-name-link">getDimension()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#getElements()" class="member-name-link">getElements()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getExcludedFields()" class="member-name-link">getExcludedFields()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getExcludedFields()" class="member-name-link">getExcludedFields()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getExcludedFields()" class="member-name-link">getExcludedFields()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/components/YadaWebCmsHelper.html#getFlagClasses(java.lang.Object,java.lang.String)" class="member-name-link">getFlagClasses(Object, String)</a> - Method in class net.yadaframework.cms.components.<a href="net/yadaframework/cms/components/YadaWebCmsHelper.html" title="class in net.yadaframework.cms.components">YadaWebCmsHelper</a></dt>
<dd>
<div class="block">Returns a space-separated list of classes taken from the flagClasses list when the corresponding flag on target is true.</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getGalleryImages()" class="member-name-link">getGalleryImages()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getGalleryImages()" class="member-name-link">getGalleryImages()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#getHeight()" class="member-name-link">getHeight()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getId()" class="member-name-link">getId()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getId()" class="member-name-link">getId()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getId()" class="member-name-link">getId()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getImage()" class="member-name-link">getImage()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getImage()" class="member-name-link">getImage()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getImage()" class="member-name-link">getImage()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getInternalName()" class="member-name-link">getInternalName()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#getLength()" class="member-name-link">getLength()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getLocalColor()" class="member-name-link">getLocalColor()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>
<div class="block">Returns the localized color in the current request locale</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getLocalDescription()" class="member-name-link">getLocalDescription()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>
<div class="block">Returns the localized description in the current request locale</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getLocalMaterials()" class="member-name-link">getLocalMaterials()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>
<div class="block">Returns the localized materials in the current request locale</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getLocalName()" class="member-name-link">getLocalName()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>
<div class="block">Returns the localized name in the current request locale</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getLocalName()" class="member-name-link">getLocalName()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>
<div class="block">Returns the localized name in the current request locale</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getLocalSubtitle()" class="member-name-link">getLocalSubtitle()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>
<div class="block">Returns the localized subtitle in the current request locale</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getMaterials()" class="member-name-link">getMaterials()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getModified()" class="member-name-link">getModified()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getModified()" class="member-name-link">getModified()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getMultipartImage()" class="member-name-link">getMultipartImage()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getMultipartVideo()" class="member-name-link">getMultipartVideo()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getName()" class="member-name-link">getName()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getName()" class="member-name-link">getName()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getPos()" class="member-name-link">getPos()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getProduct()" class="member-name-link">getProduct()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#getRadius()" class="member-name-link">getRadius()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getSilhouetteImages()" class="member-name-link">getSilhouetteImages()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#getSize()" class="member-name-link">getSize()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getSku()" class="member-name-link">getSku()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getSubcategories()" class="member-name-link">getSubcategories()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getSubtitle()" class="member-name-link">getSubtitle()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText1()" class="member-name-link">getText1()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText2()" class="member-name-link">getText2()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText3()" class="member-name-link">getText3()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText4()" class="member-name-link">getText4()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText5local()" class="member-name-link">getText5local()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText5LocalValue()" class="member-name-link">getText5LocalValue()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText6local()" class="member-name-link">getText6local()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText6LocalValue()" class="member-name-link">getText6LocalValue()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText7local()" class="member-name-link">getText7local()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText7LocalValue()" class="member-name-link">getText7LocalValue()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText8local()" class="member-name-link">getText8local()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getText8LocalValue()" class="member-name-link">getText8LocalValue()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaSortableEntity.html#getThumbnail()" class="member-name-link">getThumbnail()</a> - Method in interface net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaSortableEntity.html" title="interface in net.yadaframework.cms.persistence.entity">YadaSortableEntity</a></dt>
<dd>
<div class="block">Returns the representative image used for the sorting interface</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaSortableEntity.html#getTitle()" class="member-name-link">getTitle()</a> - Method in interface net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaSortableEntity.html" title="interface in net.yadaframework.cms.persistence.entity">YadaSortableEntity</a></dt>
<dd>
<div class="block">Returns a string representation of the element</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getUnitPrice()" class="member-name-link">getUnitPrice()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#getVersion()" class="member-name-link">getVersion()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getVersion()" class="member-name-link">getVersion()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#getVideo()" class="member-name-link">getVideo()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#getWeight()" class="member-name-link">getWeight()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#getWidth()" class="member-name-link">getWidth()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#getYear()" class="member-name-link">getYear()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:H">H</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#hashCode()" class="member-name-link">hashCode()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#hashCodeInternal()" class="member-name-link">hashCodeInternal()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#height" class="member-name-link">height</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:I">I</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#id" class="member-name-link">id</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#id" class="member-name-link">id</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#image" class="member-name-link">image</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>
<div class="block">The main image to show in lists etc.</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#image" class="member-name-link">image</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>
<div class="block">The main image to show in lists etc.</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#internalName" class="member-name-link">internalName</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#isAccessoryFlag()" class="member-name-link">isAccessoryFlag()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#isFlag1()" class="member-name-link">isFlag1()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#isFlag2()" class="member-name-link">isFlag2()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#isPublished()" class="member-name-link">isPublished()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#isPublished()" class="member-name-link">isPublished()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#isSlideEnabled()" class="member-name-link">isSlideEnabled()</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:L">L</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#length" class="member-name-link">length</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:M">M</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#materials" class="member-name-link">materials</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#modified" class="member-name-link">modified</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#modified" class="member-name-link">modified</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:N">N</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#name" class="member-name-link">name</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#name" class="member-name-link">name</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/package-summary.html">net.yadaframework.cms</a> - package net.yadaframework.cms</dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/components/package-summary.html">net.yadaframework.cms.components</a> - package net.yadaframework.cms.components</dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/package-summary.html">net.yadaframework.cms.persistence.entity</a> - package net.yadaframework.cms.persistence.entity</dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/repository/package-summary.html">net.yadaframework.cms.persistence.repository</a> - package net.yadaframework.cms.persistence.repository</dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/web/package-summary.html">net.yadaframework.cms.web</a> - package net.yadaframework.cms.web</dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:P">P</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#product" class="member-name-link">product</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#published" class="member-name-link">published</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#published" class="member-name-link">published</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:R">R</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#radius" class="member-name-link">radius</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/repository/YadaProductDao.html#removeGalleryImage(java.lang.Long,java.lang.Long)" class="member-name-link">removeGalleryImage(Long, Long)</a> - Method in class net.yadaframework.cms.persistence.repository.<a href="net/yadaframework/cms/persistence/repository/YadaProductDao.html" title="class in net.yadaframework.cms.persistence.repository">YadaProductDao</a></dt>
<dd>
<div class="block">Remove the association of a gallery image from a product</div>
</dd>
</dl>
<h2 class="title" id="I:S">S</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#seLocalDescription(java.lang.String)" class="member-name-link">seLocalDescription(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#seLocalMaterials(java.lang.String)" class="member-name-link">seLocalMaterials(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#seLocalName(java.lang.String)" class="member-name-link">seLocalName(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#seLocalSubtitle(java.lang.String)" class="member-name-link">seLocalSubtitle(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setAccessories(java.util.List)" class="member-name-link">setAccessories(List&lt;YadaProduct&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setAccessoryFlag(boolean)" class="member-name-link">setAccessoryFlag(boolean)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setAccessoryOf(java.util.List)" class="member-name-link">setAccessoryOf(List&lt;YadaProduct&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setArticles(java.util.List)" class="member-name-link">setArticles(List&lt;YadaArticle&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setAttachments(java.util.List)" class="member-name-link">setAttachments(List&lt;YadaAttachedFile&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setAttachments(java.util.List)" class="member-name-link">setAttachments(List&lt;YadaAttachedFile&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setCategories(java.util.List)" class="member-name-link">setCategories(List&lt;YadaPersistentEnum&lt;?&gt;&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setChosenProductId(java.lang.Long)" class="member-name-link">setChosenProductId(Long)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setColor(java.util.Map)" class="member-name-link">setColor(Map&lt;Locale, String&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setData1(java.lang.String)" class="member-name-link">setData1(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setData2(java.lang.String)" class="member-name-link">setData2(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setData3(java.lang.String)" class="member-name-link">setData3(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setData4(java.lang.String)" class="member-name-link">setData4(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setData5(java.lang.String)" class="member-name-link">setData5(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setData6(java.lang.String)" class="member-name-link">setData6(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#setDepth(java.lang.Float)" class="member-name-link">setDepth(Float)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setDescription(java.util.Map)" class="member-name-link">setDescription(Map&lt;Locale, String&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#setDiameter(java.lang.Float)" class="member-name-link">setDiameter(Float)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setDimension(net.yadaframework.cms.persistence.entity.YadaDimension)" class="member-name-link">setDimension(YadaDimension)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#setElements(java.lang.Integer)" class="member-name-link">setElements(Integer)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setFlag1(boolean)" class="member-name-link">setFlag1(boolean)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setFlag2(boolean)" class="member-name-link">setFlag2(boolean)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setGalleryImages(java.util.List)" class="member-name-link">setGalleryImages(List&lt;YadaAttachedFile&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setGalleryImages(java.util.List)" class="member-name-link">setGalleryImages(List&lt;YadaAttachedFile&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#setHeight(java.lang.Float)" class="member-name-link">setHeight(Float)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setId(java.lang.Long)" class="member-name-link">setId(Long)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setId(java.lang.Long)" class="member-name-link">setId(Long)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setId(java.lang.Long)" class="member-name-link">setId(Long)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setImage(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">setImage(YadaAttachedFile)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setImage(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">setImage(YadaAttachedFile)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setImage(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">setImage(YadaAttachedFile)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setInternalName(java.lang.String)" class="member-name-link">setInternalName(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#setLength(java.lang.Float)" class="member-name-link">setLength(Float)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setLocalcolor(java.lang.String)" class="member-name-link">setLocalcolor(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setLocalName(java.lang.String)" class="member-name-link">setLocalName(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setLocalName(java.util.Locale,java.lang.String)" class="member-name-link">setLocalName(Locale, String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setMaterials(java.util.Map)" class="member-name-link">setMaterials(Map&lt;Locale, String&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setModified(java.util.Date)" class="member-name-link">setModified(Date)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setModified(java.util.Date)" class="member-name-link">setModified(Date)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setMultipartImage(org.springframework.web.multipart.MultipartFile)" class="member-name-link">setMultipartImage(MultipartFile)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setMultipartVideo(org.springframework.web.multipart.MultipartFile)" class="member-name-link">setMultipartVideo(MultipartFile)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setName(java.util.Map)" class="member-name-link">setName(Map&lt;Locale, String&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setName(java.util.Map)" class="member-name-link">setName(Map&lt;Locale, String&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setPos(java.lang.Long)" class="member-name-link">setPos(Long)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setProduct(net.yadaframework.cms.persistence.entity.YadaProduct)" class="member-name-link">setProduct(YadaProduct)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setPublished(boolean)" class="member-name-link">setPublished(boolean)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setPublished(boolean)" class="member-name-link">setPublished(boolean)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#setRadius(java.lang.Float)" class="member-name-link">setRadius(Float)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setSilhouetteImages(java.util.List)" class="member-name-link">setSilhouetteImages(List&lt;YadaAttachedFile&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#setSize(java.lang.String)" class="member-name-link">setSize(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setSku(java.lang.String)" class="member-name-link">setSku(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setSlideEnabled(boolean)" class="member-name-link">setSlideEnabled(boolean)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setSubcategories(java.util.List)" class="member-name-link">setSubcategories(List&lt;YadaPersistentEnum&lt;?&gt;&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setSubtitle(java.util.Map)" class="member-name-link">setSubtitle(Map&lt;Locale, String&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setText1(java.lang.String)" class="member-name-link">setText1(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setText2(java.lang.String)" class="member-name-link">setText2(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setText3(java.lang.String)" class="member-name-link">setText3(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setText4(java.lang.String)" class="member-name-link">setText4(String)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setText5local(java.util.Map)" class="member-name-link">setText5local(Map&lt;Locale, String&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setText6local(java.util.Map)" class="member-name-link">setText6local(Map&lt;Locale, String&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setText7local(java.util.Map)" class="member-name-link">setText7local(Map&lt;Locale, String&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setText8local(java.util.Map)" class="member-name-link">setText8local(Map&lt;Locale, String&gt;)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/repository/YadaProductDao.html#setThumbnailImage(java.lang.Long,java.lang.Long)" class="member-name-link">setThumbnailImage(Long, Long)</a> - Method in class net.yadaframework.cms.persistence.repository.<a href="net/yadaframework/cms/persistence/repository/YadaProductDao.html" title="class in net.yadaframework.cms.persistence.repository">YadaProductDao</a></dt>
<dd>
<div class="block">Update a new thumbnail image attachment to a product</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#setUnitPrice(net.yadaframework.persistence.YadaMoney)" class="member-name-link">setUnitPrice(YadaMoney)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#setVideo(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">setVideo(YadaAttachedFile)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#setWeight(java.lang.Float)" class="member-name-link">setWeight(Float)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#setWidth(java.lang.Float)" class="member-name-link">setWidth(Float)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#setYear(int)" class="member-name-link">setYear(int)</a> - Method in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#silhouetteImages" class="member-name-link">silhouetteImages</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#size" class="member-name-link">size</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#sku" class="member-name-link">sku</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#subcategories" class="member-name-link">subcategories</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#subtitle" class="member-name-link">subtitle</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/repository/YadaWebCmsDao.html#swapAttributes(java.lang.Class,java.lang.String,java.lang.Long,java.lang.Long)" class="member-name-link">swapAttributes(Class&lt;?&gt;, String, Long, Long)</a> - Method in class net.yadaframework.cms.persistence.repository.<a href="net/yadaframework/cms/persistence/repository/YadaWebCmsDao.html" title="class in net.yadaframework.cms.persistence.repository">YadaWebCmsDao</a></dt>
<dd>
<div class="block">Swaps the attributes of two entities.</div>
</dd>
</dl>
<h2 class="title" id="I:U">U</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#unitPrice" class="member-name-link">unitPrice</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:V">V</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#version" class="member-name-link">version</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#version" class="member-name-link">version</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:W">W</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#weight" class="member-name-link">weight</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#width" class="member-name-link">width</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:Y">Y</h2>
<dl class="index">
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" class="type-name-link" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a> - Class in <a href="net/yadaframework/cms/persistence/entity/package-summary.html">net.yadaframework.cms.persistence.entity</a></dt>
<dd>
<div class="block">An Article is the actual physical item that can be produced and sold, so it will have a specific color, size, price, etc.</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaArticle.html#%3Cinit%3E()" class="member-name-link">YadaArticle()</a> - Constructor for class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaArticle.html" title="class in net.yadaframework.cms.persistence.entity">YadaArticle</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/YadaCmsConfig.html" class="type-name-link" title="class in net.yadaframework.cms">YadaCmsConfig</a> - Class in <a href="net/yadaframework/cms/package-summary.html">net.yadaframework.cms</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/YadaCmsConfig.html#%3Cinit%3E()" class="member-name-link">YadaCmsConfig()</a> - Constructor for class net.yadaframework.cms.<a href="net/yadaframework/cms/YadaCmsConfig.html" title="class in net.yadaframework.cms">YadaCmsConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/YadaCmsConfiguration.html" class="type-name-link" title="class in net.yadaframework.cms">YadaCmsConfiguration</a> - Class in <a href="net/yadaframework/cms/package-summary.html">net.yadaframework.cms</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/YadaCmsConfiguration.html#%3Cinit%3E()" class="member-name-link">YadaCmsConfiguration()</a> - Constructor for class net.yadaframework.cms.<a href="net/yadaframework/cms/YadaCmsConfiguration.html" title="class in net.yadaframework.cms">YadaCmsConfiguration</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/web/YadaCmsProductController.html" class="type-name-link" title="class in net.yadaframework.cms.web">YadaCmsProductController</a> - Class in <a href="net/yadaframework/cms/web/package-summary.html">net.yadaframework.cms.web</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/web/YadaCmsProductController.html#%3Cinit%3E()" class="member-name-link">YadaCmsProductController()</a> - Constructor for class net.yadaframework.cms.web.<a href="net/yadaframework/cms/web/YadaCmsProductController.html" title="class in net.yadaframework.cms.web">YadaCmsProductController</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" class="type-name-link" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a> - Class in <a href="net/yadaframework/cms/persistence/entity/package-summary.html">net.yadaframework.cms.persistence.entity</a></dt>
<dd>
<div class="block">Entity for a product dimension</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaDimension.html#%3Cinit%3E()" class="member-name-link">YadaDimension()</a> - Constructor for class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaDimension.html" title="class in net.yadaframework.cms.persistence.entity">YadaDimension</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" class="type-name-link" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a> - Class in <a href="net/yadaframework/cms/persistence/entity/package-summary.html">net.yadaframework.cms.persistence.entity</a></dt>
<dd>
<div class="block">Stores all the elements that can appear on a gallery slide: image, video, text...</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html#%3Cinit%3E()" class="member-name-link">YadaGallerySlide()</a> - Constructor for class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaGallerySlide.html" title="class in net.yadaframework.cms.persistence.entity">YadaGallerySlide</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" class="type-name-link" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a> - Class in <a href="net/yadaframework/cms/persistence/entity/package-summary.html">net.yadaframework.cms.persistence.entity</a></dt>
<dd>
<div class="block">A Product is an "abstract" item because it groups similar objects that differ in color, size or other attributes.</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#%3Cinit%3E()" class="member-name-link">YadaProduct()</a> - Constructor for class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/repository/YadaProductDao.html" class="type-name-link" title="class in net.yadaframework.cms.persistence.repository">YadaProductDao</a> - Class in <a href="net/yadaframework/cms/persistence/repository/package-summary.html">net.yadaframework.cms.persistence.repository</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/repository/YadaProductDao.html#%3Cinit%3E()" class="member-name-link">YadaProductDao()</a> - Constructor for class net.yadaframework.cms.persistence.repository.<a href="net/yadaframework/cms/persistence/repository/YadaProductDao.html" title="class in net.yadaframework.cms.persistence.repository">YadaProductDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaSortableEntity.html" class="type-name-link" title="interface in net.yadaframework.cms.persistence.entity">YadaSortableEntity</a> - Interface in <a href="net/yadaframework/cms/persistence/entity/package-summary.html">net.yadaframework.cms.persistence.entity</a></dt>
<dd>
<div class="block">Entities can be sorted visually (by showing a list of thumbnails) if they implement this interface.</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/repository/YadaWebCmsDao.html" class="type-name-link" title="class in net.yadaframework.cms.persistence.repository">YadaWebCmsDao</a> - Class in <a href="net/yadaframework/cms/persistence/repository/package-summary.html">net.yadaframework.cms.persistence.repository</a></dt>
<dd>
<div class="block">Miscellaneous CMS-related methods</div>
</dd>
<dt><a href="net/yadaframework/cms/persistence/repository/YadaWebCmsDao.html#%3Cinit%3E()" class="member-name-link">YadaWebCmsDao()</a> - Constructor for class net.yadaframework.cms.persistence.repository.<a href="net/yadaframework/cms/persistence/repository/YadaWebCmsDao.html" title="class in net.yadaframework.cms.persistence.repository">YadaWebCmsDao</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/components/YadaWebCmsHelper.html" class="type-name-link" title="class in net.yadaframework.cms.components">YadaWebCmsHelper</a> - Class in <a href="net/yadaframework/cms/components/package-summary.html">net.yadaframework.cms.components</a></dt>
<dd>
<div class="block">Helper for html fragments</div>
</dd>
<dt><a href="net/yadaframework/cms/components/YadaWebCmsHelper.html#%3Cinit%3E()" class="member-name-link">YadaWebCmsHelper()</a> - Constructor for class net.yadaframework.cms.components.<a href="net/yadaframework/cms/components/YadaWebCmsHelper.html" title="class in net.yadaframework.cms.components">YadaWebCmsHelper</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/YadaCmsConfiguration.html#yadaWebUtil" class="member-name-link">yadaWebUtil</a> - Variable in class net.yadaframework.cms.<a href="net/yadaframework/cms/YadaCmsConfiguration.html" title="class in net.yadaframework.cms">YadaCmsConfiguration</a></dt>
<dd>&nbsp;</dd>
<dt><a href="net/yadaframework/cms/persistence/entity/YadaProduct.html#year" class="member-name-link">year</a> - Variable in class net.yadaframework.cms.persistence.entity.<a href="net/yadaframework/cms/persistence/entity/YadaProduct.html" title="class in net.yadaframework.cms.persistence.entity">YadaProduct</a></dt>
<dd>&nbsp;</dd>
</dl>
<a href="#I:A">A</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:W">W</a>&nbsp;<a href="#I:Y">Y</a>&nbsp;<br><a href="allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="allpackages-index.html">All&nbsp;Packages</a><span class="vertical-separator">|</span><a href="serialized-form.html">Serialized&nbsp;Form</a></main>
</div>
</div>
</body>
</html>
