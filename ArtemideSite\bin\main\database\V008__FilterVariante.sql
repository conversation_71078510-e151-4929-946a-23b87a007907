create table FilterVariante (id bigint not null auto_increment, variante integer not null, subfamily_id bigint, primary key (id)) ENGINE=InnoDB;
create table FilterVarianteDescription (id bigint not null auto_increment, localeCode varchar(5), value longtext, FilterVariante_id bigint, primary key (id)) ENGINE=InnoDB;
create table FilterVariante_variants (FilterVariante_id bigint not null, variants integer) ENGINE=InnoDB;

alter table FilterVariante add index FK6DC80438FB787D17 (subfamily_id), add constraint FK6DC80438FB787D17 foreign key (subfamily_id) references Subfamily (id);
alter table FilterVarianteDescription add index FK1748D4A4F80FB71D (FilterVariante_id), add constraint FK1748D4A4F80FB71D foreign key (FilterVariante_id) references FilterVariante (id);
alter table FilterVariante_variants add index FKE3F76CB5F80FB71D (FilterVariante_id), add constraint FKE3F76CB5F80FB71D foreign key (FilterVariante_id) references FilterVariante (id);

alter table Subfamily add column beam bit not null, add column cct bit not null, add column color bit not null, add column controlType bit not null, add column cri bit not null, add column diameter bit not null, add column  flux bit not null, add column height bit not null, add column length bit not null, add column lightSource bit not null, add column power bit not null, add column typology bit not null;

create table Prodotto_variante (Prodotto_id bigint not null, variante integer) ENGINE=InnoDB;

alter table Prodotto_variante add index FKDE8200A8ADF2CDD (Prodotto_id), add constraint FKDE8200A8ADF2CDD foreign key (Prodotto_id) references Prodotto (id);

insert into Prodotto_variante (Prodotto_id,variante) select id,variante from Prodotto where not Prodotto.variante=-1;

alter table Prodotto drop variante;