<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaLookupTableFive (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.raw, class: YadaLookupTableFive">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.raw</a></div>
<h1 title="Class YadaLookupTableFive" class="title">Class YadaLookupTableFive&lt;K1,<wbr>K2,<wbr>K3,<wbr>K4,<wbr>V&gt;</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.raw.YadaLookupTableFive&lt;K1,<wbr>K2,<wbr>K3,<wbr>K4,<wbr>V&gt;</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>K1</code> - the type of column 1</dd>
<dd><code>K2</code> - the type of column 2</dd>
<dd><code>K3</code> - the type of column 3</dd>
<dd><code>K4</code> - the type of column 4</dd>
<dd><code>V</code> - the type of the value</dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaLookupTableFive&lt;K1,<wbr>K2,<wbr>K3,<wbr>K4,<wbr>V&gt;</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Implements a table with five columns: four keys and one value. The purpose is to return the fifth value given the first four ones.
 Can't have rows with the same keys.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaLookupTableFive</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clear()" class="member-name-link">clear</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Clear the table</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">V</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#get(K1,K2,K3,K4)" class="member-name-link">get</a><wbr>(<a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K1</a>&nbsp;key1,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K2</a>&nbsp;key2,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K3</a>&nbsp;key3,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K4</a>&nbsp;key4)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the value of the last column given the first ones</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaLookupTableFour.html" title="class in net.yadaframework.raw">YadaLookupTableFour</a><wbr>&lt;<a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K2</a>,<wbr><a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K3</a>,<wbr><a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K4</a>,<wbr><a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">V</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSubtable(K1)" class="member-name-link">getSubtable</a><wbr>(<a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K1</a>&nbsp;key1)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get a table of all the rows that match the first column value, excluding the first column.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#put(K1,K2,K3,K4,V)" class="member-name-link">put</a><wbr>(<a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K1</a>&nbsp;key1,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K2</a>&nbsp;key2,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K3</a>&nbsp;key3,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K4</a>&nbsp;key4,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">V</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new row to the table.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaLookupTableFive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaLookupTableFive</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="put(K1,K2,K3,K4,V)">
<h3 id="put(java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">put</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">put</span><wbr><span class="parameters">(<a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K1</a>&nbsp;key1,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K2</a>&nbsp;key2,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K3</a>&nbsp;key3,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K4</a>&nbsp;key4,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">V</a>&nbsp;value)</span></div>
<div class="block">Add a new row to the table. No parameter can be null.</div>
</section>
</li>
<li>
<section class="detail" id="get(K1,K2,K3,K4)">
<h3 id="get(java.lang.Object,java.lang.Object,java.lang.Object,java.lang.Object)">get</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">V</a></span>&nbsp;<span class="element-name">get</span><wbr><span class="parameters">(<a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K1</a>&nbsp;key1,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K2</a>&nbsp;key2,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K3</a>&nbsp;key3,
 <a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K4</a>&nbsp;key4)</span></div>
<div class="block">Get the value of the last column given the first ones</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>key1</code> - value of the first column</dd>
<dd><code>key2</code> - value of the second column</dd>
<dd><code>key3</code> - value of the third column</dd>
<dd><code>key4</code> - value of the fourth column</dd>
<dt>Returns:</dt>
<dd>the value of column 5, or null if not found.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSubtable(K1)">
<h3 id="getSubtable(java.lang.Object)">getSubtable</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaLookupTableFour.html" title="class in net.yadaframework.raw">YadaLookupTableFour</a>&lt;<a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K2</a>,<wbr><a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K3</a>,<wbr><a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K4</a>,<wbr><a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">V</a>&gt;</span>&nbsp;<span class="element-name">getSubtable</span><wbr><span class="parameters">(<a href="YadaLookupTableFive.html" title="type parameter in YadaLookupTableFive">K1</a>&nbsp;key1)</span></div>
<div class="block">Get a table of all the rows that match the first column value, excluding the first column.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>key1</code> - the value of the first column</dd>
<dt>Returns:</dt>
<dd>a table of the following columns for the given value of the first column, or null if not found.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="clear()">
<h3>clear</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clear</span>()</div>
<div class="block">Clear the table</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
