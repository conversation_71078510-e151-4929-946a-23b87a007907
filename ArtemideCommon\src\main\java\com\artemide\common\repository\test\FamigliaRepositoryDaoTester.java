package com.artemide.common.repository.test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.repository.FamigliaRepoDao;
import com.artemide.common.repository.FamigliaRepository;
import com.yr.entity.Famiglia;
import com.yr.entity.Subfamily;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies FamigliaRepoDao behaves exactly like FamigliaRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class FamigliaRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private FamigliaRepository famigliaRepository;

    @Autowired
    private FamigliaRepoDao famigliaRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    Famiglia entity = entityManager.find(Famiglia.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            // Since Famiglia uses a Map for names, we need to use a different approach for cleanup
            // We'll delete by ID range or use a more complex query
            entityManager.createQuery("DELETE FROM Famiglia f WHERE f.id IN (SELECT f2.id FROM Famiglia f2 JOIN f2.name n WHERE KEY(n) = :locale AND VALUE(n) LIKE 'TEST_%' OR VALUE(n) LIKE 'INTEGRATION_%')")
                .setParameter("locale", java.util.Locale.ENGLISH)
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private Famiglia createTestEntity(String name) {
        Famiglia entity = new Famiglia();
        // Famiglia uses a Map<Locale, String> for names, so we need to create the map
        Map<java.util.Locale, String> nameMap = new HashMap<>();
        nameMap.put(java.util.Locale.ENGLISH, name);
        entity.setName(nameMap);
        entity.setPublished(true);
        return entity;
    }

    private void trackEntity(Famiglia entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = famigliaRepository.count();
            long initialDaoCount = famigliaRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            Famiglia testEntity = createTestEntity("INTEGRATION_SAVE_001");

            Famiglia repoSaved = famigliaRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            Famiglia testEntity2 = createTestEntity("INTEGRATION_SAVE_002");
            Famiglia daoSaved = famigliaRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<Famiglia> repoFound = famigliaRepository.findById(repoSaved.getId());
            Optional<Famiglia> daoFoundOptional = famigliaRepoDao.findById(daoSaved.getId());
            Famiglia daoFoundDirect = famigliaRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<Famiglia> repoResults = famigliaRepository.findAll();
            List<Famiglia> daoResults = famigliaRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            Famiglia entityForRepo = createTestEntity("INTEGRATION_DELETE_001");
            Famiglia entityForDao = createTestEntity("INTEGRATION_DELETE_002");
            
            Famiglia savedForRepo = famigliaRepository.save(entityForRepo);
            Famiglia savedForDao = famigliaRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!famigliaRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!famigliaRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            famigliaRepository.delete(savedForRepo);
            famigliaRepoDao.delete(savedForDao);
            entityManager.flush();

            if (famigliaRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (famigliaRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindBySubfamilyId() {
        setUp();
        try {
            // Get first available subfamily ID using DbUtil
            java.util.Optional<Long> subfamilyIdOpt = DbUtil.findFirstId(entityManager, Subfamily.class);
            if (!subfamilyIdOpt.isPresent()) {
                return "SKIP: No subfamily records found in database for testing";
            }
            Long testSubfamilyId = subfamilyIdOpt.get();
            
            Long repoResult = famigliaRepository.findBySubfamilyId(testSubfamilyId);
            Long daoResult = famigliaRepoDao.findBySubfamilyId(testSubfamilyId);

            if ((repoResult == null && daoResult != null) || (repoResult != null && !repoResult.equals(daoResult))) {
                return "FAIL: findBySubfamilyId results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: findBySubfamilyId() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindPage() {
        setUp();
        try {
            // Test findPage
            List<Famiglia> repoPage = famigliaRepository.findPage(0, 5);
            List<Famiglia> daoPage = famigliaRepoDao.findPage(0, 5);

            if (repoPage.size() != daoPage.size()) {
                return "FAIL: findPage results don't match - Repository: " + repoPage.size() + ", DAO: " + daoPage.size();
            }
            
            return "PASS: findPage() test successful";
        } finally {
            tearDown();
        }
    }

    public String testHasMagneticSubfamily() {
        setUp();
        try {
            // Get first available famiglia ID using DbUtil
            java.util.Optional<Long> famigliaIdOpt = DbUtil.findFirstId(entityManager, Famiglia.class);
            if (!famigliaIdOpt.isPresent()) {
                return "SKIP: No famiglia records found in database for testing";
            }
            long testFamigliaId = famigliaIdOpt.get();
            
            int repoResult = famigliaRepository.hasMagneticSubfamily(testFamigliaId);
            int daoResult = famigliaRepoDao.hasMagneticSubfamily(testFamigliaId);

            if (repoResult != daoResult) {
                return "FAIL: hasMagneticSubfamily results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: hasMagneticSubfamily() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindProductTipologies() {
        setUp();
        try {
            // Get first available famiglia ID using DbUtil
            java.util.Optional<Long> famigliaIdOpt = DbUtil.findFirstId(entityManager, Famiglia.class);
            if (!famigliaIdOpt.isPresent()) {
                return "SKIP: No famiglia records found in database for testing";
            }
            long testFamigliaId = famigliaIdOpt.get();
            
            List<Integer> repoResult = famigliaRepository.findProductTipologies(testFamigliaId);
            List<Integer> daoResult = famigliaRepoDao.findProductTipologies(testFamigliaId);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findProductTipologies results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findProductTipologies() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindName() {
        setUp();
        try {
            // Get first available famiglia ID using DbUtil
            java.util.Optional<Long> famigliaIdOpt = DbUtil.findFirstId(entityManager, Famiglia.class);
            if (!famigliaIdOpt.isPresent()) {
                return "SKIP: No famiglia records found in database for testing";
            }
            Long testFamigliaId = famigliaIdOpt.get();
            
            String repoResult = famigliaRepository.findName(testFamigliaId, "en_US");
            String daoResult = famigliaRepoDao.findName(testFamigliaId, "en_US");

            if ((repoResult == null && daoResult != null) || (repoResult != null && !repoResult.equals(daoResult))) {
                return "FAIL: findName results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: findName() test successful";
        } finally {
            tearDown();
        }
    }

    public String testIsFamigliaPublished() {
        setUp();
        try {
            // Get first available famiglia ID using DbUtil
            java.util.Optional<Long> famigliaIdOpt = DbUtil.findFirstId(entityManager, Famiglia.class);
            if (!famigliaIdOpt.isPresent()) {
                return "SKIP: No famiglia records found in database for testing";
            }
            Long testFamigliaId = famigliaIdOpt.get();
            
            boolean repoResult = famigliaRepository.isFamigliaPublished(testFamigliaId);
            boolean daoResult = famigliaRepoDao.isFamigliaPublished(testFamigliaId);

            if (repoResult != daoResult) {
                return "FAIL: isFamigliaPublished results don't match - Repository: " + repoResult + ", DAO: " + daoResult;
            }
            
            return "PASS: isFamigliaPublished() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== FamigliaRepository vs FamigliaRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String findBySubfamilyIdResult = testFindBySubfamilyId();
            report.append("5. FindBySubfamilyId Test: ").append(findBySubfamilyIdResult).append("\n");
            
            String findPageResult = testFindPage();
            report.append("6. FindPage Test: ").append(findPageResult).append("\n");
            
            String hasMagneticSubfamilyResult = testHasMagneticSubfamily();
            report.append("7. HasMagneticSubfamily Test: ").append(hasMagneticSubfamilyResult).append("\n");
            
            String findProductTipologiesResult = testFindProductTipologies();
            report.append("8. FindProductTipologies Test: ").append(findProductTipologiesResult).append("\n");
            
            String findNameResult = testFindName();
            report.append("9. FindName Test: ").append(findNameResult).append("\n");
            
            String isFamigliaPublishedResult = testIsFamigliaPublished();
            report.append("10. IsFamigliaPublished Test: ").append(isFamigliaPublishedResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - FamigliaRepoDao behaves exactly like FamigliaRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
