<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Deprecated List (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="deprecated elements">
<meta name="generator" content="javadoc/DeprecatedListWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="deprecated-list-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="nav-bar-cell1-rev">Deprecated</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#deprecated">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Deprecated API" class="title">Deprecated API</h1>
</div>
<h2 title="Contents">Contents</h2>
<ul class="contents-list">
<li id="contents-method"><a href="#method">Methods</a></li>
</ul>
<ul class="block-list">
<li>
<div id="method">
<div class="caption"><span>Deprecated Methods</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#extendAutologinLink(java.lang.String,java.lang.String)">net.yadaframework.security.components.YadaSecurityEmailService.extendAutologinLink<wbr>(String, String)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#makeAutologinLink(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials,java.lang.String)">net.yadaframework.security.components.YadaSecurityEmailService.makeAutologinLink<wbr>(String, YadaUserCredentials, String)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#makeAutologinLink(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials,java.util.Date,java.lang.String,jakarta.servlet.http.HttpServletRequest)">net.yadaframework.security.components.YadaSecurityEmailService.makeAutologinLink<wbr>(String, YadaUserCredentials, Date, String, HttpServletRequest)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/components/YadaSecurityEmailService.html#makeAutologinLink(java.lang.String,net.yadaframework.security.persistence.entity.YadaUserCredentials,java.util.Date,java.lang.String,java.lang.String)">net.yadaframework.security.components.YadaSecurityEmailService.makeAutologinLink<wbr>(String, YadaUserCredentials, Date, String, String)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/components/YadaSecurityUtil.html#hasCurrentRole(java.lang.String%5B%5D)">net.yadaframework.security.components.YadaSecurityUtil.hasCurrentRole<wbr>(String[])</a></div>
<div class="col-last even-row-color">
<div class="block">use <a href="net/yadaframework/security/components/YadaSecurityUtil.html#hasAnyRole(java.lang.String...)"><code>YadaSecurityUtil.hasAnyRole(String...)</code></a> instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/components/YadaSecurityUtil.html#loggedIn()">net.yadaframework.security.components.YadaSecurityUtil.loggedIn()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/components/YadaSecurityUtil.html#setRolesWhenAllowed(net.yadaframework.security.persistence.entity.YadaUserProfile,java.util.List,java.util.List)">net.yadaframework.security.components.YadaSecurityUtil.setRolesWhenAllowed<wbr>(YadaUserProfile, List&lt;Integer&gt;, List&lt;Integer&gt;)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/components/YadaUserDetailsService.html#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials)">net.yadaframework.security.components.YadaUserDetailsService.authenticateAs<wbr>(YadaUserCredentials)</a></div>
<div class="col-last odd-row-color">
<div class="block">because for Spring 5</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/components/YadaUserDetailsService.html#authenticateAs(net.yadaframework.security.persistence.entity.YadaUserCredentials,boolean)">net.yadaframework.security.components.YadaUserDetailsService.authenticateAs<wbr>(YadaUserCredentials, boolean)</a></div>
<div class="col-last even-row-color">
<div class="block">because for Spring 5 we need Request and Response</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getTimezone()">net.yadaframework.security.persistence.entity.YadaRegistrationRequest.getTimezone()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#getTrattamentoDati()">net.yadaframework.security.persistence.entity.YadaRegistrationRequest.getTrattamentoDati()</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#isTrattamentoDatiAccepted()">net.yadaframework.security.persistence.entity.YadaRegistrationRequest.isTrattamentoDatiAccepted()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setTrattamentoDati(net.yadaframework.persistence.entity.YadaClause)">net.yadaframework.security.persistence.entity.YadaRegistrationRequest.setTrattamentoDati<wbr>(YadaClause)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html#setTrattamentoDatiAccepted(boolean)">net.yadaframework.security.persistence.entity.YadaRegistrationRequest.setTrattamentoDatiAccepted<wbr>(boolean)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#changePassword(java.lang.String,org.springframework.security.crypto.password.PasswordEncoder)">net.yadaframework.security.persistence.entity.YadaUserCredentials.changePassword<wbr>(String, PasswordEncoder)</a></div>
<div class="col-last even-row-color">
<div class="block">because it arbitrarily clears the changePassword flag</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html#setRole(java.lang.Integer)">net.yadaframework.security.persistence.entity.YadaUserCredentials.setRole<wbr>(Integer)</a></div>
<div class="col-last odd-row-color">
<div class="block">@see #setOnlyRole(Integer)</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#findByUsername(java.lang.String)">net.yadaframework.security.persistence.repository.YadaUserCredentialsDao.findByUsername<wbr>(String)</a></div>
<div class="col-last even-row-color">
<div class="block">because there can be no more than one result</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#findByUsername(java.lang.String,net.yadaframework.web.YadaPageRequest)">net.yadaframework.security.persistence.repository.YadaUserCredentialsDao.findByUsername<wbr>(String, YadaPageRequest)</a></div>
<div class="col-last odd-row-color">
<div class="block">because there can be no more than one result</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/persistence/repository/YadaUserCredentialsDao.html#save(net.yadaframework.security.persistence.entity.YadaUserCredentials)">net.yadaframework.security.persistence.repository.YadaUserCredentialsDao.save<wbr>(YadaUserCredentials)</a></div>
<div class="col-last even-row-color">
<div class="block">use a higher-level method instead</div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/web/YadaLoginController.html#ajaxLoginForm(org.springframework.ui.Model)">net.yadaframework.security.web.YadaLoginController.ajaxLoginForm<wbr>(Model)</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/web/YadaLoginController.html#ajaxLoginOk(org.springframework.ui.Model)">net.yadaframework.security.web.YadaLoginController.ajaxLoginOk<wbr>(Model)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/web/YadaLoginController.html#loginModal()">net.yadaframework.security.web.YadaLoginController.loginModal()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/web/YadaRegistrationController.html#passwordChangeModal(java.lang.String,java.lang.String,net.yadaframework.web.form.YadaFormPasswordChange)">net.yadaframework.security.web.YadaRegistrationController.passwordChangeModal<wbr>(String, String, YadaFormPasswordChange)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/web/YadaSession.html#depersonate()">net.yadaframework.security.web.YadaSession.depersonate()</a></div>
<div class="col-last odd-row-color">
<div class="block">Use 
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#deimpersonate()</code></pre>
</details>
 instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/web/YadaSession.html#depersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)">net.yadaframework.security.web.YadaSession.depersonate<wbr>(HttpServletRequest, HttpServletResponse)</a></div>
<div class="col-last even-row-color">
<div class="block">Use <a href="net/yadaframework/security/web/YadaSession.html#deimpersonate(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)"><code><span class="invalid-tag">invalid input: 'instead'</span></code></a></div>
</div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/web/YadaSession.html#depersonify()">net.yadaframework.security.web.YadaSession.depersonify()</a></div>
<div class="col-last odd-row-color">
<div class="block">Use 
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#deimpersonate()</code></pre>
</details>
 instead.</div>
</div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/web/YadaSession.html#impersonify(java.lang.Long)">net.yadaframework.security.web.YadaSession.impersonify<wbr>(Long)</a></div>
<div class="col-last even-row-color"></div>
<div class="col-summary-item-name odd-row-color"><a href="net/yadaframework/security/web/YadaSession.html#isImpersonificationActive()">net.yadaframework.security.web.YadaSession.isImpersonificationActive()</a></div>
<div class="col-last odd-row-color"></div>
<div class="col-summary-item-name even-row-color"><a href="net/yadaframework/security/web/YadaSession.html#isLoggedUser(T)">net.yadaframework.security.web.YadaSession.isLoggedUser<wbr>(T)</a></div>
<div class="col-last even-row-color">
<div class="block">use <a href="net/yadaframework/security/web/YadaSession.html#isLoggedInUser(T)"><code>YadaSession.isLoggedInUser(YadaUserProfile)</code></a> instead</div>
</div>
</div>
</div>
</li>
</ul>
<script type="text/javascript">document.addEventListener("DOMContentLoaded", function(e) {
    document.querySelectorAll('input[type="checkbox"]').forEach(
        function(c) {
            c.disabled = false;
            c.onclick();
        });
    });
window.addEventListener("load", function(e) {
    document.querySelectorAll('input[type="checkbox"]').forEach(
        function(c) {
            c.onclick();
        });
    });
</script>
</main>
</div>
</div>
</body>
</html>
