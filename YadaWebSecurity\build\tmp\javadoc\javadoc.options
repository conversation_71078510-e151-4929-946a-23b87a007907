-charset 'UTF-8'
-classpath 'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\build\\classes\\java\\main;C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\build\\resources\\main;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.thymeleaf.extras\\thymeleaf-extras-springsecurity6\\3.1.3.RELEASE\\f2a9e9505b145d22a52047460c74fb8200f2cb23\\thymeleaf-extras-springsecurity6-3.1.3.RELEASE.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.api-client\\google-api-client-gson\\2.8.1\\d4f3440ca08fc906b8039c24cf2e0a82871f4cdf\\google-api-client-gson-2.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework.security\\spring-security-web\\6.5.5\\53362443076c948cf1fbdd078802ef58917b5938\\spring-security-web-6.5.5.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework.security\\spring-security-config\\6.5.5\\e647f8334d2d4578f7b553e705a409017ac37be1\\spring-security-config-6.5.5.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\jakarta.servlet\\jakarta.servlet-api\\6.1.0\\1169a246913fe3823782af7943e7a103634867c5\\jakarta.servlet-api-6.1.0.jar;C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWeb\\build\\classes\\java\\main;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\jakarta.annotation\\jakarta.annotation-api\\3.0.0\\54f928fadec906a99d558536756d171917b9d936\\jakarta.annotation-api-3.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.thymeleaf\\thymeleaf-spring6\\3.1.3.RELEASE\\4b276ea2bd536a18e44b40ff1d9f4848965ff59c\\thymeleaf-spring6-3.1.3.RELEASE.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\ch.qos.logback\\logback-classic\\1.5.18\\fc371f3fc97a639de2d67947cffb7518ec5e3d40\\logback-classic-1.5.18.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.vibur\\vibur-dbcp\\25.0\\f03f9159bf6e284bef3090797005f1e426a1ca37\\vibur-dbcp-25.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.thymeleaf\\thymeleaf\\3.1.3.RELEASE\\51474f2a90b282ee97dabcd159c7faf24790f373\\thymeleaf-3.1.3.RELEASE.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.slf4j\\slf4j-api\\2.0.17\\d9e58ac9c7779ba3bf8142aff6c830617a7fe60f\\slf4j-api-2.0.17.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.api-client\\google-api-client\\2.8.1\\926bf96b546311d4ce18672d00bc8766ecfd72d1\\google-api-client-2.8.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.oauth-client\\google-oauth-client\\1.36.0\\dc3f07bc8f49dd52fe8fcc15958f3cfeb003e20f\\google-oauth-client-1.36.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.auth\\google-auth-library-oauth2-http\\1.30.0\\3e7b8091fbcbbdbc9dcf93a6d09f62052620a3c2\\google-auth-library-oauth2-http-1.30.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.http-client\\google-http-client-gson\\2.0.0\\59b964f378897f070d1eb3052a7207583d190536\\google-http-client-gson-2.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.http-client\\google-http-client-apache-v2\\2.0.0\\9d7ec2f47541ac747bce141c0d04b07c01a9f820\\google-http-client-apache-v2-2.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.http-client\\google-http-client\\2.0.0\\bcb2279b29d541ab39439b244feeef2bb9feacd8\\google-http-client-2.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework.security\\spring-security-core\\6.5.5\\b4010d71d6b53cae574ba3d5c15629ed782f3318\\spring-security-core-6.5.5.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-context-support\\6.2.11\\b092cbb9c21eab6fbbbf57b21eaef8da6d3a96a6\\spring-context-support-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-webmvc\\6.2.11\\3313fe95e06c1d3c94d00301cbed56ed1a9d2a86\\spring-webmvc-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-context\\6.2.11\\bbfb4d55385b2b65ecaf04937d209cf467eaba2f\\spring-context-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-aop\\6.2.11\\1e970383810700506d646c2fa8943725f123f000\\spring-aop-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-web\\6.2.11\\e590ac3e1aaea2d34a5ccb5c25ad84acbada8468\\spring-web-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-orm\\6.2.11\\d14df2d6ecddd2984ef5590d73c5178d7df25c11\\spring-orm-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-jdbc\\6.2.11\\8aadfc8bed1630563c65bb5628e2175ecd508998\\spring-jdbc-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-tx\\6.2.11\\f2b8bef65f6682019002c12a628886637e596261\\spring-tx-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-beans\\6.2.11\\8d92eb837b70094d7316f6a07770eefd360bc53c\\spring-beans-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-expression\\6.2.11\\7190c1c69576516efef1a061d956dccf17cb0b86\\spring-expression-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-core\\6.2.11\\f4860eb6ea92abb8ae6c5e82e2e7efc395cef8d\\spring-core-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.commons\\commons-configuration2\\2.12.0\\cab81350c85ca35db8c47621a19dcddf128883f\\commons-configuration2-2.12.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.commons\\commons-text\\1.14.0\\adcb0d4c67eabc79682604b47eb852aaff21138a\\commons-text-1.14.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.commons\\commons-lang3\\3.18.0\\fb14946f0e39748a6571de0635acbe44e7885491\\commons-lang3-3.18.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\commons-jxpath\\commons-jxpath\\1.4.0\\f61af30d8db727b7bb53d285fe074f89d8b68951\\commons-jxpath-1.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.hibernate.orm\\hibernate-core\\7.1.1.Final\\ef758e23453f219771a7fdb020f2fd047ca830bc\\hibernate-core-7.1.1.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.hibernate.validator\\hibernate-validator\\9.0.1.Final\\28c0e41ecc84d1f0b8b6d81d16e0784e63364e68\\hibernate-validator-9.0.1.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.mysql\\mysql-connector-j\\9.4.0\\8f6c66269048fbd2316b7b45d75898ebee44986f\\mysql-connector-j-9.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\commons-beanutils\\commons-beanutils\\1.11.0\\ac03ea606d13de04c2e4508227680faff151f491\\commons-beanutils-1.11.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\commons-collections\\commons-collections\\3.2.2\\8ad72fe39fa8c91eaaf12aadb21e0c3661fe26d5\\commons-collections-3.2.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.commons\\commons-exec\\1.3\\8dfb9facd0830a27b1b5f29f84593f0aeee7773b\\commons-exec-1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.sun.mail\\jakarta.mail\\2.0.2\\6dfe5d279fb579a41baa84a7728e31a40a50d90e\\jakarta.mail-2.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opencensus\\opencensus-contrib-http-util\\0.31.1\\3c13fc5715231fadb16a9b74a44d9d59c460cfa8\\opencensus-contrib-http-util-0.31.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\guava\\33.4.8-android\\e70a3268e6cd3e7d458aa15787ce6811c34e96ae\\guava-33.4.8-jre.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jsoup\\jsoup\\1.20.1\\769377896610be1736f8d6d51fc52a6042d1ce82\\jsoup-1.20.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.fasterxml.jackson.core\\jackson-annotations\\2.19.2\\c5381f11988ae3d424b197a26087d86067b6d7d\\jackson-annotations-2.19.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.fasterxml.jackson.core\\jackson-core\\2.19.2\\50f3b4bd59b9ff51a0ed493e7b5abaf5c39709bf\\jackson-core-2.19.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.flywaydb\\flyway-mysql\\11.13.1\\d514f7ab22e9373f6a2bee4a19985a1cff3e170c\\flyway-mysql-11.13.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.flywaydb\\flyway-core\\11.13.1\\3d44db5cf1ddb86b682c5f961675fe8606b7b0d5\\flyway-core-11.13.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.fasterxml.jackson.core\\jackson-databind\\2.19.2\\46509399d28f57ca32c6bb4b0d4e10e8f062051e\\jackson-databind-2.19.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.drewnoakes\\metadata-extractor\\2.19.0\\4d3ab42dd9965b8f2b8bd1b7572e028d6f90a34f\\metadata-extractor-2.19.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.httpcomponents\\httpclient\\4.5.14\\1194890e6f56ec29177673f2f12d0b8e627dec98\\httpclient-4.5.14.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\commons-codec\\commons-codec\\1.17.1\\973638b7149d333563584137ebf13a691bb60579\\commons-codec-1.17.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.auth\\google-auth-library-credentials\\1.30.0\\30f48b5459b931c7d2692970fae23121b598dad7\\google-auth-library-credentials-1.30.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.apache.httpcomponents\\httpcore\\4.4.16\\51cf043c87253c9f58b539c9f7e44c8894223850\\httpcore-4.4.16.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.code.findbugs\\jsr305\\3.0.2\\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\\jsr305-3.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.errorprone\\error_prone_annotations\\2.36.0\\227d4d4957ccc3dc5761bd897e3a0ee587e750a7\\error_prone_annotations-2.36.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.j2objc\\j2objc-annotations\\3.0.0\\7399e65dd7e9ff3404f4535b2f017093bdb134c7\\j2objc-annotations-3.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.opencensus\\opencensus-api\\0.31.1\\66a60c7201c2b8b20ce495f0295b32bb0ccbbc57\\opencensus-api-0.31.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.grpc\\grpc-context\\1.70.0\\375570c198b9ff46f2202731d11271bb5cb396fa\\grpc-context-1.70.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.code.gson\\gson\\2.11.0\\527175ca6d81050b53bdd4c457a6d6e017626b0e\\gson-2.11.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework.security\\spring-security-crypto\\6.5.5\\83f9ebdd4706d23d697a7012aa946b64b54476f6\\spring-security-crypto-6.5.5.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.micrometer\\micrometer-observation\\1.14.11\\ad84118158e17027c5c31b7567df736c3edc9cce\\micrometer-observation-1.14.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.springframework\\spring-jcl\\6.2.11\\819fc7e968ac07d4b042be7cfb8d99c267142ac7\\spring-jcl-6.2.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\jakarta.persistence\\jakarta.persistence-api\\3.2.0\\bb75a113f3fa191c2c7ee7b206d8e674251b3129\\jakarta.persistence-api-3.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\jakarta.transaction\\jakarta.transaction-api\\2.0.1\\51a520e3fae406abb84e2e1148e6746ce3f80a1a\\jakarta.transaction-api-2.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\jakarta.validation\\jakarta.validation-api\\3.1.1\\ec8622148afc5564235d17af80ea80288d0e7f92\\jakarta.validation-api-3.1.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jboss.logging\\jboss-logging\\3.6.1.Final\\886afbb445b4016a37c8960a7aef6ebd769ce7e5\\jboss-logging-3.6.1.Final.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.fasterxml\\classmate\\1.7.0\\e98374da1f2143ac8e6e0a95036994bb19137a3\\classmate-1.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.protobuf\\protobuf-java\\4.31.1\\1828b20315b63d5f71b3c61b094494a8f1acdc5a\\protobuf-java-4.31.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.sun.activation\\jakarta.activation\\2.0.1\\828b80e886a52bb09fe41ff410b10b342f533ce1\\jakarta.activation-2.0.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\failureaccess\\1.0.3\\aeaffd00d57023a2c947393ed251f0354f0985fc\\failureaccess-1.0.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.guava\\listenablefuture\\9999.0-empty-to-avoid-conflict-with-guava\\b421526c5f297295adef1c886e5246c39d4ac629\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.jspecify\\jspecify\\1.0.0\\7425a601c1c7ec76645a78d22b8c6a627edee507\\jspecify-1.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\ch.qos.logback\\logback-core\\1.5.18\\6c0375624f6f36b4e089e2488ba21334a11ef13f\\logback-core-1.5.18.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.adobe.xmp\\xmpcore\\6.1.11\\852f14101381e527e6d43339d7db1698c970436c\\xmpcore-6.1.11.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.vibur\\vibur-object-pool\\25.0\\8412049bfba1a65acf239efe74da811397f668c7\\vibur-object-pool-25.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.attoparser\\attoparser\\2.0.7.RELEASE\\e5d0e988d9124139d645bb5872b24dfa23e283cc\\attoparser-2.0.7.RELEASE.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.unbescape\\unbescape\\1.1.6.RELEASE\\7b90360afb2b860e09e8347112800d12c12b2a13\\unbescape-1.1.6.RELEASE.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.auto.value\\auto-value-annotations\\1.11.0\\f0d047931d07cfbc6fa4079854f181ff62891d6f\\auto-value-annotations-1.11.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\commons-logging\\commons-logging\\1.2\\4bfc12adfe4842bf07b657f0369c4cb522955686\\commons-logging-1.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\io.micrometer\\micrometer-commons\\1.14.11\\451c18c4a48cfb74973fb29ec6b9339fc29332f0\\micrometer-commons-1.14.11.jar'
-d 'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\build\\docs\\javadoc'
-docencoding 'UTF-8'
-doctitle 'YadaWebSecurity \'0.7.8\' API'
-encoding 'UTF-8'
-notimestamp 
-quiet 
-windowtitle 'YadaWebSecurity \'0.7.8\' API'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\AuditFilter.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\CheckSessionFilter.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\components\\YadaAuthenticationFailureHandler.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\components\\YadaAuthenticationSuccessFilter.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\components\\YadaAuthenticationSuccessHandler.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\components\\YadaLogoutSuccessHandler.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\components\\YadaSecurityBeans.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\components\\YadaSecurityEmailService.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\components\\YadaSecurityUtil.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\components\\YadaTokenHandler.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\components\\YadaUserDetailsService.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\components\\YadaUserSetup.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\exceptions\\InternalAuthenticationException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\exceptions\\YadaInvalidUserException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaAutoLoginToken.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaCommentMessage.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaRegistrationRequest.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaSocialCredentials.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaTicket.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaTicketMessage.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaTicketStatus.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaTicketType.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaUserCredentials.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaUserMessage.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaUserMessageType.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\entity\\YadaUserProfile.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\repository\\YadaAutoLoginTokenDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\repository\\YadaRegistrationRequestDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\repository\\YadaSocialCredentialsDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\repository\\YadaTicketDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\repository\\YadaTicketMessageDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\repository\\YadaUserCredentialsDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\repository\\YadaUserMessageDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\persistence\\repository\\YadaUserProfileDao.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\SecurityWebApplicationInitializer.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\TooManyFailedAttemptsException.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\web\\YadaActionUploadAttrProcessor.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\web\\YadaCropDefinition.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\web\\YadaDialectWithSecurity.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\web\\YadaLoginController.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\web\\YadaMiscController.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\web\\YadaRegistrationController.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\web\\YadaSession.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\web\\YadaSocialRegistrationData.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\YadaLocalePathRequestCache.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\YadaSecurityConfig.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\YadaWebSecurityConfig.java'
'C:\\work\\gits\\Artemide\\ArtemideResponsive3Project\\yadaframework\\YadaWebSecurity\\src\\main\\java\\net\\yadaframework\\security\\YadaWrappedSavedRequest.java'
