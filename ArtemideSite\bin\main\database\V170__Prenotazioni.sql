# Gestione prenotazioni per visite agli showroom
create table PrenotaSlot (id bigint not null auto_increment, capacity integer not null, sizeMinutes integer not null, start TIMESTAMP NULL, version bigint not null, prenotaStore_id bigint not null, primary key (id)) engine=InnoDB;
create table PrenotaStore (id bigint not null auto_increment, enabled bit not null, nameAndAddress varchar(255), version bigint not null, primary key (id)) engine=InnoDB;
create table PrenotaUser (id bigint not null auto_increment, email varchar(255), name varchar(255), people integer not null, phone varchar(255), profession varchar(255), surname varchar(255), version bigint not null, prenotaSlot_id bigint, primary key (id)) engine=InnoDB;

alter table PrenotaSlot add constraint UK3wk1wxx4t6bfvf4heqs9t5k2e unique (prenotaStore_id, start);
alter table PrenotaUser add constraint UKmi2igg6rsbb56spiciofx24k7 unique (email, prenotaSlot_id);

alter table PrenotaSlot add constraint FKoj55bskt5qtjyq4c0w7q9b8ht foreign key (prenotaStore_id) references PrenotaStore (id);
alter table PrenotaUser add constraint FK5gpa7sctserj18fxclrl22nof foreign key (prenotaSlot_id) references PrenotaSlot (id);
