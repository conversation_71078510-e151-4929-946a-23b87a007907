# Add a derived column for the sole use of sorting and searching in the dashboard
ALTER TABLE Store ADD promotions_sortable VARCHAR(255);

# Add an index for better sorting and searching
create index idx_store_promotions_sortable on Store (promotions_sortable);

# Initialize the promotions_sortable field with sorted, comma-separated promotion codes
UPDATE Store s
SET s.promotions_sortable = (
    SELECT GROUP_CONCAT(sp.promotion_code ORDER BY sp.promotion_code SEPARATOR ', ')
    FROM Store_promotions sp
    WHERE sp.Store_id = s.id
    GROUP BY sp.Store_id
);

# Set empty string for stores with no promotions
UPDATE Store
SET promotions_sortable = ''
WHERE promotions_sortable IS NULL;