# Canelllazoni delle vecchie tabelle dei Projects e aggiunta di quelle nuove. Tolgo anche scenario
Drop table ProjectAuthorTitle, ProjectDescription, ProjectLocation, ProjectProductName, ProjectProdotto, Project_SortedUploadedFiles, Project_Subfamily, Project_categorie, ScenarioImage, Project;

alter table PageModule ADD COLUMN project_id bigint after news_id;
create table Project (id bigint not null auto_increment, author varchar(255),scenarios bit not null, bespoken bit not null, changes varchar(255), enabled bit not null, photoby varchar(255), publishDate datetime, sartorial bit not null, selected bit not null, version bigint not null, year integer not null, bigImage_id bigint, smallImage_id bigint, primary key (id)) engine=InnoDB;
create table Project_location (Project_id bigint not null, location varchar(255), locale varchar(32) not null, primary key (Project_id, locale)) engine=InnoDB;
create table Project_title (Project_id bigint not null, title varchar(255), locale varchar(32) not null, primary key (Project_id, locale)) engine=InnoDB;
create table ProjectTag (id bigint not null auto_increment, pos bigint not null, version bigint not null, primary key (id)) engine=InnoDB;
create table ProjectTag_name (ProjectTag_id bigint not null, name varchar(255), locale varchar(32) not null, primary key (ProjectTag_id, locale)) engine=InnoDB;
create table ProjectTag_Project (tags_id bigint not null, project_id bigint not null) engine=InnoDB;

alter table ProjectTag_name add constraint UK3ldk543hj2d46i6vm706fi3s3 unique (name, locale);
alter table ProjectTag_Project add constraint UK9qgkbv3tvjm2qrwumwoef294f unique (tags_id, project_id);

alter table PageModule add constraint FK8peyknod1vgal2449jhma4vdf foreign key (project_id) references Project (id);
alter table Project add constraint FKlqpwkk1pptcdnor74g2eul41b foreign key (bigImage_id) references YadaAttachedFile (id);
alter table Project add constraint FK4s490p7bkrljqumiq8h63boec foreign key (smallImage_id) references YadaAttachedFile (id);
alter table Project_location add constraint FKjri72nues6ru6mh6ojebb5esv foreign key (Project_id) references Project (id);
alter table Project_title add constraint FK7f8si44c8l9n65kq3w08s0ot8 foreign key (Project_id) references Project (id);
alter table ProjectTag_name add constraint FKnlu4vnl6bb1l4owyuj3g8min5 foreign key (ProjectTag_id) references ProjectTag (id);
alter table ProjectTag_Project add constraint FKl8uryv5qearu9m8b69g26kbpn foreign key (project_id) references Project (id);
alter table ProjectTag_Project add constraint FK6po7owpvn3owxivr4sdtw8dyp foreign key (tags_id) references ProjectTag (id);
