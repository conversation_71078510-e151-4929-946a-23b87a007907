<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaCopyShallow (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.components, annotation type: YadaCopyShallow">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Field</li>
<li>Required</li>
<li>Optional</li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li>Element</li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Required&nbsp;|&nbsp;</li>
<li>Optional</li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Element</li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.components</a></div>
<h1 title="Annotation Interface YadaCopyShallow" class="title">Annotation Interface YadaCopyShallow</h1>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/annotation/Target.html" title="class or interface in java.lang.annotation" class="external-link">@Target</a>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/annotation/ElementType.html#FIELD" title="class or interface in java.lang.annotation" class="external-link">FIELD</a>)
<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/annotation/Retention.html" title="class or interface in java.lang.annotation" class="external-link">@Retention</a>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/annotation/RetentionPolicy.html#RUNTIME" title="class or interface in java.lang.annotation" class="external-link">RUNTIME</a>)
</span><span class="modifiers">public @interface </span><span class="element-name type-name-label">YadaCopyShallow</span></div>
<div class="block">Fields marked by this annotation are not recursively copied when cloning with YadaUtil, but copied by value,
 unless their value has been cloned in an ancestor object, in which case the cloned version will be used as a value.<br>
 <br>
 For example, if ProjectA contains ModuleA that has a project field referring to ProjectA with this annotation, then when
 cloning ModuleA the new ModuleB will keep a reference to ProjectA (shallow copy); when
 cloning ProjectA (the parent of ModuleA) the new ProjectB will contain a ModuleB with a reference to ProjectB (not
 to ProjectA because a clone of it has been made already).<br>
 <br>
 If this annotation is not used, it makes no difference for the latter case because ModuleB would get a reference to ProjectB
 that has already been cloned (there's a check to prevent infinite loops). 
 In the former case though, when cloning just ModuleA (and not its parent ProjectA) the cloned
 module ModuleB would get a reference to a clone of ProjectA and this probably not what you mean as you're not cloning the project.</div>
</section>
<section class="details" id="annotation-interface-element-detail"></section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
