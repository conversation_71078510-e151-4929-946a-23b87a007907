<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaFileManager (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.components, class: YadaFileManager">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.components</a></div>
<h1 title="Class YadaFileManager" class="title">Class YadaFileManager</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.components.YadaFileManager</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Service
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaFileManager</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">The File Manager handles uploaded files. They are kept in a specific folder where they can be
 chosen to be attached to entities.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#COUNTER_SEPARATOR" class="member-name-link">COUNTER_SEPARATOR</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#NOIMAGE_DATA" class="member-name-link">NOIMAGE_DATA</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaFileManager</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#attach(boolean,net.yadaframework.persistence.entity.YadaAttachedFile,java.io.File,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)" class="member-name-link">attach</a><wbr>(boolean&nbsp;move,
 <a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clientFilename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Performs file copy and (for images) resize to different versions.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#attachNew(boolean,java.io.File,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)" class="member-name-link">attachNew</a><wbr>(boolean&nbsp;move,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clientFilename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies (and resizes) a managed file to the destination folder, creating a database association to assign to an Entity.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#attachNew(java.io.File,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)" class="member-name-link">attachNew</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clientFilename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies (and resizes) a managed file to the destination folder, creating a database association to assign to an Entity.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#attachNew(java.io.File,org.springframework.web.multipart.MultipartFile,java.lang.String,java.lang.String)" class="member-name-link">attachNew</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies a managed file to the destination folder, creating a database association to assign to an Entity.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#attachNew(java.io.File,org.springframework.web.multipart.MultipartFile,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)" class="member-name-link">attachNew</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies (and resizes) a managed file to the destination folder, creating a database association to assign to an Entity.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#attachNew(org.springframework.web.multipart.MultipartFile,java.lang.String,java.lang.String)" class="member-name-link">attachNew</a><wbr>(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies an uploaded file to the destination folder, creating a database association to assign to an Entity.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#attachReplace(boolean,net.yadaframework.persistence.entity.YadaAttachedFile,java.io.File,org.springframework.web.multipart.MultipartFile,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)" class="member-name-link">attachReplace</a><wbr>(boolean&nbsp;move,
 <a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;currentAttachedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Replace the file associated with the current attachment, only if a file was actually attached</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#attachReplace(net.yadaframework.persistence.entity.YadaAttachedFile,java.io.File,org.springframework.web.multipart.MultipartFile,java.lang.String)" class="member-name-link">attachReplace</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;currentAttachedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Replace the file associated with the current attachment
 The managedFile is moved to the destination when config.isFileManagerDeletingUploads() is true, otherwise the original is copied
 and left unchanged.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#attachReplace(net.yadaframework.persistence.entity.YadaAttachedFile,java.io.File,org.springframework.web.multipart.MultipartFile,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)" class="member-name-link">attachReplace</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;currentAttachedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Replace the file associated with the current attachment, only if a file was actually attached
 The managedFile is moved to the destination when config.isFileManagerDeletingUploads() is true, otherwise the original is copied
 and left unchanged.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#attachReplace(net.yadaframework.persistence.entity.YadaAttachedFile,org.springframework.web.multipart.MultipartFile,java.lang.String)" class="member-name-link">attachReplace</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;currentAttachedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Replace the file associated with the current attachment
 The multipartFile is moved to the destination when config.isFileManagerDeletingUploads() is true, otherwise the original is copied
 and left unchanged.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#delete(net.yadaframework.persistence.entity.YadaManagedFile)" class="member-name-link">delete</a><wbr>(<a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a>&nbsp;managedFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Remove a managed file from disk and database</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#deleteFileAttachment(java.lang.Long)" class="member-name-link">deleteFileAttachment</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaAttachedFileId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteFileAttachment(java.util.List)" class="member-name-link">deleteFileAttachment</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&gt;&nbsp;yadaAttachedFiles)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Deletes from the filesystem all files related to the attachments</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteFileAttachment(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">deleteFileAttachment</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Deletes from the filesystem all files related to the attachment</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#duplicateFiles(net.yadaframework.persistence.entity.YadaAttachedFile,net.yadaframework.components.YadaAttachedFileCloneSet)" class="member-name-link">duplicateFiles</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFileCopy,
 <a href="YadaAttachedFileCloneSet.html" title="class in net.yadaframework.components">YadaAttachedFileCloneSet</a>&nbsp;yadaAttachedFileCloneSet)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">You don't usually need to call this method but <a href="YadaUtil.html#copyEntity(net.yadaframework.core.CloneableFiltered)"><code>YadaUtil.copyEntity(net.yadaframework.core.CloneableFiltered)</code></a> instead.<br>
 Makes a copy of just the filesystem files.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAbsoluteDesktopFile(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">getAbsoluteDesktopFile</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the absolute path of the desktop file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAbsoluteFile(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">getAbsoluteFile</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the absolute path of the default file (no mobile/desktop variant)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAbsoluteFile(net.yadaframework.persistence.entity.YadaAttachedFile,java.lang.String)" class="member-name-link">getAbsoluteFile</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the absolute path of a file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAbsoluteFile(net.yadaframework.persistence.entity.YadaManagedFile)" class="member-name-link">getAbsoluteFile</a><wbr>(<a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a>&nbsp;managedFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the absolute path of a managed file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAbsoluteMobileFile(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">getAbsoluteMobileFile</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the absolute path of the mobile file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAbsolutePdfFile(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">getAbsolutePdfFile</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the absolute path of the pdf file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDesktopImageUrl(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">getDesktopImageUrl</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the (relative) url of the desktop image.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFileUrl(java.lang.String,java.lang.String)" class="member-name-link">getFileUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the url of a YadaAttachedFile that has the given attributes</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFileUrl(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">getFileUrl</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the (relative) url of the file, or null.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMobileImageUrl(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">getMobileImageUrl</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the (relative) url of the mobile image if any, or null</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPdfImageUrl(net.yadaframework.persistence.entity.YadaAttachedFile)" class="member-name-link">getPdfImageUrl</a><wbr>(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the (relative) url of the pdf image if any, or null.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#manageFile(org.springframework.web.multipart.MultipartFile)" class="member-name-link">manageFile</a><wbr>(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies a received file to the upload folder.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#manageFile(org.springframework.web.multipart.MultipartFile,java.lang.String)" class="member-name-link">manageFile</a><wbr>(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;description)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies a received file to the upload folder.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#moveToTemp(net.yadaframework.persistence.entity.YadaManagedFile)" class="member-name-link">moveToTemp</a><wbr>(<a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a>&nbsp;yadaManagedFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Move the file to the public temp folder for later processing.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#uploadFile(org.springframework.web.multipart.MultipartFile)" class="member-name-link">uploadFile</a><wbr>(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies a received file to the upload folder.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="COUNTER_SEPARATOR">
<h3>COUNTER_SEPARATOR</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">COUNTER_SEPARATOR</span></div>
</section>
</li>
<li>
<section class="detail" id="NOIMAGE_DATA">
<h3>NOIMAGE_DATA</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">NOIMAGE_DATA</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../constant-values.html#net.yadaframework.components.YadaFileManager.NOIMAGE_DATA">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaFileManager</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaFileManager</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="moveToTemp(net.yadaframework.persistence.entity.YadaManagedFile)">
<h3>moveToTemp</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a></span>&nbsp;<span class="element-name">moveToTemp</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a>&nbsp;yadaManagedFile)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Move the file to the public temp folder for later processing.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaManagedFile</code> - </dd>
<dt>Returns:</dt>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="delete(net.yadaframework.persistence.entity.YadaManagedFile)">
<h3>delete</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">delete</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a>&nbsp;managedFile)</span></div>
<div class="block">Remove a managed file from disk and database</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>managedFile</code> - </dd>
<dt>Returns:</dt>
<dd>true when deleted from disk, false when not deleted from disk (could have been deleted from db though)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAbsoluteFile(net.yadaframework.persistence.entity.YadaManagedFile)">
<h3>getAbsoluteFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getAbsoluteFile</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a>&nbsp;managedFile)</span></div>
<div class="block">Returns the absolute path of a managed file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - the attachment</dd>
<dd><code>filename</code> - the relative file name, can be yadaAttachedFile.getFilename(), yadaAttachedFile.getFilenameDesktop(), yadaAttachedFile.getFilenameMobile()</dd>
<dt>Returns:</dt>
<dd>the File or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="duplicateFiles(net.yadaframework.persistence.entity.YadaAttachedFile,net.yadaframework.components.YadaAttachedFileCloneSet)">
<h3>duplicateFiles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">duplicateFiles</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFileCopy,
 <a href="YadaAttachedFileCloneSet.html" title="class in net.yadaframework.components">YadaAttachedFileCloneSet</a>&nbsp;yadaAttachedFileCloneSet)</span>
                                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">You don't usually need to call this method but <a href="YadaUtil.html#copyEntity(net.yadaframework.core.CloneableFiltered)"><code>YadaUtil.copyEntity(net.yadaframework.core.CloneableFiltered)</code></a> instead.<br>
 Makes a copy of just the filesystem files. New names are generated from the old ones by appending an incremental number.
 The input YadaAttachedFile is updated with the new names. The old files are not deleted.
 This method is used by YadaUtil.copyEntity() when a field is a YadaAttachedFile so that files are copied too.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFileCopy</code> - a copy of some other YadaAttachedFile that will be left unchanged</dd>
<dd><code>yadaAttachedFileCloneSet</code> - when not null, all files are copied to a temp folder. 
              This is useful when the final path depends on the id
                  of a cloned object so it can't be determined during cloning.
                  The method yadaAttachedFileCloneSet.moveAll() will have to be called after the clone has been persisted.</dd>
<dt>Returns:</dt>
<dd>the saved YadaAttachedFile with new files on disk, eventually in a different folder</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAbsoluteMobileFile(net.yadaframework.persistence.entity.YadaAttachedFile)">
<h3>getAbsoluteMobileFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getAbsoluteMobileFile</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</span></div>
<div class="block">Returns the absolute path of the mobile file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - the attachment</dd>
<dt>Returns:</dt>
<dd>the File or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAbsoluteDesktopFile(net.yadaframework.persistence.entity.YadaAttachedFile)">
<h3>getAbsoluteDesktopFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getAbsoluteDesktopFile</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</span></div>
<div class="block">Returns the absolute path of the desktop file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - the attachment</dd>
<dt>Returns:</dt>
<dd>the File or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAbsolutePdfFile(net.yadaframework.persistence.entity.YadaAttachedFile)">
<h3>getAbsolutePdfFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getAbsolutePdfFile</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</span></div>
<div class="block">Returns the absolute path of the pdf file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - the attachment</dd>
<dt>Returns:</dt>
<dd>the File or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAbsoluteFile(net.yadaframework.persistence.entity.YadaAttachedFile)">
<h3>getAbsoluteFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getAbsoluteFile</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</span></div>
<div class="block">Returns the absolute path of the default file (no mobile/desktop variant)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - the attachment</dd>
<dt>Returns:</dt>
<dd>the File or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getAbsoluteFile(net.yadaframework.persistence.entity.YadaAttachedFile,java.lang.String)">
<h3>getAbsoluteFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">getAbsoluteFile</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Returns the absolute path of a file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - the attachment</dd>
<dd><code>filename</code> - the relative file name, can be yadaAttachedFile.getFilename(), yadaAttachedFile.getFilenameDesktop(), yadaAttachedFile.getFilenameMobile()</dd>
<dt>Returns:</dt>
<dd>the File or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteFileAttachment(java.lang.Long)">
<h3>deleteFileAttachment</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">deleteFileAttachment</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaAttachedFileId)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Deletes from the filesystem all files related to the attachment</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFileId</code> - the attachment id</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#deleteFileAttachment(net.yadaframework.persistence.entity.YadaAttachedFile)"><code>deleteFileAttachment(YadaAttachedFile)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteFileAttachment(net.yadaframework.persistence.entity.YadaAttachedFile)">
<h3>deleteFileAttachment</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">deleteFileAttachment</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</span></div>
<div class="block">Deletes from the filesystem all files related to the attachment</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - the attachment</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#deleteFileAttachment(java.lang.Long)"><code>deleteFileAttachment(Long)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteFileAttachment(java.util.List)">
<h3>deleteFileAttachment</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">deleteFileAttachment</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&gt;&nbsp;yadaAttachedFiles)</span></div>
<div class="block">Deletes from the filesystem all files related to the attachments</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFiles</code> - the attachments</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMobileImageUrl(net.yadaframework.persistence.entity.YadaAttachedFile)">
<h3>getMobileImageUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getMobileImageUrl</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</span></div>
<div class="block">Returns the (relative) url of the mobile image if any, or null</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDesktopImageUrl(net.yadaframework.persistence.entity.YadaAttachedFile)">
<h3>getDesktopImageUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDesktopImageUrl</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</span></div>
<div class="block">Returns the (relative) url of the desktop image. If not defined, falls back to the plain file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPdfImageUrl(net.yadaframework.persistence.entity.YadaAttachedFile)">
<h3>getPdfImageUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPdfImageUrl</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</span></div>
<div class="block">Returns the (relative) url of the pdf image if any, or null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFileUrl(net.yadaframework.persistence.entity.YadaAttachedFile)">
<h3>getFileUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFileUrl</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile)</span></div>
<div class="block">Returns the (relative) url of the file, or null.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaAttachedFile</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFileUrl(java.lang.String,java.lang.String)">
<h3>getFileUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFileUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;filename)</span></div>
<div class="block">Returns the url of a YadaAttachedFile that has the given attributes</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>relativeFolderPath</code> - </dd>
<dd><code>filename</code> - </dd>
<dt>Returns:</dt>
<dd>The URL as a String</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="uploadFile(org.springframework.web.multipart.MultipartFile)">
<h3>uploadFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">uploadFile</span><wbr><span class="parameters">(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile)</span>
                throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copies a received file to the upload folder. The returned File is the only pointer to the uploaded file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>multipartFile</code> - file coming from the http request</dd>
<dt>Returns:</dt>
<dd>the uploaded file with a unique name, or null if the user did not send any file</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="manageFile(org.springframework.web.multipart.MultipartFile)">
<h3>manageFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a></span>&nbsp;<span class="element-name">manageFile</span><wbr><span class="parameters">(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copies a received file to the upload folder. A pointer to the file is stored in the database as YadaManagedFile</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>multipartFile</code> - file coming from the http request</dd>
<dt>Returns:</dt>
<dd>the uploaded file with a unique name, or null if the user did not send any file</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="manageFile(org.springframework.web.multipart.MultipartFile,java.lang.String)">
<h3>manageFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a></span>&nbsp;<span class="element-name">manageFile</span><wbr><span class="parameters">(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;description)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copies a received file to the upload folder. A pointer to the file is stored in the database as YadaManagedFile</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>multipartFile</code> - file coming from the http request</dd>
<dd><code>description</code> - a user description for the file</dd>
<dt>Returns:</dt>
<dd>the uploaded file with a unique name, or null if the user did not send any file</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="attachReplace(net.yadaframework.persistence.entity.YadaAttachedFile,org.springframework.web.multipart.MultipartFile,java.lang.String)">
<h3>attachReplace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">attachReplace</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;currentAttachedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix)</span>
                               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Replace the file associated with the current attachment
 The multipartFile is moved to the destination when config.isFileManagerDeletingUploads() is true, otherwise the original is copied
 and left unchanged.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>currentAttachedFile</code> - an existing attachment, never null</dd>
<dd><code>multipartFile</code> - the original uploaded file, to get the client filename. If null, the client filename is not changed.</dd>
<dt>Returns:</dt>
<dd>YadaAttachedFile if the file is uploaded, null if no file was sent by the user</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="attachReplace(net.yadaframework.persistence.entity.YadaAttachedFile,java.io.File,org.springframework.web.multipart.MultipartFile,java.lang.String)">
<h3>attachReplace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">attachReplace</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;currentAttachedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix)</span>
                               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Replace the file associated with the current attachment
 The managedFile is moved to the destination when config.isFileManagerDeletingUploads() is true, otherwise the original is copied
 and left unchanged.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>currentAttachedFile</code> - an existing attachment, never null</dd>
<dd><code>managedFile</code> - the new file to set</dd>
<dd><code>multipartFile</code> - the original uploaded file, to get the client filename. If null, the client filename is not changed.</dd>
<dt>Returns:</dt>
<dd>YadaAttachedFile if the file has been replaced, null if managedFile is null</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="attachReplace(net.yadaframework.persistence.entity.YadaAttachedFile,java.io.File,org.springframework.web.multipart.MultipartFile,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)">
<h3>attachReplace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">attachReplace</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;currentAttachedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</span>
                               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Replace the file associated with the current attachment, only if a file was actually attached
 The managedFile is moved to the destination when config.isFileManagerDeletingUploads() is true, otherwise the original is copied
 and left unchanged.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>currentAttachedFile</code> - an existing attachment, never null</dd>
<dd><code>managedFile</code> - the new file to set</dd>
<dd><code>multipartFile</code> - the original uploaded file, to get the client filename. If null, the client filename is not changed.</dd>
<dd><code>targetExtension</code> - optional, to convert image file formats</dd>
<dd><code>desktopWidth</code> - optional width for desktop images - when null, the image is not resized</dd>
<dd><code>mobileWidth</code> - optional width for mobile images - when null, the mobile file is the same as the desktop</dd>
<dt>Returns:</dt>
<dd>YadaAttachedFile if the file has been replaced, null if managedFile is null</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="attachReplace(boolean,net.yadaframework.persistence.entity.YadaAttachedFile,java.io.File,org.springframework.web.multipart.MultipartFile,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)">
<h3>attachReplace</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">attachReplace</span><wbr><span class="parameters">(boolean&nbsp;move,
 <a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;currentAttachedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</span>
                               throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Replace the file associated with the current attachment, only if a file was actually attached</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>move</code> - the managedFile is moved to the destination when true, otherwise the original is copied
 and left unchanged.</dd>
<dd><code>currentAttachedFile</code> - an existing attachment, never null</dd>
<dd><code>managedFile</code> - the new file to set</dd>
<dd><code>multipartFile</code> - the original uploaded file, to get the client filename. If null, the client filename is not changed.</dd>
<dd><code>namePrefix</code> - optional prefix to set before the original file name. Add a separator if you need one.</dd>
<dd><code>targetExtension</code> - optional, to convert image file formats</dd>
<dd><code>desktopWidth</code> - optional width for desktop images - when null, the image is not resized</dd>
<dd><code>mobileWidth</code> - optional width for mobile images - when null, the mobile file is the same as the desktop</dd>
<dt>Returns:</dt>
<dd>YadaAttachedFile if the file has been replaced, null if managedFile is null</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="attachNew(org.springframework.web.multipart.MultipartFile,java.lang.String,java.lang.String)">
<h3>attachNew</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">attachNew</span><wbr><span class="parameters">(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copies an uploaded file to the destination folder, creating a database association to assign to an Entity.
 The name of the file is in the format [basename]managedFileName_id.ext.
 Images are not resized.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>multipartFile</code> - the original uploaded file</dd>
<dd><code>relativeFolderPath</code> - path of the target folder relative to the contents folder, starting with a slash /</dd>
<dd><code>namePrefix</code> - prefix to attach before the original file name. Add a separator if you need one. Can be null.</dd>
<dt>Returns:</dt>
<dd>YadaAttachedFile if the file is uploaded, null if no file was sent by the user</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="attachNew(java.io.File,org.springframework.web.multipart.MultipartFile,java.lang.String,java.lang.String)">
<h3>attachNew</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">attachNew</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copies a managed file to the destination folder, creating a database association to assign to an Entity.
 The name of the file is in the format [basename]managedFileName_id.ext.
 Images are not resized.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>managedFile</code> - an uploaded file, can be an image or not</dd>
<dd><code>multipartFile</code> - the original uploaded file, to get the client filename. If null, the client filename is not set.</dd>
<dd><code>relativeFolderPath</code> - path of the target folder relative to the contents folder</dd>
<dd><code>namePrefix</code> - optional prefix to set before the original file name. Add a separator if you need one.</dd>
<dt>Returns:</dt>
<dd>YadaAttachedFile if the file is uploaded, null if no file was sent by the user</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="attachNew(java.io.File,org.springframework.web.multipart.MultipartFile,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)">
<h3>attachNew</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">attachNew</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 org.springframework.web.multipart.MultipartFile&nbsp;multipartFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copies (and resizes) a managed file to the destination folder, creating a database association to assign to an Entity.
 The name of the file is in the format [basename]managedFileName_id.ext</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>managedFile</code> - an uploaded file, can be an image or not. When null, nothing is done.</dd>
<dd><code>multipartFile</code> - the original uploaded file, to get the client filename. If null, the client filename is not changed.</dd>
<dd><code>relativeFolderPath</code> - path of the target folder relative to the contents folder, starting with a slash /</dd>
<dd><code>namePrefix</code> - prefix to attach before the original file name. Add a separator if you need one. Can be null.</dd>
<dd><code>targetExtension</code> - optional, to convert image file formats</dd>
<dd><code>desktopWidth</code> - optional width for desktop images - when null, the image is not resized</dd>
<dd><code>mobileWidth</code> - optional width for mobile images - when null, the mobile file is the same as the desktop</dd>
<dt>Returns:</dt>
<dd>YadaAttachedFile if the file is uploaded, null if no file was sent by the user</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="attachNew(java.io.File,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)">
<h3>attachNew</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">attachNew</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clientFilename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copies (and resizes) a managed file to the destination folder, creating a database association to assign to an Entity.
 The name of the file is in the format [basename]managedFileName_id.ext</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>managedFile</code> - an uploaded file, can be an image or not. When null, nothing is done.</dd>
<dd><code>clientFilename</code> - the original client filename with extension. If null, the client filename is not changed.</dd>
<dd><code>relativeFolderPath</code> - path of the target folder relative to the contents folder, starting with a slash /</dd>
<dd><code>namePrefix</code> - prefix to attach before the original file name. Add a separator if you need one. Can be null.</dd>
<dd><code>targetExtension</code> - optional, to convert image file formats</dd>
<dd><code>desktopWidth</code> - optional width for desktop images - when null, the image is not resized</dd>
<dd><code>mobileWidth</code> - optional width for mobile images - when null, the mobile file is the same as the desktop</dd>
<dt>Returns:</dt>
<dd>YadaAttachedFile if the file is uploaded, null if no file was sent by the user</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="attachNew(boolean,java.io.File,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)">
<h3>attachNew</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">attachNew</span><wbr><span class="parameters">(boolean&nbsp;move,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clientFilename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeFolderPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</span>
                           throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Copies (and resizes) a managed file to the destination folder, creating a database association to assign to an Entity.
 The name of the file is in the format [basename]managedFileName_id.ext</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>move</code> - true if the original file has to be deleted (moved when not transformed), false to keep it there</dd>
<dd><code>managedFile</code> - an uploaded file, can be an image or not. When null, nothing is done.</dd>
<dd><code>clientFilename</code> - the original client filename with extension. If null, the client filename is not changed.</dd>
<dd><code>relativeFolderPath</code> - path of the target folder relative to the contents folder, starting with a slash /</dd>
<dd><code>namePrefix</code> - prefix to attach before the original file name. Add a separator if you need one. Can be null.</dd>
<dd><code>targetExtension</code> - optional, to convert image file formats</dd>
<dd><code>desktopWidth</code> - optional width for desktop images - when null, the image is not resized</dd>
<dd><code>mobileWidth</code> - optional width for mobile images - when null, the mobile file is the same as the desktop</dd>
<dt>Returns:</dt>
<dd>YadaAttachedFile if the file is uploaded, null if no file was sent by the user</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="attach(boolean,net.yadaframework.persistence.entity.YadaAttachedFile,java.io.File,java.lang.String,java.lang.String,java.lang.String,java.lang.Integer,java.lang.Integer)">
<h3>attach</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></span>&nbsp;<span class="element-name">attach</span><wbr><span class="parameters">(boolean&nbsp;move,
 <a href="../persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a>&nbsp;yadaAttachedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;managedFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clientFilename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;namePrefix,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetExtension,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;desktopWidth,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;mobileWidth)</span>
                        throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Performs file copy and (for images) resize to different versions.
 The managedFile is moved to the destination when config.isFileManagerDeletingUploads() is true, otherwise the original is copied
 and left unchanged.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>move</code> - true if the original file has to be deleted (moved when not transformed), false to keep it there</dd>
<dd><code>yadaAttachedFile</code> - object to fill with values</dd>
<dd><code>managedFile</code> - some file to attach or replace, can be an image or not. When null, nothing is done.</dd>
<dd><code>clientFilename</code> - the client filename with extension. If null, the client filename is not changed.</dd>
<dd><code>namePrefix</code> - prefix to attach before the original file name to make the target name. Add a separator (like a dash) if you need one. Can be null.</dd>
<dd><code>targetExtension</code> - optional, to convert image file formats</dd>
<dd><code>desktopWidth</code> - optional width for desktop images - when null, the image is not resized</dd>
<dd><code>mobileWidth</code> - optional width for mobile images - when null, the mobile file is the same as the desktop</dd>
<dt>Returns:</dt>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
