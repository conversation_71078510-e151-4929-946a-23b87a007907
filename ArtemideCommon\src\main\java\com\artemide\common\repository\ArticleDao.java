package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.yr.entity.Articolo;

@Repository
@Transactional(readOnly = true)
public class ArticleDao {

    @PersistenceContext EntityManager em;

    public Optional<Articolo> findByCodiceSap(String codiceSap) {
        String sql = "from Articolo where codiceSap = :codiceSap";
        try {
            Articolo result = em.createQuery(sql, Articolo.class)
                .setMaxResults(1)
                .setParameter("codiceSap", codiceSap)
                .getSingleResult();
            return Optional.ofNullable(result);
        } catch (NonUniqueResultException | NoResultException e) {
            return Optional.empty();
        }
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Articolo", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Articolo save(Articolo entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Articolo> findById(Long entityId) {
        Articolo result = em.find(Articolo.class, entityId);
        return Optional.ofNullable(result);
    }

    public Articolo findOne(Long entityId) {
        return em.find(Articolo.class, entityId);
    }

    public List<Articolo> findAll() {
        return em.createQuery("from Articolo", Articolo.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Articolo> batchToSave) {
        for (Articolo entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Articolo entity) {
        em.remove(entity);
    }
}
