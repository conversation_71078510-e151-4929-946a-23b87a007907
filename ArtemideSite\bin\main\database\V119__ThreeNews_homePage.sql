# Three news for homePage

create table HomeNewsTitleThree (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeNewsTitleThreeMobile (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeNewsTitleTwo (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;
create table HomeNewsTitleTwoMobile (id bigint not null auto_increment, localeCode varchar(5), value longtext, HomePage_id bigint, primary key (id)) engine=InnoDB;

ALTER TABLE HomePage ADD COLUMN linkNewsThree varchar(255), ADD COLUMN linkNewsTwo varchar(255);

alter table HomeNewsTitleThree add constraint FKjmp3hh5v8l0kv5rcd47hlebrh foreign key (HomePage_id) references HomePage (id);
alter table HomeNewsTitleThreeMobile add constraint FK1k71dd6fd9vbnkstdvsbklbj4 foreign key (HomePage_id) references HomePage (id);
alter table HomeNewsTitleTwo add constraint FK2j9jt7cmgatwrlenscybtqc1h foreign key (HomePage_id) references HomePage (id);
alter table HomeNewsTitleTwoMobile add constraint FKbdy49mo3lkv0vg0u83d208ucq foreign key (HomePage_id) references HomePage (id);
