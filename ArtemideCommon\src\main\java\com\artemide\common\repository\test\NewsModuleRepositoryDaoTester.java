package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.NewsJournal;
import com.artemide.common.persistence.entity.NewsModule;
import com.artemide.common.repository.NewsModuleRepoDao;
import com.artemide.common.repository.NewsModuleRepository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies NewsModuleRepoDao behaves exactly like NewsModuleRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class NewsModuleRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private NewsModuleRepository newsModuleRepository;

    @Autowired
    private NewsModuleRepoDao newsModuleRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    NewsModule entity = entityManager.find(NewsModule.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            entityManager.createQuery("DELETE FROM NewsModule n WHERE n.data1 LIKE 'TEST_%' OR n.data1 LIKE 'INTEGRATION_%'")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private NewsModule createTestEntity(String data) {
        NewsModule entity = new NewsModule();
        // NewsModule extends PageModule which doesn't have setTitle()
        // We can use setData1() which accepts String
        entity.setData1(data);
        entity.setEnabled(true);
        return entity;
    }

    private void trackEntity(NewsModule entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = newsModuleRepository.count();
            long initialDaoCount = newsModuleRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            NewsModule testEntity = createTestEntity("INTEGRATION_SAVE_001");

            NewsModule repoSaved = newsModuleRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            NewsModule testEntity2 = createTestEntity("INTEGRATION_SAVE_002");
            NewsModule daoSaved = newsModuleRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<NewsModule> repoFound = newsModuleRepository.findById(repoSaved.getId());
            Optional<NewsModule> daoFoundOptional = newsModuleRepoDao.findById(daoSaved.getId());
            NewsModule daoFoundDirect = newsModuleRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<NewsModule> repoResults = newsModuleRepository.findAll();
            List<NewsModule> daoResults = newsModuleRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            NewsModule entityForRepo = createTestEntity("INTEGRATION_DELETE_001");
            NewsModule entityForDao = createTestEntity("INTEGRATION_DELETE_002");
            
            NewsModule savedForRepo = newsModuleRepository.save(entityForRepo);
            NewsModule savedForDao = newsModuleRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!newsModuleRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!newsModuleRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            newsModuleRepository.delete(savedForRepo);
            newsModuleRepoDao.delete(savedForDao);
            entityManager.flush();

            if (newsModuleRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (newsModuleRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAllOrderByNewsJournal() {
        setUp();
        try {
            // Get first available NewsJournal ID using DbUtil
            java.util.Optional<Long> newsJournalIdOpt = DbUtil.findFirstId(entityManager, NewsJournal.class);
            if (!newsJournalIdOpt.isPresent()) {
                return "SKIP: No NewsJournal records found in database for testing";
            }
            
            NewsJournal testNews = new NewsJournal();
            testNews.setId(newsJournalIdOpt.get());
            
            List<NewsModule> repoResult = newsModuleRepository.findAllOrderByNewsJournal(testNews);
            List<NewsModule> daoResult = newsModuleRepoDao.findAllOrderByNewsJournal(testNews);

            if (repoResult.size() != daoResult.size()) {
                return "FAIL: findAllOrderByNewsJournal results don't match - Repository: " + repoResult.size() + ", DAO: " + daoResult.size();
            }
            
            return "PASS: findAllOrderByNewsJournal() test successful";
        } finally {
            tearDown();
        }
    }

    @Transactional(readOnly = false)
    public String testRemoveImageFromCarrousel() {
        setUp();
        try {
            // Create test entity first
            NewsModule testEntity = createTestEntity("INTEGRATION_IMAGE_001");
            NewsModule saved = newsModuleRepository.save(testEntity);
            trackEntity(saved);
            entityManager.flush();

            // Test the method - this modifies associations
            Long testNewsModuleId = saved.getId();
            // Get a real image ID from database
            Optional<Long> imageIdOpt = DbUtil.findFirstId(entityManager, com.yr.babka37.entity.UploadedFile.class);
            if (!imageIdOpt.isPresent()) {
                return "SKIP: No UploadedFile records found for removeImageFromCarrousel test";
            }
            Long testImageId = imageIdOpt.get();
            
            newsModuleRepository.removeImageFromCarrousel(testNewsModuleId, testImageId);
            newsModuleRepoDao.removeImageFromCarrousel(testNewsModuleId, testImageId);
            entityManager.flush();
            
            // Both methods should execute without error
            return "PASS: removeImageFromCarrousel() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== NewsModuleRepository vs NewsModuleRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String findAllOrderByNewsJournalResult = testFindAllOrderByNewsJournal();
            report.append("5. FindAllOrderByNewsJournal Test: ").append(findAllOrderByNewsJournalResult).append("\n");
            
            String removeImageFromCarrouselResult = testRemoveImageFromCarrousel();
            report.append("6. RemoveImageFromCarrousel Test: ").append(removeImageFromCarrouselResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - NewsModuleRepoDao behaves exactly like NewsModuleRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
