<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaDataTableColumn (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.datatables.config, class: YadaDataTableColumn">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.datatables.config</a></div>
<h1 title="Class YadaDataTableColumn" class="title">Class YadaDataTableColumn</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">net.yadaframework.core.YadaFluentBase</a>&lt;<a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>&gt;
<div class="inheritance">net.yadaframework.web.datatables.config.YadaDataTableColumn</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../proxy/YadaDataTableColumnProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableColumnProxy</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaDataTableColumn</span>
<span class="extends-implements">extends <a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;<a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>&gt;</span></div>
<div class="block">Configuration for a column of a DataTables table.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../options/YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options"><code>for DataTables column options</code></a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#headerText" class="member-name-link">headerText</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#orderAsc" class="member-name-link">orderAsc</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected int</code></div>
<div class="col-second even-row-color"><code><a href="#positionInTable" class="member-name-link">positionInTable</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="../proxy/YadaDTColumnsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTColumnsProxy</a></code></div>
<div class="col-second odd-row-color"><code><a href="#yadaDTColumns" class="member-name-link">yadaDTColumns</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.core.YadaFluentBase">Fields inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#parent">parent</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String,net.yadaframework.web.datatables.config.YadaDataTableHTML)" class="member-name-link">YadaDataTableColumn</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;headerText,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;data,
 <a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>&nbsp;parent)</code></div>
<div class="col-last even-row-color">
<div class="block">Make a new column configuration</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#back()" class="member-name-link">back</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Method to return to parent for fluent chaining.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtCssClasses(java.lang.String)" class="member-name-link">dtCssClasses</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssClasses)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">css classes to set on the cell.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtName(java.lang.String)" class="member-name-link">dtName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a name on this column for database operations and cross reference.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderableOff()" class="member-name-link">dtOrderableOff</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Makes the column not orderable</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderAsc()" class="member-name-link">dtOrderAsc</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Choose this column for the initial sorting, ascending</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderAsc(int)" class="member-name-link">dtOrderAsc</a><wbr>(int&nbsp;precedence)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Choose this column for the initial sorting, ascending</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderDesc()" class="member-name-link">dtOrderDesc</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Choose this column for the initial sorting, descending</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOrderDesc(int)" class="member-name-link">dtOrderDesc</a><wbr>(int&nbsp;precedence)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Choose this column for the initial sorting, descending</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtResponsivePriority(int)" class="member-name-link">dtResponsivePriority</a><wbr>(int&nbsp;responsivePriority)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">In a responsive table control the order in which columns are hidden.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtSearchableOff()" class="member-name-link">dtSearchableOff</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Makes the column not searchable</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getName()" class="member-name-link">getName</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="headerText">
<h3>headerText</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">headerText</span></div>
</section>
</li>
<li>
<section class="detail" id="yadaDTColumns">
<h3>yadaDTColumns</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../proxy/YadaDTColumnsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTColumnsProxy</a></span>&nbsp;<span class="element-name">yadaDTColumns</span></div>
</section>
</li>
<li>
<section class="detail" id="positionInTable">
<h3>positionInTable</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">positionInTable</span></div>
</section>
</li>
<li>
<section class="detail" id="orderAsc">
<h3>orderAsc</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">orderAsc</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String,net.yadaframework.web.datatables.config.YadaDataTableHTML)">
<h3>YadaDataTableColumn</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">YadaDataTableColumn</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;headerText,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;data,
 <a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>&nbsp;parent)</span></div>
<div class="block">Make a new column configuration</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>headerText</code> - the column title</dd>
<dd><code>data</code> - the json path of the result in the ajax response. See <a href="YadaDataTableHTML.html#dtColumnObj(java.lang.String,java.lang.String)"><code>YadaDataTableHTML.dtColumnObj(java.lang.String, java.lang.String)</code></a></dd>
<dd><code>parent</code> - </dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="dtCssClasses(java.lang.String)">
<h3>dtCssClasses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></span>&nbsp;<span class="element-name">dtCssClasses</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssClasses)</span></div>
<div class="block">css classes to set on the cell.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cssClasses</code> - space-separated css classes</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.className">DataTables Reference: columns.className<span class="invalid-tag">invalid input: '&lt;'</span>/</li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderableOff()">
<h3>dtOrderableOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></span>&nbsp;<span class="element-name">dtOrderableOff</span>()</div>
<div class="block">Makes the column not orderable</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtSearchableOff()">
<h3>dtSearchableOff</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></span>&nbsp;<span class="element-name">dtSearchableOff</span>()</div>
<div class="block">Makes the column not searchable</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtName(java.lang.String)">
<h3>dtName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></span>&nbsp;<span class="element-name">dtName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
<div class="block">Set a name on this column for database operations and cross reference.
 Be careful that it matches the Entity attribute name when "data" is not a function. 
 Examples: "id", "useCredentials.username"
 When unset, the "data" option value is used.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>name</code> - the name of the column, can be null, must be unique otherwise</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="YadaDataTableConfirmDialog.html#dtPlaceholderColumnName(java.lang.String...)"><code>YadaDataTableConfirmDialog.dtPlaceholderColumnName(String...)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderAsc()">
<h3>dtOrderAsc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></span>&nbsp;<span class="element-name">dtOrderAsc</span>()</div>
<div class="block">Choose this column for the initial sorting, ascending</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtOrderAsc(int)"><code>for multiple column sorting</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderAsc(int)">
<h3>dtOrderAsc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></span>&nbsp;<span class="element-name">dtOrderAsc</span><wbr><span class="parameters">(int&nbsp;precedence)</span></div>
<div class="block">Choose this column for the initial sorting, ascending</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>precedence</code> - for multiple column sorting, a lower number means higher precedence</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderDesc()">
<h3>dtOrderDesc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></span>&nbsp;<span class="element-name">dtOrderDesc</span>()</div>
<div class="block">Choose this column for the initial sorting, descending</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#dtOrderDesc(int)"><code>for multiple column sorting</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtOrderDesc(int)">
<h3>dtOrderDesc</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></span>&nbsp;<span class="element-name">dtOrderDesc</span><wbr><span class="parameters">(int&nbsp;precedence)</span></div>
<div class="block">Choose this column for the initial sorting, descending</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>precedence</code> - for multiple column sorting, a lower number means higher precedence</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtResponsivePriority(int)">
<h3>dtResponsivePriority</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></span>&nbsp;<span class="element-name">dtResponsivePriority</span><wbr><span class="parameters">(int&nbsp;responsivePriority)</span></div>
<div class="block">In a responsive table control the order in which columns are hidden.
 Responsive will automatically remove columns from the right-hand-side 
 of the table when a table is too wide for a given display, unless this value is set.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>responsivePriority</code> - The priority is an integer value where lower numbers
                        are given a higher priority (i.e. a column with priority 2 will be 
                        hidden before a column with priority 1). The default is 10000.</dd>
<dt>Returns:</dt>
<dd>This instance for method chaining.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.responsivePriority">DataTables Reference: columns.responsivePriority</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getName()">
<h3>getName</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getName</span>()</div>
</section>
</li>
<li>
<section class="detail" id="back()">
<h3>back</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></span>&nbsp;<span class="element-name">back</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../core/YadaFluentBase.html#back()">YadaFluentBase</a></code></span></div>
<div class="block">Method to return to parent for fluent chaining.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../core/YadaFluentBase.html#back()">back</a></code>&nbsp;in class&nbsp;<code><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;<a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>&gt;</code></dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
