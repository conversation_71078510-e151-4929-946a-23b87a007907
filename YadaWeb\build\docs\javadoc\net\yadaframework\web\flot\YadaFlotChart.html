<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaFlot<PERSON>hart (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.flot, class: YadaFlotChart">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li>Method</li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.flot</a></div>
<h1 title="Class YadaFlotChart" class="title">Class YadaFlotChart</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/AbstractCollection.html" title="class or interface in java.util" class="external-link">java.util.AbstractCollection</a>&lt;<a href="YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesObject</a>&gt;
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/AbstractList.html" title="class or interface in java.util" class="external-link">java.util.AbstractList</a>&lt;<a href="YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesObject</a>&gt;
<div class="inheritance"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html" title="class or interface in java.util" class="external-link">java.util.ArrayList</a>&lt;<a href="YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesObject</a>&gt;
<div class="inheritance">net.yadaframework.web.flot.YadaFlotChart</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Cloneable.html" title="class or interface in java.lang" class="external-link">Cloneable</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;<a href="YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesObject</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesObject</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesObject</a>&gt;</code>, <code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/RandomAccess.html" title="class or interface in java.util" class="external-link">RandomAccess</a></code>, <code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/SequencedCollection.html" title="class or interface in java.util" class="external-link">SequencedCollection</a>&lt;<a href="YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesObject</a>&gt;</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaFlotChart</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html" title="class or interface in java.util" class="external-link">ArrayList</a>&lt;<a href="YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesObject</a>&gt;
implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></span></div>
<div class="block">Flot chart, composed of many series</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../serialized-form.html#net.yadaframework.web.flot.YadaFlotChart">Serialized Form</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-java.util.AbstractList">Fields inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/AbstractList.html" title="class or interface in java.util" class="external-link">AbstractList</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/AbstractList.html#modCount" title="class or interface in java.util" class="external-link">modCount</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaFlotChart</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.ArrayList">Methods inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html" title="class or interface in java.util" class="external-link">ArrayList</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#add(int,E)" title="class or interface in java.util" class="external-link">add</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#add(E)" title="class or interface in java.util" class="external-link">add</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#addAll(int,java.util.Collection)" title="class or interface in java.util" class="external-link">addAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#addAll(java.util.Collection)" title="class or interface in java.util" class="external-link">addAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#addFirst(E)" title="class or interface in java.util" class="external-link">addFirst</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#addLast(E)" title="class or interface in java.util" class="external-link">addLast</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#clear()" title="class or interface in java.util" class="external-link">clear</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#clone()" title="class or interface in java.util" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#contains(java.lang.Object)" title="class or interface in java.util" class="external-link">contains</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#ensureCapacity(int)" title="class or interface in java.util" class="external-link">ensureCapacity</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#equals(java.lang.Object)" title="class or interface in java.util" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#forEach(java.util.function.Consumer)" title="class or interface in java.util" class="external-link">forEach</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#get(int)" title="class or interface in java.util" class="external-link">get</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#getFirst()" title="class or interface in java.util" class="external-link">getFirst</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#getLast()" title="class or interface in java.util" class="external-link">getLast</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#hashCode()" title="class or interface in java.util" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#indexOf(java.lang.Object)" title="class or interface in java.util" class="external-link">indexOf</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#isEmpty()" title="class or interface in java.util" class="external-link">isEmpty</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#iterator()" title="class or interface in java.util" class="external-link">iterator</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#lastIndexOf(java.lang.Object)" title="class or interface in java.util" class="external-link">lastIndexOf</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#listIterator()" title="class or interface in java.util" class="external-link">listIterator</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#listIterator(int)" title="class or interface in java.util" class="external-link">listIterator</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#remove(int)" title="class or interface in java.util" class="external-link">remove</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#remove(java.lang.Object)" title="class or interface in java.util" class="external-link">remove</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#removeAll(java.util.Collection)" title="class or interface in java.util" class="external-link">removeAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#removeFirst()" title="class or interface in java.util" class="external-link">removeFirst</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#removeIf(java.util.function.Predicate)" title="class or interface in java.util" class="external-link">removeIf</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#removeLast()" title="class or interface in java.util" class="external-link">removeLast</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#removeRange(int,int)" title="class or interface in java.util" class="external-link">removeRange</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#replaceAll(java.util.function.UnaryOperator)" title="class or interface in java.util" class="external-link">replaceAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#retainAll(java.util.Collection)" title="class or interface in java.util" class="external-link">retainAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#set(int,E)" title="class or interface in java.util" class="external-link">set</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#size()" title="class or interface in java.util" class="external-link">size</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#sort(java.util.Comparator)" title="class or interface in java.util" class="external-link">sort</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#spliterator()" title="class or interface in java.util" class="external-link">spliterator</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#subList(int,int)" title="class or interface in java.util" class="external-link">subList</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#toArray()" title="class or interface in java.util" class="external-link">toArray</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#toArray(T%5B%5D)" title="class or interface in java.util" class="external-link">toArray</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/ArrayList.html#trimToSize()" title="class or interface in java.util" class="external-link">trimToSize</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.AbstractCollection">Methods inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/AbstractCollection.html" title="class or interface in java.util" class="external-link">AbstractCollection</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/AbstractCollection.html#containsAll(java.util.Collection)" title="class or interface in java.util" class="external-link">containsAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/AbstractCollection.html#toString()" title="class or interface in java.util" class="external-link">toString</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.Collection">Methods inherited from interface&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html#parallelStream()" title="class or interface in java.util" class="external-link">parallelStream</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html#stream()" title="class or interface in java.util" class="external-link">stream</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html#toArray(java.util.function.IntFunction)" title="class or interface in java.util" class="external-link">toArray</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.util.List">Methods inherited from interface&nbsp;java.util.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html#containsAll(java.util.Collection)" title="class or interface in java.util" class="external-link">containsAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html#reversed()" title="class or interface in java.util" class="external-link">reversed</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaFlotChart</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaFlotChart</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
