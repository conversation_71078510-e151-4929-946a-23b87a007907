package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.repository.UploadedFileRepoDao;
import com.artemide.common.repository.UploadedFileRepository;
import com.yr.babka37.entity.UploadedFile;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies UploadedFileRepoDao behaves exactly like UploadedFileRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class UploadedFileRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private UploadedFileRepository uploadedFileRepository;

    @Autowired
    private UploadedFileRepoDao uploadedFileRepoDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    UploadedFile entity = entityManager.find(UploadedFile.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            entityManager.createQuery("DELETE FROM UploadedFile u WHERE u.extension LIKE 'test_%' OR u.extension LIKE 'integration_%'")
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private UploadedFile createTestEntity(String extension) {
        UploadedFile entity = new UploadedFile();
        entity.setExtension(extension); // UploadedFile has setExtension(), not setFilename()
        return entity;
    }

    private void trackEntity(UploadedFile entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = uploadedFileRepository.count();
            long initialDaoCount = uploadedFileRepoDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            UploadedFile testEntity = createTestEntity("integration_save_001.txt");

            UploadedFile repoSaved = uploadedFileRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            UploadedFile testEntity2 = createTestEntity("integration_save_002.txt");
            UploadedFile daoSaved = uploadedFileRepoDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<UploadedFile> repoFound = uploadedFileRepository.findById(repoSaved.getId());
            Optional<UploadedFile> daoFoundOptional = uploadedFileRepoDao.findById(daoSaved.getId());
            UploadedFile daoFoundDirect = uploadedFileRepoDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<UploadedFile> repoResults = uploadedFileRepository.findAll();
            List<UploadedFile> daoResults = uploadedFileRepoDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            UploadedFile entityForRepo = createTestEntity("integration_delete_001.txt");
            UploadedFile entityForDao = createTestEntity("integration_delete_002.txt");
            
            UploadedFile savedForRepo = uploadedFileRepository.save(entityForRepo);
            UploadedFile savedForDao = uploadedFileRepoDao.save(entityForDao);
            entityManager.flush();
            
            if (!uploadedFileRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!uploadedFileRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            uploadedFileRepository.delete(savedForRepo);
            uploadedFileRepoDao.delete(savedForDao);
            entityManager.flush();

            if (uploadedFileRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (uploadedFileRepoDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== UploadedFileRepository vs UploadedFileRepoDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - UploadedFileRepoDao behaves exactly like UploadedFileRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
