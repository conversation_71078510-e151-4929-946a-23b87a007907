<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="net.yadaframework.components.YadaUtilTest$JsonTests" tests="15" skipped="0" failures="0" errors="0" timestamp="2025-09-18T16:14:08.327Z" hostname="DREAMPC2" time="0.034">
  <properties/>
  <testcase name="getJsonObject_ReturnsNestedObject()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.001"/>
  <testcase name="getJsonArray_ReturnsArray()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.002"/>
  <testcase name="getJsonAttribute_NonexistentPath_ReturnsNull()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.014"/>
  <testcase name="makeJsonObject_CreatesEmptyMap()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.002"/>
  <testcase name="makeJsonObject_WithArrayPath_ReturnsMapFromArray()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.0"/>
  <testcase name="setAndGetJsonAttribute_IntegerValue()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.0"/>
  <testcase name="setAndGetJsonAttribute_NestedValue()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.001"/>
  <testcase name="setAndGetJsonAttribute_ArrayValue()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.001"/>
  <testcase name="setAndGetJsonAttribute_DoubleValue()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.001"/>
  <testcase name="makeJsonObject_WithPath_CreatesNestedStructure()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.0"/>
  <testcase name="setAndGetJsonAttribute_SimpleValue()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.001"/>
  <testcase name="setJsonAttribute_ArrayWithGaps_FillsWithEmptyObjects()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.001"/>
  <testcase name="setJsonAttribute_NonStringValue_ConvertsToString()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.001"/>
  <testcase name="setJsonAttribute_DeepNesting_CreatesFullPath()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.001"/>
  <testcase name="getJsonObject_WithArrayIndex()" classname="net.yadaframework.components.YadaUtilTest$JsonTests" time="0.001"/>
  <system-out><![CDATA[18:14:08.335 [Test worker] DEBUG net.yadaframework.components.YadaUtil -- Null value at nonexistent
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
