<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaDTOptionsProxy (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.datatables.proxy, class: YadaDTOptionsProxy">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.datatables.proxy</a></div>
<h1 title="Class YadaDTOptionsProxy" class="title">Class YadaDTOptionsProxy</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">net.yadaframework.core.YadaFluentBase</a>&lt;<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&gt;
<div class="inheritance"><a href="../options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">net.yadaframework.web.datatables.options.YadaDTOptions</a>
<div class="inheritance">net.yadaframework.web.datatables.proxy.YadaDTOptionsProxy</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaDTOptionsProxy</span>
<span class="extends-implements">extends <a href="../options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span></div>
<div class="block">This class implements the methods needed for <b>internal use</b> 
 so that they don't pollute the fluent interface.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.web.datatables.options.YadaDTOptions">Fields inherited from class&nbsp;net.yadaframework.web.datatables.options.<a href="../options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></h3>
<code><a href="../options/YadaDTOptions.html#autoWidth">autoWidth</a>, <a href="../options/YadaDTOptions.html#caption">caption</a>, <a href="../options/YadaDTOptions.html#columnDefs">columnDefs</a>, <a href="../options/YadaDTOptions.html#columns">columns</a>, <a href="../options/YadaDTOptions.html#createdRow">createdRow</a>, <a href="../options/YadaDTOptions.html#data">data</a>, <a href="../options/YadaDTOptions.html#dataTableExtErrMode">dataTableExtErrMode</a>, <a href="../options/YadaDTOptions.html#deferLoading">deferLoading</a>, <a href="../options/YadaDTOptions.html#deferRender">deferRender</a>, <a href="../options/YadaDTOptions.html#destroy">destroy</a>, <a href="../options/YadaDTOptions.html#displayStart">displayStart</a>, <a href="../options/YadaDTOptions.html#drawCallback">drawCallback</a>, <a href="../options/YadaDTOptions.html#footerCallback">footerCallback</a>, <a href="../options/YadaDTOptions.html#formatNumber">formatNumber</a>, <a href="../options/YadaDTOptions.html#headerCallback">headerCallback</a>, <a href="../options/YadaDTOptions.html#info">info</a>, <a href="../options/YadaDTOptions.html#infoCallback">infoCallback</a>, <a href="../options/YadaDTOptions.html#initComplete">initComplete</a>, <a href="../options/YadaDTOptions.html#layout">layout</a>, <a href="../options/YadaDTOptions.html#lengthChange">lengthChange</a>, <a href="../options/YadaDTOptions.html#lengthMenu">lengthMenu</a>, <a href="../options/YadaDTOptions.html#order">order</a>, <a href="../options/YadaDTOptions.html#orderClasses">orderClasses</a>, <a href="../options/YadaDTOptions.html#orderDescReverse">orderDescReverse</a>, <a href="../options/YadaDTOptions.html#orderFixed">orderFixed</a>, <a href="../options/YadaDTOptions.html#ordering">ordering</a>, <a href="../options/YadaDTOptions.html#orderMulti">orderMulti</a>, <a href="../options/YadaDTOptions.html#pageLength">pageLength</a>, <a href="../options/YadaDTOptions.html#paging">paging</a>, <a href="../options/YadaDTOptions.html#preDrawCallback">preDrawCallback</a>, <a href="../options/YadaDTOptions.html#processing">processing</a>, <a href="../options/YadaDTOptions.html#renderer">renderer</a>, <a href="../options/YadaDTOptions.html#responsive">responsive</a>, <a href="../options/YadaDTOptions.html#rowCallback">rowCallback</a>, <a href="../options/YadaDTOptions.html#rowId">rowId</a>, <a href="../options/YadaDTOptions.html#scrollCollapse">scrollCollapse</a>, <a href="../options/YadaDTOptions.html#scrollX">scrollX</a>, <a href="../options/YadaDTOptions.html#scrollY">scrollY</a>, <a href="../options/YadaDTOptions.html#searchDelay">searchDelay</a>, <a href="../options/YadaDTOptions.html#searching">searching</a>, <a href="../options/YadaDTOptions.html#serverSide">serverSide</a>, <a href="../options/YadaDTOptions.html#stateDuration">stateDuration</a>, <a href="../options/YadaDTOptions.html#stateLoadCallback">stateLoadCallback</a>, <a href="../options/YadaDTOptions.html#stateLoaded">stateLoaded</a>, <a href="../options/YadaDTOptions.html#stateLoadParams">stateLoadParams</a>, <a href="../options/YadaDTOptions.html#stateSave">stateSave</a>, <a href="../options/YadaDTOptions.html#stateSaveCallback">stateSaveCallback</a>, <a href="../options/YadaDTOptions.html#stateSaveParams">stateSaveParams</a>, <a href="../options/YadaDTOptions.html#tabIndex">tabIndex</a>, <a href="../options/YadaDTOptions.html#typeDetect">typeDetect</a>, <a href="../options/YadaDTOptions.html#yadaDTResponsive">yadaDTResponsive</a></code></div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.core.YadaFluentBase">Fields inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#parent">parent</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(net.yadaframework.web.datatables.YadaDataTable)" class="member-name-link">YadaDTOptionsProxy</a><wbr>(<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&nbsp;parent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../options/YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addNewColumn(int)" class="member-name-link">addNewColumn</a><wbr>(int&nbsp;pos)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new column at the specified position.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getAutoWidth()" class="member-name-link">getAutoWidth</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCaption()" class="member-name-link">getCaption</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../options/YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getColumnDefs()" class="member-name-link">getColumnDefs</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../options/YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getColumns()" class="member-name-link">getColumns</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCreatedRow()" class="member-name-link">getCreatedRow</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getData()" class="member-name-link">getData</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDataTableExtErrMode()" class="member-name-link">getDataTableExtErrMode</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDeferLoading()" class="member-name-link">getDeferLoading</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDeferRender()" class="member-name-link">getDeferRender</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDestroy()" class="member-name-link">getDestroy</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDisplayStart()" class="member-name-link">getDisplayStart</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDrawCallback()" class="member-name-link">getDrawCallback</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFooterCallback()" class="member-name-link">getFooterCallback</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFormatNumber()" class="member-name-link">getFormatNumber</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getHeaderCallback()" class="member-name-link">getHeaderCallback</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInfo()" class="member-name-link">getInfo</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInfoCallback()" class="member-name-link">getInfoCallback</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getInitComplete()" class="member-name-link">getInitComplete</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLayout()" class="member-name-link">getLayout</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLengthChange()" class="member-name-link">getLengthChange</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLengthMenu()" class="member-name-link">getLengthMenu</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../options/YadaDTOrder.html" title="class in net.yadaframework.web.datatables.options">YadaDTOrder</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOrder()" class="member-name-link">getOrder</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOrderClasses()" class="member-name-link">getOrderClasses</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOrderDescReverse()" class="member-name-link">getOrderDescReverse</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOrderFixed()" class="member-name-link">getOrderFixed</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOrdering()" class="member-name-link">getOrdering</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOrderMulti()" class="member-name-link">getOrderMulti</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPageLength()" class="member-name-link">getPageLength</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPaging()" class="member-name-link">getPaging</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPreDrawCallback()" class="member-name-link">getPreDrawCallback</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getProcessing()" class="member-name-link">getProcessing</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRenderer()" class="member-name-link">getRenderer</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getResponsive()" class="member-name-link">getResponsive</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRowCallback()" class="member-name-link">getRowCallback</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRowId()" class="member-name-link">getRowId</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getScrollCollapse()" class="member-name-link">getScrollCollapse</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getScrollX()" class="member-name-link">getScrollX</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSearchDelay()" class="member-name-link">getSearchDelay</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSearching()" class="member-name-link">getSearching</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getServerSide()" class="member-name-link">getServerSide</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStateDuration()" class="member-name-link">getStateDuration</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStateLoadCallback()" class="member-name-link">getStateLoadCallback</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStateLoaded()" class="member-name-link">getStateLoaded</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStateLoadParams()" class="member-name-link">getStateLoadParams</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStateSave()" class="member-name-link">getStateSave</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStateSaveCallback()" class="member-name-link">getStateSaveCallback</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getStateSaveParams()" class="member-name-link">getStateSaveParams</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTabIndex()" class="member-name-link">getTabIndex</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getTypeDetect()" class="member-name-link">getTypeDetect</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setOrderMulti(boolean)" class="member-name-link">setOrderMulti</a><wbr>(boolean&nbsp;multi)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enable/disable multiple column ordering ability
 This has been added for internal use only.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-net.yadaframework.web.datatables.options.YadaDTOptions">Methods inherited from class&nbsp;net.yadaframework.web.datatables.options.<a href="../options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></h3>
<code><a href="../options/YadaDTOptions.html#dtAutoWidthOff()">dtAutoWidthOff</a>, <a href="../options/YadaDTOptions.html#dtCaption(java.lang.String)">dtCaption</a>, <a href="../options/YadaDTOptions.html#dtColumnDefsObj()">dtColumnDefsObj</a>, <a href="../options/YadaDTOptions.html#dtColumnsObj()">dtColumnsObj</a>, <a href="../options/YadaDTOptions.html#dtCreatedRow(java.lang.String)">dtCreatedRow</a>, <a href="../options/YadaDTOptions.html#dtData(java.util.List)">dtData</a>, <a href="../options/YadaDTOptions.html#dtDataTableExtErrMode(java.lang.String)">dtDataTableExtErrMode</a>, <a href="../options/YadaDTOptions.html#dtDeferLoading(int)">dtDeferLoading</a>, <a href="../options/YadaDTOptions.html#dtDeferLoading(int,int)">dtDeferLoading</a>, <a href="../options/YadaDTOptions.html#dtDeferRenderOff()">dtDeferRenderOff</a>, <a href="../options/YadaDTOptions.html#dtDestroy()">dtDestroy</a>, <a href="../options/YadaDTOptions.html#dtDisplayStart(java.lang.Integer)">dtDisplayStart</a>, <a href="../options/YadaDTOptions.html#dtDrawCallback(java.lang.String)">dtDrawCallback</a>, <a href="../options/YadaDTOptions.html#dtFooterCallback(java.lang.String)">dtFooterCallback</a>, <a href="../options/YadaDTOptions.html#dtFormatNumber(java.lang.String)">dtFormatNumber</a>, <a href="../options/YadaDTOptions.html#dtHeaderCallback(java.lang.String)">dtHeaderCallback</a>, <a href="../options/YadaDTOptions.html#dtInfoCallback(java.lang.String)">dtInfoCallback</a>, <a href="../options/YadaDTOptions.html#dtInfoOff()">dtInfoOff</a>, <a href="../options/YadaDTOptions.html#dtInitComplete(java.lang.String)">dtInitComplete</a>, <a href="../options/YadaDTOptions.html#dtLayout(java.lang.String)">dtLayout</a>, <a href="../options/YadaDTOptions.html#dtLengthChangeOff()">dtLengthChangeOff</a>, <a href="../options/YadaDTOptions.html#dtLengthMenu(int...)">dtLengthMenu</a>, <a href="../options/YadaDTOptions.html#dtOrder(int,java.lang.String)">dtOrder</a>, <a href="../options/YadaDTOptions.html#dtOrderClassesOff()">dtOrderClassesOff</a>, <a href="../options/YadaDTOptions.html#dtOrderDescReverseOff()">dtOrderDescReverseOff</a>, <a href="../options/YadaDTOptions.html#dtOrderFixed(java.lang.Object)">dtOrderFixed</a>, <a href="../options/YadaDTOptions.html#dtOrderingOff()">dtOrderingOff</a>, <a href="../options/YadaDTOptions.html#dtOrderMultiOff()">dtOrderMultiOff</a>, <a href="../options/YadaDTOptions.html#dtPageLength(java.lang.Integer)">dtPageLength</a>, <a href="../options/YadaDTOptions.html#dtPagingOff()">dtPagingOff</a>, <a href="../options/YadaDTOptions.html#dtPreDrawCallback(java.lang.String)">dtPreDrawCallback</a>, <a href="../options/YadaDTOptions.html#dtProcessingOn()">dtProcessingOn</a>, <a href="../options/YadaDTOptions.html#dtRenderer(java.lang.String)">dtRenderer</a>, <a href="../options/YadaDTOptions.html#dtResponsiveObj()">dtResponsiveObj</a>, <a href="../options/YadaDTOptions.html#dtResponsiveOn()">dtResponsiveOn</a>, <a href="../options/YadaDTOptions.html#dtRowCallback(java.lang.String)">dtRowCallback</a>, <a href="../options/YadaDTOptions.html#dtRowId(java.lang.String)">dtRowId</a>, <a href="../options/YadaDTOptions.html#dtScrollCollapseOn()">dtScrollCollapseOn</a>, <a href="../options/YadaDTOptions.html#dtScrollXOn()">dtScrollXOn</a>, <a href="../options/YadaDTOptions.html#dtScrollY(java.lang.String)">dtScrollY</a>, <a href="../options/YadaDTOptions.html#dtSearchDelay(java.lang.Integer)">dtSearchDelay</a>, <a href="../options/YadaDTOptions.html#dtSearchingOff()">dtSearchingOff</a>, <a href="../options/YadaDTOptions.html#dtServerSideOff()">dtServerSideOff</a>, <a href="../options/YadaDTOptions.html#dtStateDuration(java.lang.Integer)">dtStateDuration</a>, <a href="../options/YadaDTOptions.html#dtStateLoadCallback(java.lang.String)">dtStateLoadCallback</a>, <a href="../options/YadaDTOptions.html#dtStateLoaded(java.lang.String)">dtStateLoaded</a>, <a href="../options/YadaDTOptions.html#dtStateLoadParams(java.lang.String)">dtStateLoadParams</a>, <a href="../options/YadaDTOptions.html#dtStateSaveCallback(java.lang.String)">dtStateSaveCallback</a>, <a href="../options/YadaDTOptions.html#dtStateSaveOn()">dtStateSaveOn</a>, <a href="../options/YadaDTOptions.html#dtStateSaveParams(java.lang.String)">dtStateSaveParams</a>, <a href="../options/YadaDTOptions.html#dtTabIndex(java.lang.Integer)">dtTabIndex</a>, <a href="../options/YadaDTOptions.html#dtTypeDetectOff()">dtTypeDetectOff</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-net.yadaframework.core.YadaFluentBase">Methods inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#back()">back</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(net.yadaframework.web.datatables.YadaDataTable)">
<h3>YadaDTOptionsProxy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaDTOptionsProxy</span><wbr><span class="parameters">(<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&nbsp;parent)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="addNewColumn(int)">
<h3>addNewColumn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../options/YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></span>&nbsp;<span class="element-name">addNewColumn</span><wbr><span class="parameters">(int&nbsp;pos)</span></div>
<div class="block">Add a new column at the specified position.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pos</code> - the position to add the new column</dd>
<dt>Returns:</dt>
<dd>the new column</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getResponsive()">
<h3>getResponsive</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getResponsive</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>either a boolean or a full object.</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/responsive">DataTables responsive option</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDataTableExtErrMode()">
<h3>getDataTableExtErrMode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDataTableExtErrMode</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getAutoWidth()">
<h3>getAutoWidth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getAutoWidth</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getCaption()">
<h3>getCaption</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCaption</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getColumnDefs()">
<h3>getColumnDefs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../options/YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a>&gt;</span>&nbsp;<span class="element-name">getColumnDefs</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getColumns()">
<h3>getColumns</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../options/YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a>&gt;</span>&nbsp;<span class="element-name">getColumns</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getCreatedRow()">
<h3>getCreatedRow</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCreatedRow</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getData()">
<h3>getData</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getData</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getDeferLoading()">
<h3>getDeferLoading</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getDeferLoading</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getDeferRender()">
<h3>getDeferRender</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getDeferRender</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getDestroy()">
<h3>getDestroy</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getDestroy</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTypeDetect()">
<h3>getTypeDetect</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getTypeDetect</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getDisplayStart()">
<h3>getDisplayStart</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getDisplayStart</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getDrawCallback()">
<h3>getDrawCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDrawCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getFooterCallback()">
<h3>getFooterCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFooterCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getFormatNumber()">
<h3>getFormatNumber</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFormatNumber</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getHeaderCallback()">
<h3>getHeaderCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getHeaderCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getInfo()">
<h3>getInfo</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getInfo</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getInfoCallback()">
<h3>getInfoCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getInfoCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getInitComplete()">
<h3>getInitComplete</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getInitComplete</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getLayout()">
<h3>getLayout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getLayout</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getLengthChange()">
<h3>getLengthChange</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getLengthChange</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getLengthMenu()">
<h3>getLengthMenu</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int[]</span>&nbsp;<span class="element-name">getLengthMenu</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getOrder()">
<h3>getOrder</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../options/YadaDTOrder.html" title="class in net.yadaframework.web.datatables.options">YadaDTOrder</a>&gt;</span>&nbsp;<span class="element-name">getOrder</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getOrderClasses()">
<h3>getOrderClasses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getOrderClasses</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getOrderDescReverse()">
<h3>getOrderDescReverse</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getOrderDescReverse</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getOrderFixed()">
<h3>getOrderFixed</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">getOrderFixed</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getOrderMulti()">
<h3>getOrderMulti</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getOrderMulti</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getOrdering()">
<h3>getOrdering</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getOrdering</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getPageLength()">
<h3>getPageLength</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getPageLength</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getPaging()">
<h3>getPaging</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getPaging</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getPreDrawCallback()">
<h3>getPreDrawCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getPreDrawCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getProcessing()">
<h3>getProcessing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getProcessing</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getRenderer()">
<h3>getRenderer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRenderer</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getRowCallback()">
<h3>getRowCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRowCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getRowId()">
<h3>getRowId</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRowId</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getScrollCollapse()">
<h3>getScrollCollapse</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getScrollCollapse</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getScrollX()">
<h3>getScrollX</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getScrollX</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getSearchDelay()">
<h3>getSearchDelay</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getSearchDelay</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getSearching()">
<h3>getSearching</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getSearching</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getServerSide()">
<h3>getServerSide</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getServerSide</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getStateDuration()">
<h3>getStateDuration</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getStateDuration</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getStateLoadCallback()">
<h3>getStateLoadCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStateLoadCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getStateLoadParams()">
<h3>getStateLoadParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStateLoadParams</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getStateLoaded()">
<h3>getStateLoaded</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStateLoaded</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getStateSave()">
<h3>getStateSave</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">getStateSave</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getStateSaveCallback()">
<h3>getStateSaveCallback</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStateSaveCallback</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getStateSaveParams()">
<h3>getStateSaveParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getStateSaveParams</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getTabIndex()">
<h3>getTabIndex</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">getTabIndex</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setOrderMulti(boolean)">
<h3>setOrderMulti</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setOrderMulti</span><wbr><span class="parameters">(boolean&nbsp;multi)</span></div>
<div class="block">Enable/disable multiple column ordering ability
 This has been added for internal use only.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/option/orderMulti">orderMulti</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
