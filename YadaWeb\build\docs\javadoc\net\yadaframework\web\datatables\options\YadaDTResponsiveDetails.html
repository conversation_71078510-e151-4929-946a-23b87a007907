<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaDTResponsiveDetails (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.datatables.options, class: YadaDTResponsiveDetails">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.datatables.options</a></div>
<h1 title="Class YadaDTResponsiveDetails" class="title">Class YadaDTResponsiveDetails</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">net.yadaframework.core.YadaFluentBase</a>&lt;<a href="YadaDTResponsive.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsive</a>&gt;
<div class="inheritance">net.yadaframework.web.datatables.options.YadaDTResponsiveDetails</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaDTResponsiveDetails</span>
<span class="extends-implements">extends <a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;<a href="YadaDTResponsive.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsive</a>&gt;</span></div>
<div class="block">Class representing detailed options for responsive behavior in DataTables.</div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/responsive.details">DataTables Responsive Details Reference</a></li>
</ul>
</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.core.YadaFluentBase">Fields inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#parent">parent</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(net.yadaframework.web.datatables.options.YadaDTResponsive)" class="member-name-link">YadaDTResponsiveDetails</a><wbr>(<a href="YadaDTResponsive.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsive</a>&nbsp;parent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtDisplay(java.lang.String)" class="member-name-link">dtDisplay</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;display)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">The function given is responsible for showing and hiding the child data.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtRenderer(java.lang.String)" class="member-name-link">dtRenderer</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;renderer)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the function that will be used to render the child row when the table is responsive.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTarget(java.lang.Integer)" class="member-name-link">dtTarget</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;target)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Column for child row display control when using column details type.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTarget(java.lang.String)" class="member-name-link">dtTarget</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;target)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Selector for child row display control when using column details type.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTypeColumn()" class="member-name-link">dtTypeColumn</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sets the "column" type of control for displaying the child rows i.e.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtTypeNone()" class="member-name-link">dtTypeNone</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Don't show the show / hide icons.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-net.yadaframework.core.YadaFluentBase">Methods inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#back()">back</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(net.yadaframework.web.datatables.options.YadaDTResponsive)">
<h3>YadaDTResponsiveDetails</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaDTResponsiveDetails</span><wbr><span class="parameters">(<a href="YadaDTResponsive.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsive</a>&nbsp;parent)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="dtDisplay(java.lang.String)">
<h3>dtDisplay</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></span>&nbsp;<span class="element-name">dtDisplay</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;display)</span></div>
<div class="block">The function given is responsible for showing and hiding the child data.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>display</code> - Details display control configuration:
 <ul>
 <li>"DataTable.Responsive.display.childRowImmediate" will show the child rows immediately without need of clicking on the control</li>
 <li>"DataTable.Responsive.display.modal()" will show the child rows in a modal dialog</li>
 <ul></dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/responsive.details.display">DataTables details.display Reference</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtRenderer(java.lang.String)">
<h3>dtRenderer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></span>&nbsp;<span class="element-name">dtRenderer</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;renderer)</span></div>
<div class="block">Sets the function that will be used to render the child row when the table is responsive.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>renderer</code> - Details renderer function</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/responsive.details.renderer">DataTables details.renderer Reference</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtTarget(java.lang.Integer)">
<h3>dtTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></span>&nbsp;<span class="element-name">dtTarget</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;target)</span></div>
<div class="block">Column for child row display control when using column details type.
 This column is the one where the user needs to click in order to show the hidden details.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - Column index to which the show / hide control should be attached. 
                                This can be &gt;=0 to count columns from the left, or <span class="invalid-tag">invalid input: '&lt;'</span>0 to count from the right.</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#dtTypeColumn()"><code>dtTypeColumn()</code></a></li>
<li><a href="https://datatables.net/reference/option/responsive.details.target">DataTables details.target Reference</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtTarget(java.lang.String)">
<h3>dtTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></span>&nbsp;<span class="element-name">dtTarget</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;target)</span></div>
<div class="block">Selector for child row display control when using column details type.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>target</code> - jQuery selector to determine what element(s) will activate the show / hide 
                        control for the details child rows. This provides the ability to use any element 
                        in a table - for example you can use the whole row, or a single img element in the row.</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/responsive.details.target">DataTables details.target Reference</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtTypeColumn()">
<h3>dtTypeColumn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></span>&nbsp;<span class="element-name">dtTypeColumn</span>()</div>
<div class="block">Sets the "column" type of control for displaying the child rows i.e.
 use a whole column to display the control element.
 This means that you need to click on a cell to show the hidden columns
 and the cell is in the column set with dtTarget().</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#dtTarget(java.lang.Integer)"><code>dtTarget(Integer)</code></a></li>
<li>
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#dtTypeNone(Integer)</code></pre>
</details>
</li>
<li><a href="https://datatables.net/reference/option/responsive.details.type">DataTables details.type Reference</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtTypeNone()">
<h3>dtTypeNone</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></span>&nbsp;<span class="element-name">dtTypeNone</span>()</div>
<div class="block">Don't show the show / hide icons.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/responsive.details.type">DataTables details.type Reference</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
