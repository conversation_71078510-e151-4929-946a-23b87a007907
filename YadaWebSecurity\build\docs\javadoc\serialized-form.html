<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Serialized Form (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="serialized forms">
<meta name="generator" content="javadoc/SerializedFormWriterImpl">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="serialized-form-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#serialized-form">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Serialized Form" class="title">Serialized Form</h1>
</div>
<ul class="block-list">
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/security/package-summary.html">net.yadaframework.security</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.security.TooManyFailedAttemptsException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/security/TooManyFailedAttemptsException.html" title="class in net.yadaframework.security">net.yadaframework.security.TooManyFailedAttemptsException</a></h3>
<div class="type-signature">class TooManyFailedAttemptsException extends org.springframework.security.core.userdetails.UsernameNotFoundException implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.security.YadaWrappedSavedRequest">
<h3>Class&nbsp;<a href="net/yadaframework/security/YadaWrappedSavedRequest.html" title="class in net.yadaframework.security">net.yadaframework.security.YadaWrappedSavedRequest</a></h3>
<div class="type-signature">class YadaWrappedSavedRequest extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>overriddenRedirectUrl</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> overriddenRedirectUrl</pre>
</li>
<li class="block-list">
<h5>savedRequest</h5>
<pre>org.springframework.security.web.savedrequest.SavedRequest savedRequest</pre>
</li>
<li class="block-list">
<h5>yadaWebUtil</h5>
<pre>net.yadaframework.components.YadaWebUtil yadaWebUtil</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/security/exceptions/package-summary.html">net.yadaframework.security.exceptions</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.security.exceptions.InternalAuthenticationException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/security/exceptions/InternalAuthenticationException.html" title="class in net.yadaframework.security.exceptions">net.yadaframework.security.exceptions.InternalAuthenticationException</a></h3>
<div class="type-signature">class InternalAuthenticationException extends org.springframework.security.core.userdetails.UsernameNotFoundException implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.security.exceptions.YadaInvalidUserException">
<h3>Exception Class&nbsp;<a href="net/yadaframework/security/exceptions/YadaInvalidUserException.html" title="class in net.yadaframework.security.exceptions">net.yadaframework.security.exceptions.YadaInvalidUserException</a></h3>
<div class="type-signature">class YadaInvalidUserException extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="net/yadaframework/security/persistence/entity/package-summary.html">net.yadaframework.security.persistence.entity</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="net.yadaframework.security.persistence.entity.YadaAutoLoginToken">
<h3>Class&nbsp;<a href="net/yadaframework/security/persistence/entity/YadaAutoLoginToken.html" title="class in net.yadaframework.security.persistence.entity">net.yadaframework.security.persistence.entity.YadaAutoLoginToken</a></h3>
<div class="type-signature">class YadaAutoLoginToken extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>expiration</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> expiration</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>timestamp</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> timestamp</pre>
</li>
<li class="block-list">
<h5>token</h5>
<pre>long token</pre>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
<li class="block-list">
<h5>yadaUserCredentials</h5>
<pre><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a> yadaUserCredentials</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.security.persistence.entity.YadaCommentMessage">
<h3>Class&nbsp;<a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">net.yadaframework.security.persistence.entity.YadaCommentMessage</a></h3>
<div class="type-signature">class YadaCommentMessage extends <a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&lt;<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>likers</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&gt; likers</pre>
</li>
<li class="block-list">
<h5>repliesTo</h5>
<pre><a href="net/yadaframework/security/persistence/entity/YadaCommentMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaCommentMessage</a> repliesTo</pre>
</li>
<li class="block-list">
<h5>totLikes</h5>
<pre>long totLikes</pre>
</li>
<li class="block-list">
<h5>totReplies</h5>
<pre>int totReplies</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.security.persistence.entity.YadaRegistrationRequest">
<h3>Class&nbsp;<a href="net/yadaframework/security/persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">net.yadaframework.security.persistence.entity.YadaRegistrationRequest</a></h3>
<div class="type-signature">class YadaRegistrationRequest extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>confirmPassword</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> confirmPassword</pre>
<div class="block">Password confirm, only used on the frontend but needed by thymeleaf to make things simpler</div>
</li>
<li class="block-list">
<h5>email</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> email</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>password</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> password</pre>
</li>
<li class="block-list">
<h5>registrationType</h5>
<pre>net.yadaframework.core.YadaRegistrationType registrationType</pre>
</li>
<li class="block-list">
<h5>timestamp</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> timestamp</pre>
</li>
<li class="block-list">
<h5>timezone</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a> timezone</pre>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</li>
<li class="block-list">
<h5>token</h5>
<pre>long token</pre>
</li>
<li class="block-list">
<h5>trattamentoDati</h5>
<pre>net.yadaframework.persistence.entity.YadaClause trattamentoDati</pre>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</li>
<li class="block-list">
<h5>trattamentoDatiAccepted</h5>
<pre>boolean trattamentoDatiAccepted</pre>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
<li class="block-list">
<h5>yadaUserCredentials</h5>
<pre><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a> yadaUserCredentials</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.security.persistence.entity.YadaSocialCredentials">
<h3>Class&nbsp;<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">net.yadaframework.security.persistence.entity.YadaSocialCredentials</a></h3>
<div class="type-signature">class YadaSocialCredentials extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>email</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> email</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>socialId</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> socialId</pre>
</li>
<li class="block-list">
<h5>type</h5>
<pre>int type</pre>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
<li class="block-list">
<h5>yadaUserCredentials</h5>
<pre><a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a> yadaUserCredentials</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.security.persistence.entity.YadaTicket">
<h3>Class&nbsp;<a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">net.yadaframework.security.persistence.entity.YadaTicket</a></h3>
<div class="type-signature">class YadaTicket extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>assigned</h5>
<pre><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a> assigned</pre>
</li>
<li class="block-list">
<h5>creationDate</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> creationDate</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>messages</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaTicketMessage</a>&gt; messages</pre>
</li>
<li class="block-list">
<h5>owner</h5>
<pre><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a> owner</pre>
</li>
<li class="block-list">
<h5>priority</h5>
<pre>int priority</pre>
</li>
<li class="block-list">
<h5>status</h5>
<pre>net.yadaframework.persistence.entity.YadaPersistentEnum&lt;<a href="net/yadaframework/security/persistence/entity/YadaTicketStatus.html" title="enum class in net.yadaframework.security.persistence.entity">YadaTicketStatus</a>&gt; status</pre>
</li>
<li class="block-list">
<h5>title</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> title</pre>
</li>
<li class="block-list">
<h5>type</h5>
<pre>net.yadaframework.persistence.entity.YadaPersistentEnum&lt;?&gt; type</pre>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.security.persistence.entity.YadaTicketMessage">
<h3>Class&nbsp;<a href="net/yadaframework/security/persistence/entity/YadaTicketMessage.html" title="class in net.yadaframework.security.persistence.entity">net.yadaframework.security.persistence.entity.YadaTicketMessage</a></h3>
<div class="type-signature">class YadaTicketMessage extends <a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&lt;<a href="net/yadaframework/security/persistence/entity/YadaUserMessageType.html" title="enum class in net.yadaframework.security.persistence.entity">YadaUserMessageType</a>&gt; implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>yadaTicket</h5>
<pre><a href="net/yadaframework/security/persistence/entity/YadaTicket.html" title="class in net.yadaframework.security.persistence.entity">YadaTicket</a> yadaTicket</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.security.persistence.entity.YadaUserCredentials">
<h3>Class&nbsp;<a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">net.yadaframework.security.persistence.entity.YadaUserCredentials</a></h3>
<div class="type-signature">class YadaUserCredentials extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>changePassword</h5>
<pre>boolean changePassword</pre>
</li>
<li class="block-list">
<h5>creationDate</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> creationDate</pre>
</li>
<li class="block-list">
<h5>enabled</h5>
<pre>boolean enabled</pre>
</li>
<li class="block-list">
<h5>failedAttempts</h5>
<pre>int failedAttempts</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>lastFailedAttempt</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> lastFailedAttempt</pre>
</li>
<li class="block-list">
<h5>lastSuccessfulLogin</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> lastSuccessfulLogin</pre>
</li>
<li class="block-list">
<h5>newPassword</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> newPassword</pre>
</li>
<li class="block-list">
<h5>password</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> password</pre>
</li>
<li class="block-list">
<h5>passwordDate</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> passwordDate</pre>
</li>
<li class="block-list">
<h5>roles</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt; roles</pre>
</li>
<li class="block-list">
<h5>username</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> username</pre>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
<li class="block-list">
<h5>yadaSocialCredentialsList</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="net/yadaframework/security/persistence/entity/YadaSocialCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaSocialCredentials</a>&gt; yadaSocialCredentialsList</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.security.persistence.entity.YadaUserMessage">
<h3>Class&nbsp;<a href="net/yadaframework/security/persistence/entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">net.yadaframework.security.persistence.entity.YadaUserMessage</a></h3>
<div class="type-signature">class YadaUserMessage extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>7008892353441772768L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>attachment</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;net.yadaframework.persistence.entity.YadaAttachedFile&gt; attachment</pre>
</li>
<li class="block-list">
<h5>contentHash</h5>
<pre>int contentHash</pre>
</li>
<li class="block-list">
<h5>created</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&gt; created</pre>
</li>
<li class="block-list">
<h5>data</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> data</pre>
</li>
<li class="block-list">
<h5>emailed</h5>
<pre>boolean emailed</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>message</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> message</pre>
</li>
<li class="block-list">
<h5>modified</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a> modified</pre>
</li>
<li class="block-list">
<h5>priority</h5>
<pre>int priority</pre>
</li>
<li class="block-list">
<h5>readByRecipient</h5>
<pre>boolean readByRecipient</pre>
</li>
<li class="block-list">
<h5>recipient</h5>
<pre><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a> recipient</pre>
</li>
<li class="block-list">
<h5>sender</h5>
<pre><a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a> sender</pre>
</li>
<li class="block-list">
<h5>stackable</h5>
<pre>boolean stackable</pre>
</li>
<li class="block-list">
<h5>stackSize</h5>
<pre>int stackSize</pre>
</li>
<li class="block-list">
<h5>status</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a> status</pre>
</li>
<li class="block-list">
<h5>title</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> title</pre>
</li>
<li class="block-list">
<h5>type</h5>
<pre>net.yadaframework.persistence.entity.YadaPersistentEnum&lt;?&gt; type</pre>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-class-details" id="net.yadaframework.security.persistence.entity.YadaUserProfile">
<h3>Class&nbsp;<a href="net/yadaframework/security/persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">net.yadaframework.security.persistence.entity.YadaUserProfile</a></h3>
<div class="type-signature">class YadaUserProfile extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a> implements <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
<dl class="name-value">
<dt>serialVersionUID:</dt>
<dd>1L</dd>
</dl>
<ul class="block-list">
<li>
<section class="detail">
<h4>Serialized Fields</h4>
<ul class="block-list">
<li class="block-list">
<h5>avatar</h5>
<pre>net.yadaframework.persistence.entity.YadaAttachedFile avatar</pre>
</li>
<li class="block-list">
<h5>config</h5>
<pre>net.yadaframework.core.YadaConfiguration config</pre>
</li>
<li class="block-list">
<h5>firstName</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> firstName</pre>
</li>
<li class="block-list">
<h5>id</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a> id</pre>
</li>
<li class="block-list">
<h5>lastName</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> lastName</pre>
</li>
<li class="block-list">
<h5>locale</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a> locale</pre>
</li>
<li class="block-list">
<h5>middleName</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a> middleName</pre>
</li>
<li class="block-list">
<h5>timezone</h5>
<pre><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/TimeZone.html" title="class or interface in java.util" class="external-link">TimeZone</a> timezone</pre>
</li>
<li class="block-list">
<h5>timezoneSetByUser</h5>
<pre>boolean timezoneSetByUser</pre>
</li>
<li class="block-list">
<h5>userCredentials</h5>
<pre>@NotNull <a href="net/yadaframework/security/persistence/entity/YadaUserCredentials.html" title="class in net.yadaframework.security.persistence.entity">YadaUserCredentials</a> userCredentials</pre>
</li>
<li class="block-list">
<h5>version</h5>
<pre>long version</pre>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
</li>
</ul>
</main>
</div>
</div>
</body>
</html>
