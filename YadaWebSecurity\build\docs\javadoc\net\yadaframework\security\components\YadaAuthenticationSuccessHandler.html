<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaAuthenticationSuccessHandler (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.security.components, class: YadaAuthenticationSuccessHandler">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.security.components</a></div>
<h1 title="Class YadaAuthenticationSuccessHandler" class="title">Class YadaAuthenticationSuccessHandler</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">org.springframework.security.web.authentication.AbstractAuthenticationTargetUrlRequestHandler
<div class="inheritance">org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler
<div class="inheritance">org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler
<div class="inheritance">net.yadaframework.security.components.YadaAuthenticationSuccessHandler</div>
</div>
</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code>org.springframework.security.web.authentication.AuthenticationSuccessHandler</code></dd>
</dl>
<hr>
<div class="type-signature"><span class="annotations">@Component
@Scope("prototype")
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaAuthenticationSuccessHandler</span>
<span class="extends-implements">extends org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler</span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#AJAX_LOGGEDIN_HEADER" class="member-name-link">AJAX_LOGGEDIN_HEADER</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#AJAX_LOGGEDIN_PARAM" class="member-name-link">AJAX_LOGGEDIN_PARAM</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler">Fields inherited from class&nbsp;org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler</h3>
<code>logger</code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaAuthenticationSuccessHandler</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#determineTargetUrl(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">determineTargetUrl</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Thread-safe way of changing the default target url based on the request type (ajax/normal)</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultTargetUrlAjaxRequest()" class="member-name-link">getDefaultTargetUrlAjaxRequest</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getDefaultTargetUrlNormalRequest()" class="member-name-link">getDefaultTargetUrlNormalRequest</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onAuthenticationSuccess(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,org.springframework.security.core.Authentication)" class="member-name-link">onAuthenticationSuccess</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#onAuthenticationSuccessCustom(jakarta.servlet.http.HttpServletRequest,org.springframework.security.core.Authentication)" class="member-name-link">onAuthenticationSuccessCustom</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Custom code to be executed after login or autologin.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDefaultTargetUrlAjaxRequest(java.lang.String)" class="member-name-link">setDefaultTargetUrlAjaxRequest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultTargetUrlAjaxRequest)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Target url to redirect after login when the request is Ajax</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setDefaultTargetUrlNormalRequest(java.lang.String)" class="member-name-link">setDefaultTargetUrlNormalRequest</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultTargetUrlNormalRequest)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Target url to redirect after login when the request is not Ajax</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler">Methods inherited from class&nbsp;org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler</h3>
<code>setRequestCache</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler">Methods inherited from class&nbsp;org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler</h3>
<code>clearAuthenticationAttributes</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.security.web.authentication.AbstractAuthenticationTargetUrlRequestHandler">Methods inherited from class&nbsp;org.springframework.security.web.authentication.AbstractAuthenticationTargetUrlRequestHandler</h3>
<code>determineTargetUrl, getDefaultTargetUrl, getRedirectStrategy, getTargetUrlParameter, handle, isAlwaysUseDefaultTargetUrl, setAlwaysUseDefaultTargetUrl, setDefaultTargetUrl, setRedirectStrategy, setTargetUrlParameter, setUseReferer</code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-org.springframework.security.web.authentication.AuthenticationSuccessHandler">Methods inherited from interface&nbsp;org.springframework.security.web.authentication.AuthenticationSuccessHandler</h3>
<code>onAuthenticationSuccess</code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="AJAX_LOGGEDIN_PARAM">
<h3>AJAX_LOGGEDIN_PARAM</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">AJAX_LOGGEDIN_PARAM</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#net.yadaframework.security.components.YadaAuthenticationSuccessHandler.AJAX_LOGGEDIN_PARAM">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="AJAX_LOGGEDIN_HEADER">
<h3>AJAX_LOGGEDIN_HEADER</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">AJAX_LOGGEDIN_HEADER</span></div>
<dl class="notes">
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../constant-values.html#net.yadaframework.security.components.YadaAuthenticationSuccessHandler.AJAX_LOGGEDIN_HEADER">Constant Field Values</a></li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaAuthenticationSuccessHandler</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaAuthenticationSuccessHandler</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="onAuthenticationSuccessCustom(jakarta.servlet.http.HttpServletRequest,org.springframework.security.core.Authentication)">
<h3>onAuthenticationSuccessCustom</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onAuthenticationSuccessCustom</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 org.springframework.security.core.Authentication&nbsp;authentication)</span></div>
<div class="block">Custom code to be executed after login or autologin. Can be overridden.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dd><code>authentication</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="onAuthenticationSuccess(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse,org.springframework.security.core.Authentication)">
<h3>onAuthenticationSuccess</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">onAuthenticationSuccess</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response,
 org.springframework.security.core.Authentication&nbsp;authentication)</span>
                             throws <span class="exceptions">jakarta.servlet.ServletException,
<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code>onAuthenticationSuccess</code>&nbsp;in interface&nbsp;<code>org.springframework.security.web.authentication.AuthenticationSuccessHandler</code></dd>
<dt>Overrides:</dt>
<dd><code>onAuthenticationSuccess</code>&nbsp;in class&nbsp;<code>org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler</code></dd>
<dt>Throws:</dt>
<dd><code>jakarta.servlet.ServletException</code></dd>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getDefaultTargetUrlAjaxRequest()">
<h3>getDefaultTargetUrlAjaxRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultTargetUrlAjaxRequest</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setDefaultTargetUrlAjaxRequest(java.lang.String)">
<h3>setDefaultTargetUrlAjaxRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDefaultTargetUrlAjaxRequest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultTargetUrlAjaxRequest)</span></div>
<div class="block">Target url to redirect after login when the request is Ajax</div>
</section>
</li>
<li>
<section class="detail" id="getDefaultTargetUrlNormalRequest()">
<h3>getDefaultTargetUrlNormalRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getDefaultTargetUrlNormalRequest</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setDefaultTargetUrlNormalRequest(java.lang.String)">
<h3>setDefaultTargetUrlNormalRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setDefaultTargetUrlNormalRequest</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;defaultTargetUrlNormalRequest)</span></div>
<div class="block">Target url to redirect after login when the request is not Ajax</div>
</section>
</li>
<li>
<section class="detail" id="determineTargetUrl(jakarta.servlet.http.HttpServletRequest,jakarta.servlet.http.HttpServletResponse)">
<h3>determineTargetUrl</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">determineTargetUrl</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span></div>
<div class="block">Thread-safe way of changing the default target url based on the request type (ajax/normal)</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code>determineTargetUrl</code>&nbsp;in class&nbsp;<code>org.springframework.security.web.authentication.AbstractAuthenticationTargetUrlRequestHandler</code></dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
