<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>net.yadaframework.web (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web">
<meta name="generator" content="javadoc/PackageWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li>Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#package">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Package:</p>
<ul>
<li>Description</li>
<li><a href="#related-package-summary">Related Packages</a></li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Package:&nbsp;</li>
<li>Description&nbsp;|&nbsp;</li>
<li><a href="#related-package-summary">Related Packages</a>&nbsp;|&nbsp;</li>
<li><a href="#class-summary">Classes and Interfaces</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="Package net.yadaframework.web" class="title">Package net.yadaframework.web</h1>
</div>
<hr>
<div class="package-signature">package <span class="element-name">net.yadaframework.web</span></div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="datatables/package-summary.html">net.yadaframework.web.datatables</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="dialect/package-summary.html">net.yadaframework.web.dialect</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="exceptions/package-summary.html">net.yadaframework.web.exceptions</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="flot/package-summary.html">net.yadaframework.web.flot</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="form/package-summary.html">net.yadaframework.web.form</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="social/package-summary.html">net.yadaframework.web.social</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="class-summary-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaController.html" title="class in net.yadaframework.web">YadaController</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaCropImage.html" title="class in net.yadaframework.web">YadaCropImage</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Kept in HttpSession, it stores all info needed to crop images.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaCropQueue.html" title="class in net.yadaframework.web">YadaCropQueue</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Kept in HttpSession, it stores all info needed to crop images.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaDatatablesColumn.html" title="class in net.yadaframework.web">YadaDatatablesColumn</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This is a map because Spring wouldn't map it from the request otherwise</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaDatatablesColumnSearch.html" title="class in net.yadaframework.web">YadaDatatablesColumnSearch</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">This is a map because Spring wouldn't map it from the request otherwise</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaDatatablesOrder.html" title="class in net.yadaframework.web">YadaDatatablesOrder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">This is a map because Spring wouldn't map it from the request otherwise</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaDatatablesRequest.html" title="class in net.yadaframework.web">YadaDatatablesRequest</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Mapped by Spring automatically from the request</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaEmailContent.html" title="class in net.yadaframework.web">YadaEmailContent</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaEmailParam.html" title="class in net.yadaframework.web">YadaEmailParam</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaGlobalExceptionHandler.html" title="class in net.yadaframework.web">YadaGlobalExceptionHandler</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Handles all exceptions exiting a @Controller that have not been annotated with @ResponseStatus
 http://ankursinghal86.blogspot.it/2014/07/exception-handling-in-spring-mvc.html</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaJsonDateSimpleSerializer.html" title="class in net.yadaframework.web">YadaJsonDateSimpleSerializer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Date serializer that produces a date in the format "yyyy-MM-dd" using the thread's timezone and locale</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaJsonDateTimeShortSerializer.html" title="class in net.yadaframework.web">YadaJsonDateTimeShortSerializer</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Date serializer that produces a date in the FastDateFormat.SHORT format using the thread's timezone and locale</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaJsonRawStringSerializer.html" title="class in net.yadaframework.web">YadaJsonRawStringSerializer</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Serialize a String to JSON as a raw value i.e.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaJsonView.html" title="class in net.yadaframework.web">YadaJsonView</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">Deprecated.</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A page for pageable content.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaPageRows.html" title="class in net.yadaframework.web">YadaPageRows</a>&lt;T&gt;</div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">A page of rows fetched using YadaPageRequest</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaPageSort.html" title="class in net.yadaframework.web">YadaPageSort</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Database "order by" definition.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="YadaPersistentEnumEditor.html" title="class in net.yadaframework.web">YadaPersistentEnumEditor</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Used by thymeleaf to convert between string representation and YadaPersistentEnum.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="YadaPublicSuffix.html" title="class in net.yadaframework.web">YadaPublicSuffix</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Parser for https://publicsuffix.org list of domain names</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="YadaViews.html" title="interface in net.yadaframework.web">YadaViews</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">
<div class="block">Some view strings that have specific effects when returned from a @Controller</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
</main>
</div>
</div>
</body>
</html>
