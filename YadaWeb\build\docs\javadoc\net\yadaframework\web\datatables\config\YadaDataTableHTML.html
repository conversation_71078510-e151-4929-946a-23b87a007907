<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaDataTableHTML (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.datatables.config, class: YadaDataTableHTML">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.datatables.config</a></div>
<h1 title="Class YadaDataTableHTML" class="title">Class YadaDataTableHTML</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">net.yadaframework.core.YadaFluentBase</a>&lt;<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&gt;
<div class="inheritance">net.yadaframework.web.datatables.config.YadaDataTableHTML</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../proxy/YadaDataTableHTMLProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableHTMLProxy</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaDataTableHTML</span>
<span class="extends-implements">extends <a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&gt;</span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#backCalled" class="member-name-link">backCalled</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../proxy/YadaDataTableButtonProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableButtonProxy</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#buttons" class="member-name-link">buttons</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../proxy/YadaDataTableColumnProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableColumnProxy</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#columns" class="member-name-link">columns</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#commandsTitle" class="member-name-link">commandsTitle</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#cssClasses" class="member-name-link">cssClasses</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="../proxy/YadaDTOptionsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTOptionsProxy</a></code></div>
<div class="col-second odd-row-color"><code><a href="#options" class="member-name-link">options</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>,<wbr><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a>&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#orderingMap" class="member-name-link">orderingMap</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#selectCheckboxTitle" class="member-name-link">selectCheckboxTitle</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second even-row-color"><code><a href="#showFooter" class="member-name-link">showFooter</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.core.YadaFluentBase">Fields inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#parent">parent</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(net.yadaframework.web.datatables.YadaDataTable,net.yadaframework.web.datatables.proxy.YadaDTOptionsProxy)" class="member-name-link">YadaDataTableHTML</a><wbr>(<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&nbsp;parent,
 <a href="../proxy/YadaDTOptionsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTOptionsProxy</a>&nbsp;options)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#back()" class="member-name-link">back</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Method to return to parent for fluent chaining.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtButtonObj(java.lang.String)" class="member-name-link">dtButtonObj</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new button to the table.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtColumnCheckbox(java.lang.String)" class="member-name-link">dtColumnCheckbox</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds the leftmost column with select checkboxes</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtColumnCommands(java.lang.String)" class="member-name-link">dtColumnCommands</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds the rightmost column with command icons</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtColumnCommands(java.lang.String,int)" class="member-name-link">dtColumnCommands</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 int&nbsp;responsivePriority)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds the rightmost column with command icons</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtColumnObj(java.lang.String,java.lang.String)" class="member-name-link">dtColumnObj</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;headerText,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;data)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a new column to the table.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtCssClasses(java.lang.String)" class="member-name-link">dtCssClasses</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssClasses)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the css classes to set on the table tag.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtFooter()" class="member-name-link">dtFooter</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Shows a footer with the same content as the header</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isSelectCheckbox()" class="member-name-link">isSelectCheckbox</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="buttons">
<h3>buttons</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../proxy/YadaDataTableButtonProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableButtonProxy</a>&gt;</span>&nbsp;<span class="element-name">buttons</span></div>
</section>
</li>
<li>
<section class="detail" id="cssClasses">
<h3>cssClasses</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">cssClasses</span></div>
</section>
</li>
<li>
<section class="detail" id="selectCheckboxTitle">
<h3>selectCheckboxTitle</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">selectCheckboxTitle</span></div>
</section>
</li>
<li>
<section class="detail" id="commandsTitle">
<h3>commandsTitle</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">commandsTitle</span></div>
</section>
</li>
<li>
<section class="detail" id="showFooter">
<h3>showFooter</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">showFooter</span></div>
</section>
</li>
<li>
<section class="detail" id="columns">
<h3>columns</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../proxy/YadaDataTableColumnProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableColumnProxy</a>&gt;</span>&nbsp;<span class="element-name">columns</span></div>
</section>
</li>
<li>
<section class="detail" id="orderingMap">
<h3>orderingMap</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>,<wbr><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a>&gt;</span>&nbsp;<span class="element-name">orderingMap</span></div>
</section>
</li>
<li>
<section class="detail" id="options">
<h3>options</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="../proxy/YadaDTOptionsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTOptionsProxy</a></span>&nbsp;<span class="element-name">options</span></div>
</section>
</li>
<li>
<section class="detail" id="backCalled">
<h3>backCalled</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">backCalled</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(net.yadaframework.web.datatables.YadaDataTable,net.yadaframework.web.datatables.proxy.YadaDTOptionsProxy)">
<h3>YadaDataTableHTML</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">YadaDataTableHTML</span><wbr><span class="parameters">(<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&nbsp;parent,
 <a href="../proxy/YadaDTOptionsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTOptionsProxy</a>&nbsp;options)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="dtColumnObj(java.lang.String,java.lang.String)">
<h3>dtColumnObj</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></span>&nbsp;<span class="element-name">dtColumnObj</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;headerText,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;data)</span></div>
<div class="block">Add a new column to the table. Order is preserved.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>headerText</code> - the text to show in the header for this column, can be null</dd>
<dd><code>data</code> - the json path of the result in the ajax response, for example "id" or "useCredentials.username".<br>
                It can eventually be localized to access localized strings using e.g. <pre>"title."+locale.getLanguage()</pre>
                for a title in the current user locale.<br>
                Can also be a javascript function provided on page if the value is like "someFunction()".
                In that case the function signature should be <pre>function(data, type, row, meta)</pre>
                Remember in such case to set a name on the column with <a href="YadaDataTableColumn.html#dtName(java.lang.String)"><code>YadaDataTableColumn.dtName(String)</code></a> for database operations.</dd>
<dt>Returns:</dt>
<dd>column instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="https://datatables.net/reference/option/columns.data">DataTables Reference: columns.data</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtButtonObj(java.lang.String)">
<h3>dtButtonObj</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtButtonObj</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</span></div>
<div class="block">Add a new button to the table. It can appear both in the commands column and in the toolbar.
 Order is preserved.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>text</code> - Tooltip text for the command icon and text for the toolbar button. Can be a message key.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtFooter()">
<h3>dtFooter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></span>&nbsp;<span class="element-name">dtFooter</span>()</div>
<div class="block">Shows a footer with the same content as the header</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtColumnCheckbox(java.lang.String)">
<h3>dtColumnCheckbox</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></span>&nbsp;<span class="element-name">dtColumnCheckbox</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</span></div>
<div class="block">Adds the leftmost column with select checkboxes</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>the</code> - title text or key</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtColumnCommands(java.lang.String)">
<h3>dtColumnCommands</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></span>&nbsp;<span class="element-name">dtColumnCommands</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title)</span></div>
<div class="block">Adds the rightmost column with command icons</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtColumnCommands(java.lang.String,int)">
<h3>dtColumnCommands</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></span>&nbsp;<span class="element-name">dtColumnCommands</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 int&nbsp;responsivePriority)</span></div>
<div class="block">Adds the rightmost column with command icons</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtCssClasses(java.lang.String)">
<h3>dtCssClasses</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></span>&nbsp;<span class="element-name">dtCssClasses</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssClasses)</span></div>
<div class="block">Set the css classes to set on the table tag. Defaults to 'table-striped no-wrap'</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cssClasses</code> - space-separated css classes</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isSelectCheckbox()">
<h3>isSelectCheckbox</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isSelectCheckbox</span>()</div>
</section>
</li>
<li>
<section class="detail" id="back()">
<h3>back</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a></span>&nbsp;<span class="element-name">back</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../core/YadaFluentBase.html#back()">YadaFluentBase</a></code></span></div>
<div class="block">Method to return to parent for fluent chaining.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../core/YadaFluentBase.html#back()">back</a></code>&nbsp;in class&nbsp;<code><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;<a href="../YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a>&gt;</code></dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
