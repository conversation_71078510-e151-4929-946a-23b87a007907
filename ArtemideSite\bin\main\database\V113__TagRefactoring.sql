# NewsTag e ProjectTag adesso estendono Tag

drop table NewsTag_name, NewsTag_NewsJournal, NewsTag, ProjectTag_Project, ProjectTag_name, ProjectTag;

create table Tag (DTYPE varchar(31) not null, id bigint not null auto_increment, pos bigint not null, version bigint not null, primary key (id)) engine=InnoDB;
create table Tag_name (Tag_id bigint not null, name varchar(255), locale varchar(32) not null, primary key (Tag_id, locale)) engine=InnoDB;
create table Tag_NewsJournal (tags_id bigint not null, news_id bigint not null) engine=InnoDB;
create table Tag_Project (tags_id bigint not null, project_id bigint not null) engine=InnoDB;

alter table Tag_NewsJournal add constraint UKoerw895n797780rss7p918dhe unique (tags_id, news_id);
alter table Tag_Project add constraint UK9batueabncsgmvcqq2e7pdima unique (tags_id, project_id);

alter table Tag_name add constraint FK98j8p1pa1lmkhrwyi1uf6oms9 foreign key (Tag_id) references Tag (id);
alter table Tag_NewsJournal add constraint FKbi9kvka8gr1slxdu9tfb2g9rm foreign key (news_id) references NewsJournal (id);
alter table Tag_NewsJournal add constraint FK5ibxqffeg151y43hsui58l5af foreign key (tags_id) references Tag (id);
alter table Tag_Project add constraint FK4yoydb33jh5ye5h8f08boy8te foreign key (project_id) references Project (id);
alter table Tag_Project add constraint FK4i2b7p273hl63cmkoyowmdxxw foreign key (tags_id) references Tag (id);
