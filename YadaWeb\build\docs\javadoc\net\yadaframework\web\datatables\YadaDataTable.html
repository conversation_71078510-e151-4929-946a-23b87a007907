<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaDataTable (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.datatables, class: YadaDataTable">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.datatables</a></div>
<h1 title="Class YadaDataTable" class="title">Class YadaDataTable</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.web.datatables.YadaDataTable</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="proxy/YadaDataTableProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableProxy</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaDataTable</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Class representing a DataTable.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#ajaxUrl" class="member-name-link">ajaxUrl</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="YadaDataTableConfigurer.html" title="interface in net.yadaframework.web.datatables">YadaDataTableConfigurer</a></code></div>
<div class="col-second odd-row-color"><code><a href="#configurer" class="member-name-link">configurer</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></code></div>
<div class="col-second even-row-color"><code><a href="#entityClass" class="member-name-link">entityClass</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#id" class="member-name-link">id</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a></code></div>
<div class="col-second even-row-color"><code><a href="#locale" class="member-name-link">locale</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="proxy/YadaDTOptionsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTOptionsProxy</a></code></div>
<div class="col-second odd-row-color"><code><a href="#options" class="member-name-link">options</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#securityAsPath" class="member-name-link">securityAsPath</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="proxy/YadaDataTableHTMLProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableHTMLProxy</a></code></div>
<div class="col-second odd-row-color"><code><a href="#yadaDataTableHTML" class="member-name-link">yadaDataTableHTML</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="proxy/YadaDataTableLanguageProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableLanguageProxy</a></code></div>
<div class="col-second even-row-color"><code><a href="#yadaDataTableLanguage" class="member-name-link">yadaDataTableLanguage</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.util.Locale)" class="member-name-link">YadaDataTable</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructor used internally by <a href="../../components/YadaDataTableFactory.html" title="class in net.yadaframework.components"><code>YadaDataTableFactory</code></a></div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtAjaxUrl(java.lang.String)" class="member-name-link">dtAjaxUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ajaxUrl)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the URL where data is fetched from (without language in the path).</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtAjaxUrl(net.yadaframework.web.datatables.YadaDtAjaxHandler)" class="member-name-link">dtAjaxUrl</a><wbr>(<a href="YadaDtAjaxHandler.html" title="interface in net.yadaframework.web.datatables">YadaDtAjaxHandler</a>&nbsp;handlerRef)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Type-safe overload that accepts a method reference to a controller method
 and extracts its @RequestMapping path (combined with class-level mapping if present).</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtEntityClass(java.lang.Class)" class="member-name-link">dtEntityClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;entityClass)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the entity class that will be used to fetch data from the database.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="config/YadaDataTableLanguage.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableLanguage</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtLanguageObj(java.lang.String)" class="member-name-link">dtLanguageObj</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;languageBaseUrl)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the base url where language files are located.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtOptionsObj()" class="member-name-link">dtOptionsObj</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="config/YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtStructureObj()" class="member-name-link">dtStructureObj</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the HTML table structure</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#prepareConfiguration()" class="member-name-link">prepareConfiguration</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="locale">
<h3>locale</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a></span>&nbsp;<span class="element-name">locale</span></div>
</section>
</li>
<li>
<section class="detail" id="configurer">
<h3>configurer</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="YadaDataTableConfigurer.html" title="interface in net.yadaframework.web.datatables">YadaDataTableConfigurer</a></span>&nbsp;<span class="element-name">configurer</span></div>
</section>
</li>
<li>
<section class="detail" id="yadaDataTableLanguage">
<h3>yadaDataTableLanguage</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="proxy/YadaDataTableLanguageProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableLanguageProxy</a></span>&nbsp;<span class="element-name">yadaDataTableLanguage</span></div>
</section>
</li>
<li>
<section class="detail" id="entityClass">
<h3>entityClass</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a></span>&nbsp;<span class="element-name">entityClass</span></div>
</section>
</li>
<li>
<section class="detail" id="securityAsPath">
<h3>securityAsPath</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">securityAsPath</span></div>
</section>
</li>
<li>
<section class="detail" id="id">
<h3>id</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">id</span></div>
</section>
</li>
<li>
<section class="detail" id="ajaxUrl">
<h3>ajaxUrl</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ajaxUrl</span></div>
</section>
</li>
<li>
<section class="detail" id="yadaDataTableHTML">
<h3>yadaDataTableHTML</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="proxy/YadaDataTableHTMLProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableHTMLProxy</a></span>&nbsp;<span class="element-name">yadaDataTableHTML</span></div>
</section>
</li>
<li>
<section class="detail" id="options">
<h3>options</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="proxy/YadaDTOptionsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTOptionsProxy</a></span>&nbsp;<span class="element-name">options</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.util.Locale)">
<h3>YadaDataTable</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">YadaDataTable</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;id,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</span></div>
<div class="block">Constructor used internally by <a href="../../components/YadaDataTableFactory.html" title="class in net.yadaframework.components"><code>YadaDataTableFactory</code></a></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>id</code> - </dd>
<dd><code>locale</code> - </dd>
<dd><code>datasource</code> - </dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li>
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>YadaDataTableFactory#getSingleton(String, String, Locale)</code></pre>
</details>
</li>
<li>
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>#YadaDataTable(String, String)</code></pre>
</details>
</li>
</ul>
</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="prepareConfiguration()">
<h3>prepareConfiguration</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">prepareConfiguration</span>()</div>
</section>
</li>
<li>
<section class="detail" id="dtLanguageObj(java.lang.String)">
<h3>dtLanguageObj</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="config/YadaDataTableLanguage.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableLanguage</a></span>&nbsp;<span class="element-name">dtLanguageObj</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;languageBaseUrl)</span></div>
<div class="block">Set the base url where language files are located. It can be the official DataTables URL or a local endpoint.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>languageBaseUrl</code> - the URL of the root folder for the DataTable i18n translations. 
                        Can be a CDN or a local folder e.g. "/static/datatables-2.1.8/i18n" depending on where the sources have been saved.
                        It can contain Thymeleaf expressions.
                        When null or empty, the default value is used: https://cdn.datatables.net/plug-ins/2.1.8/i18n/</dd>
<dt>Returns:</dt>
<dd>an instance of YadaDataTableLanguage for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtAjaxUrl(java.lang.String)">
<h3>dtAjaxUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a></span>&nbsp;<span class="element-name">dtAjaxUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;ajaxUrl)</span></div>
<div class="block">Set the URL where data is fetched from (without language in the path). Can contain Thymeleaf expressions.
 Only to be used when the default handling is not enough.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>ajaxUrl</code> - the URL where data is fetched from</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtAjaxUrl(net.yadaframework.web.datatables.YadaDtAjaxHandler)">
<h3>dtAjaxUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a></span>&nbsp;<span class="element-name">dtAjaxUrl</span><wbr><span class="parameters">(<a href="YadaDtAjaxHandler.html" title="interface in net.yadaframework.web.datatables">YadaDtAjaxHandler</a>&nbsp;handlerRef)</span></div>
<div class="block">Type-safe overload that accepts a method reference to a controller method
 and extracts its @RequestMapping path (combined with class-level mapping if present).

 Usage example inside a controller: <pre>.dtAjaxUrl(this::userProfileTablePage)</pre>

 The referenced method is expected to have the signature:
 Map<span class="invalid-tag">invalid input: '&lt;'</span>String,Object&gt; method(YadaDatatablesRequest request, Locale locale)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>handlerRef</code> - method reference to a controller handler</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtEntityClass(java.lang.Class)">
<h3>dtEntityClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a></span>&nbsp;<span class="element-name">dtEntityClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&nbsp;entityClass)</span></div>
<div class="block">Set the entity class that will be used to fetch data from the database.
 Needs to be set when using yadaDataTableDao.getConvertedJsonPage() or an exception will be thrown
 by the ajax call.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>entityClass</code> - the entity</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li>
<details class="invalid-tag">
<summary>invalid reference</summary>
<pre><code>YadaDataTableDao#getConvertedJsonPage(YadaDatatablesRequest, Class)</code></pre>
</details>
</li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtStructureObj()">
<h3>dtStructureObj</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="config/YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></span>&nbsp;<span class="element-name">dtStructureObj</span>()</div>
<div class="block">Set the HTML table structure</div>
</section>
</li>
<li>
<section class="detail" id="dtOptionsObj()">
<h3>dtOptionsObj</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></span>&nbsp;<span class="element-name">dtOptionsObj</span>()</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
