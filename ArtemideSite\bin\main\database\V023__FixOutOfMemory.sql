 # Rimuovo i duplicati nei favoriti e aggiungo i constraint

# UserProfile_favouriteFamilies
alter table UserProfile_favouriteFamilies ADD tokeep boolean;
alter table UserProfile_favouriteFamilies add constraint preventdupe unique (UserProfile_id, favouriteFamilies, tokeep);
update ignore UserProfile_favouriteFamilies set tokeep = true;
delete from UserProfile_favouriteFamilies where tokeep is null;
alter table UserProfile_favouriteFamilies drop tokeep;

# UserProfile_favouriteProducts
alter table UserProfile_favouriteProducts ADD tokeep boolean;
alter table UserProfile_favouriteProducts add constraint preventdupe unique (UserProfile_id, favouriteProducts, tokeep);
update ignore UserProfile_favouriteProducts set tokeep = true;
delete from UserProfile_favouriteProducts where tokeep is null;
alter table UserProfile_favouriteProducts drop tokeep;

# UserProfile_favouriteProjects
alter table UserProfile_favouriteProjects ADD tokeep boolean;
alter table UserProfile_favouriteProjects add constraint preventdupe unique (UserProfile_id, favouriteProjects, tokeep);
update ignore UserProfile_favouriteProjects set tokeep = true;
delete from UserProfile_favouriteProjects where tokeep is null;
alter table UserProfile_favouriteProjects drop tokeep;

# UserProfile_favouriteSubfamilies
alter table UserProfile_favouriteSubfamilies ADD tokeep boolean;
alter table UserProfile_favouriteSubfamilies add constraint preventdupe unique (UserProfile_id, favouriteSubfamilies, tokeep);
update ignore UserProfile_favouriteSubfamilies set tokeep = true;
delete from UserProfile_favouriteSubfamilies where tokeep is null;
alter table UserProfile_favouriteSubfamilies drop tokeep;

alter table UserProfile_favouriteFamilies drop index preventdupe;
alter table UserProfile_favouriteProducts drop index preventdupe;
alter table UserProfile_favouriteProjects drop index preventdupe;
alter table UserProfile_favouriteSubfamilies drop index preventdupe;

alter table UserProfile_favouriteFamilies add constraint UKkfflduucwefrwyvn7gs7himyi unique (UserProfile_id, favouriteFamilies);
alter table UserProfile_favouriteProducts add constraint UKi4ab7pim6m5cptwqqw7s2rk27 unique (UserProfile_id, favouriteProducts);
alter table UserProfile_favouriteProjects add constraint UKo0omdlh7cdvyl88ldkq8yf9fe unique (UserProfile_id, favouriteProjects);
alter table UserProfile_favouriteSubfamilies add constraint UK7b18o7ejgpexjux2d6imlkh5t unique (UserProfile_id, favouriteSubfamilies);
