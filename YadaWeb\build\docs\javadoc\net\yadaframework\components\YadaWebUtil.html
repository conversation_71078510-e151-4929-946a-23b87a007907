<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaWebUtil (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.components, class: YadaWebUtil">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.components</a></div>
<h1 title="Class YadaWebUtil" class="title">Class YadaWebUtil</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.components.YadaWebUtil</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Lazy
@Service
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaWebUtil</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Utility methods to be used for web development</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>final <a href="../web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></code></div>
<div class="col-second even-row-color"><code><a href="#FIND_ONE" class="member-name-link">FIND_ONE</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static final <a href="YadaWebUtil.html" title="class in net.yadaframework.components">YadaWebUtil</a></code></div>
<div class="col-second odd-row-color"><code><a href="#INSTANCE" class="member-name-link">INSTANCE</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Instance to be used when autowiring is not available</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaWebUtil</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addOrUpdateUrlParameter(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">addOrUpdateUrlParameter</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceUrl,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramValue)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add a url parameter or change its value if present</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#autowireAndInitialize(java.lang.Object)" class="member-name-link">autowireAndInitialize</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;instance)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Perform autowiring of an instance that doesn't come from the Spring context, e.g.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#callScriptOnModal(java.lang.String,org.springframework.ui.Model)" class="member-name-link">callScriptOnModal</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;scriptId,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkInvalidSlugCharacters(java.lang.String)" class="member-name-link">checkInvalidSlugCharacters</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Find invalid characters from a slug: white space, :,;=<span class="invalid-tag">invalid input: '&amp;'</span>!+~\()@*$'
 The char "-" is excluded.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#cleanContent(java.lang.String,java.lang.String...)" class="member-name-link">cleanContent</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;content,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;extraTags)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Cleans the html content leaving only the following tags: b, em, i, strong, u, br, cite, em, i, p, strong, img, li, ul, ol, sup, sub, s</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#downloadFile(java.nio.file.Path,boolean,java.lang.String,java.lang.String,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">downloadFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;fileToDownload,
 boolean&nbsp;thenDeleteFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;contentType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clientFilename,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies the content of a file to the Response then optionally deletes the file.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#downloadTempFile(java.lang.String,java.lang.String,java.lang.String,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">downloadTempFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tempFilename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;contentType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clientFilename,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copies the content of a file to the Response then deletes the file.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#downloadZip(java.lang.String,java.io.File%5B%5D,java.lang.String%5B%5D,boolean,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">downloadZip</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;returnedFilename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;sourceFiles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;filenamesNoExtension,
 boolean&nbsp;ignoreErrors,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Make a zip file and send it to the client.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#enhanceUrl(java.lang.String,java.util.Locale,java.lang.String...)" class="member-name-link">enhanceUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;params)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a new URL string from a starting one, taking into account the locale in the path and optional url parameters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#ensureThymeleafUrl(java.lang.String)" class="member-name-link">ensureThymeleafUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Encloses the string in a thymeleaf url operator when missing</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBrowserCountry()" class="member-name-link">getBrowserCountry</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the first country in the request language header as a string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getBrowserLanguage()" class="member-name-link">getBrowserLanguage</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the first language in the request language header as a string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getClientAddress(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">getClientAddress</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the browser's remote ip address.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getClientIp(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">getClientIp</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getContentUrlRelative(java.io.File)" class="member-name-link">getContentUrlRelative</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;someContentFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the application-relative url of a file from the contents folder</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCookieValue(jakarta.servlet.http.HttpServletRequest,java.lang.String)" class="member-name-link">getCookieValue</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cookieName)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the value of a cookie, null if not defined</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>jakarta.servlet.http.HttpServletRequest</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentRequest()" class="member-name-link">getCurrentRequest</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the current request or null</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFileExtension(org.springframework.web.multipart.MultipartFile)" class="member-name-link">getFileExtension</a><wbr>(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">ritorna ad esempio "jpg" lowercase</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFullUrl(java.lang.String,java.util.Locale,java.lang.String...)" class="member-name-link">getFullUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeUrl,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;params)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns a full url, including the server address and any optional request parameters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getModalConfirmViewName()" class="member-name-link">getModalConfirmViewName</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the view name for the confirmation modal.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRandomId12()" class="member-name-link">getRandomId12</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convenience method for generating a random id of 12 characters to be used in pages with "@yadaWebUtil.randomId12"</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRandomId6()" class="member-name-link">getRandomId6</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Convenience method for generating a random id of 6 characters to be used in pages with "@yadaWebUtil.randomId6"</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRequestMapping()" class="member-name-link">getRequestMapping</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the last part of the current request, for example from "/some/product" returns "product"
 This version does not require the request to be passed as parameter.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRequestMapping(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">getRequestMapping</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the last part of the current request, for example from "/some/product" returns "product"</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#getWebappAddress(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">getWebappAddress</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use YadaConfiguration.getWebappAddress instead, because this version does not work behind an ajp connector</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasGlobalError(org.springframework.validation.BindingResult,java.lang.String)" class="member-name-link">hasGlobalError</a><wbr>(org.springframework.validation.BindingResult&nbsp;binding,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorCode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Checks if a global error with the given code already exists in the binding result.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isAjaxRequest()" class="member-name-link">isAjaxRequest</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the current request is an ajax request</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isAjaxRequest(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">isAjaxRequest</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the request is an ajax request</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isEmpty(net.yadaframework.web.YadaPageRows)" class="member-name-link">isEmpty</a><wbr>(<a href="../web/YadaPageRows.html" title="class in net.yadaframework.web">YadaPageRows</a>&lt;?&gt;&nbsp;yadaPageRows)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isErrorPage(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">isErrorPage</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if we are in a forward that should display an error handled by YadaController.yadaError() or YadaGlobalExceptionHandler</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFragment(java.lang.Object)" class="member-name-link">isFragment</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;someObject)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if an object holds a thymeleaf fragment instance.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#isModalError(org.springframework.ui.Model)" class="member-name-link">isModalError</a><wbr>(org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isMultipartMissing(org.springframework.web.multipart.MultipartFile)" class="member-name-link">isMultipartMissing</a><wbr>(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the MultipartFile has not been uploaded at all, not even an empty file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#isNotifyModalPending(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">isNotifyModalPending</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#isNotifyModalRequested(org.springframework.ui.Model)" class="member-name-link">isNotifyModalRequested</a><wbr>(org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isWebImage(java.lang.String)" class="member-name-link">isWebImage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extension)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indica se l'estensione appartiene a un'immagine accettabile</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeSlug(java.lang.String)" class="member-name-link">makeSlug</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;source)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">From a given string, creates a "slug" that can be inserted in a url and still be readable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#makeSlugStatic(java.lang.String)" class="member-name-link">makeSlugStatic</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;source)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">From a given string, creates a "slug" that can be inserted in a url and still be readable.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeUrl(java.lang.String...)" class="member-name-link">makeUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;segments)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Assembles a url given its parts as string.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#makeUrl(java.lang.String%5B%5D,java.util.Map,java.lang.Boolean)" class="member-name-link">makeUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;segments,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;requestParams,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;urlEncode)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Assembles a url given its parts as string.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#modalAutoclose(long,org.springframework.ui.Model)" class="member-name-link">modalAutoclose</a><wbr>(long&nbsp;milliseconds,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#modalAutoclose(long,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">modalAutoclose</a><wbr>(long&nbsp;milliseconds,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#modalConfirm(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)" class="member-name-link">modalConfirm</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;confirmButton,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cancelButton,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#modalConfirm(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,java.lang.Boolean,java.lang.Boolean)" class="member-name-link">modalConfirm</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;confirmButton,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cancelButton,
 org.springframework.ui.Model&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;reloadOnConfirm,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;openModal)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Show the confirm modal and optionally reloads when the confirm button is pressed</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#modalConfirmAndReload(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)" class="member-name-link">modalConfirmAndReload</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;confirmButton,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cancelButton,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Show the confirm modal and reloads when the confirm button is pressed, adding the confirmation parameter to the url.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#modalError(java.lang.String,java.lang.String,org.springframework.ui.Model)" class="member-name-link">modalError</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#modalError(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">modalError</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#modalInfo(java.lang.String,java.lang.String,org.springframework.ui.Model)" class="member-name-link">modalInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#modalInfo(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">modalInfo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#modalOk(java.lang.String,java.lang.String,org.springframework.ui.Model)" class="member-name-link">modalOk</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#modalOk(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">modalOk</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#modalReloadOnClose(org.springframework.ui.Model)" class="member-name-link">modalReloadOnClose</a><wbr>(org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#modalReloadOnClose(org.springframework.web.servlet.mvc.support.RedirectAttributes)" class="member-name-link">modalReloadOnClose</a><wbr>(org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#notifyModal(java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)" class="member-name-link">notifyModal</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;severity,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;redirectSemiurl,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#passThrough(org.springframework.ui.Model,jakarta.servlet.http.HttpServletRequest,java.lang.String...)" class="member-name-link">passThrough</a><wbr>(org.springframework.ui.Model&nbsp;model,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;nameFilter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Adds all request parameters to the Model, optionally filtering by name.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#redirectString(java.lang.String,java.util.Locale,java.lang.String...)" class="member-name-link">redirectString</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;params)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Create a redirect string to be returned by a @Controller, taking into account the locale in the path.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#registerDynamicMapping(java.lang.String,java.lang.Object,java.lang.String,org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping)" class="member-name-link">registerDynamicMapping</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;controllerInstance,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;methodName,
 org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping&nbsp;requestMappingHandlerMapping)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a dynamic @RequestMapping i.e.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T&gt;&nbsp;org.springframework.validation.BindingResult</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#relinkBindingResult(org.springframework.validation.BindingResult,T,org.springframework.ui.Model)" class="member-name-link">relinkBindingResult</a><wbr>(org.springframework.validation.BindingResult&nbsp;originalBindingResult,
 T&nbsp;newTargetObject,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a new BindingResult associated with a new instance of the same type,
 preserving all error information from the original BindingResult.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#removeHtmlStatic(java.lang.String)" class="member-name-link">removeHtmlStatic</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;source)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Removes all HTML tags.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#removeLanguageFromOurUrl(java.lang.String,jakarta.servlet.http.HttpServletRequest)" class="member-name-link">removeLanguageFromOurUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fullUrlWithHttp,
 jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">If the url is a full url that points to our server, make it relative to the server and strip any language in the path.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sanitizeUrl(java.lang.String)" class="member-name-link">sanitizeUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Fix a url so that it valid and doesn't allow XSS attacks</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#saveAttachment(org.springframework.web.multipart.MultipartFile)" class="member-name-link">saveAttachment</a><wbr>(org.springframework.web.multipart.MultipartFile&nbsp;attachment)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Save an uploaded file to a temporary file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#saveAttachment(org.springframework.web.multipart.MultipartFile,java.io.File)" class="member-name-link">saveAttachment</a><wbr>(org.springframework.web.multipart.MultipartFile&nbsp;attachment,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Save an uploaded file to the given target file</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#saveAttachment(org.springframework.web.multipart.MultipartFile,java.nio.file.Path)" class="member-name-link">saveAttachment</a><wbr>(org.springframework.web.multipart.MultipartFile&nbsp;attachment,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;targetPath)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Save an uploaded file to the given target file</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#saveBase64Image(java.lang.String,java.io.File)" class="member-name-link">saveBase64Image</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;base64Image,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFileNoExtension)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Save to disk a base64 image received from javascript.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setCookie(java.lang.String,java.lang.String,int,jakarta.servlet.http.HttpServletResponse)" class="member-name-link">setCookie</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cookieName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cookieValue,
 int&nbsp;expirationDays,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a cookie</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>&lt;T extends <a href="../core/YadaLocalEnum.html" title="interface in net.yadaframework.core">YadaLocalEnum</a>&lt;?&gt;&gt;<br><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;T&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#sortLocalEnum(java.lang.Class,java.util.Locale)" class="member-name-link">sortLocalEnum</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;localEnum,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Sorts a localized enum according to the locale specified</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#urlDecode(java.lang.String)" class="member-name-link">urlDecode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;source)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Decodes a string with URLDecoder, handling the useless try-catch that is needed</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#urlEncode(java.lang.String)" class="member-name-link">urlEncode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;source)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Encodes a string with URLEncoder, handling the useless try-catch that is needed</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#versionifyResourceUrl(java.lang.String)" class="member-name-link">versionifyResourceUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;urlNotVersioned)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Trasforma /res/img/favicon.ico in /res-0002/img/favicon.ico</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="INSTANCE">
<h3>INSTANCE</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="YadaWebUtil.html" title="class in net.yadaframework.components">YadaWebUtil</a></span>&nbsp;<span class="element-name">INSTANCE</span></div>
<div class="block">Instance to be used when autowiring is not available</div>
</section>
</li>
<li>
<section class="detail" id="FIND_ONE">
<h3>FIND_ONE</h3>
<div class="member-signature"><span class="modifiers">public final</span>&nbsp;<span class="return-type"><a href="../web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></span>&nbsp;<span class="element-name">FIND_ONE</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaWebUtil</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaWebUtil</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getRandomId6()">
<h3>getRandomId6</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRandomId6</span>()</div>
<div class="block">Convenience method for generating a random id of 6 characters to be used in pages with "@yadaWebUtil.randomId6"</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a random string of 6 characters</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="YadaUtil.html#getRandomString(int)"><code>YadaUtil.getRandomString(length)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRandomId12()">
<h3>getRandomId12</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRandomId12</span>()</div>
<div class="block">Convenience method for generating a random id of 12 characters to be used in pages with "@yadaWebUtil.randomId12"</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a random string of 12 characters</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="YadaUtil.html#getRandomString(int)"><code>YadaUtil.getRandomString(length)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFragment(java.lang.Object)">
<h3>isFragment</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFragment</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;someObject)</span></div>
<div class="block">Check if an object holds a thymeleaf fragment instance. To be used in Thymeleaf templates.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>someObject</code> - </dd>
<dt>Returns:</dt>
<dd>true if the parameter is a fragment</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasGlobalError(org.springframework.validation.BindingResult,java.lang.String)">
<h3>hasGlobalError</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasGlobalError</span><wbr><span class="parameters">(org.springframework.validation.BindingResult&nbsp;binding,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;errorCode)</span></div>
<div class="block">Checks if a global error with the given code already exists in the binding result.
 Useful to avoid adding the same error multiple times.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>binding</code> - The binding result to check</dd>
<dd><code>errorCode</code> - The error code to look for</dd>
<dt>Returns:</dt>
<dd>true if an error with the given code already exists</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="relinkBindingResult(org.springframework.validation.BindingResult,T,org.springframework.ui.Model)">
<h3 id="relinkBindingResult(org.springframework.validation.BindingResult,java.lang.Object,org.springframework.ui.Model)">relinkBindingResult</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T&gt;</span>
<span class="return-type">org.springframework.validation.BindingResult</span>&nbsp;<span class="element-name">relinkBindingResult</span><wbr><span class="parameters">(org.springframework.validation.BindingResult&nbsp;originalBindingResult,
 T&nbsp;newTargetObject,
 org.springframework.ui.Model&nbsp;model)</span></div>
<div class="block">Creates a new BindingResult associated with a new instance of the same type,
 preserving all error information from the original BindingResult.</div>
<dl class="notes">
<dt>Type Parameters:</dt>
<dd><code>T</code> - The type of the target object</dd>
<dt>Parameters:</dt>
<dd><code>originalBindingResult</code> - The original BindingResult with error information</dd>
<dd><code>newTargetObject</code> - The new object instance to associate with the errors</dd>
<dd><code>model</code> - The Spring MVC Model to update with the new BindingResult</dd>
<dt>Returns:</dt>
<dd>A new BindingResult with the same errors, now linked to the new object</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isMultipartMissing(org.springframework.web.multipart.MultipartFile)">
<h3>isMultipartMissing</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isMultipartMissing</span><wbr><span class="parameters">(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile)</span></div>
<div class="block">Returns true if the MultipartFile has not been uploaded at all, not even an empty file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>multipartFile</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="registerDynamicMapping(java.lang.String,java.lang.Object,java.lang.String,org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping)">
<h3>registerDynamicMapping</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">registerDynamicMapping</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newPath,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;controllerInstance,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;methodName,
 org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping&nbsp;requestMappingHandlerMapping)</span></div>
<div class="block">Creates a dynamic @RequestMapping i.e. there's no need for it to be annotated and located in a controller instance.
 Useful if the url can be user-defined via some cms for example.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>newPath</code> - some url that the code will handle, like "/mypage"</dd>
<dd><code>controllerInstance</code> - the instance of a class that will handle the page. It can be a @Controller but it's not required.</dd>
<dd><code>methodName</code> - the name of the method that will handle the page. The method signature must be (Model, Locale).</dd>
<dd><code>requestMappingHandlerMapping</code> - retrieve this via autowiring because it can't be autowired in YadaWebUtil directly</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getContentUrlRelative(java.io.File)">
<h3>getContentUrlRelative</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getContentUrlRelative</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;someContentFile)</span></div>
<div class="block">Returns the application-relative url of a file from the contents folder</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>someContentFile</code> - some file that is inside the contents folder at any depth</dd>
<dt>Returns:</dt>
<dd>a string like "/contents/path/to/file.gif"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setCookie(java.lang.String,java.lang.String,int,jakarta.servlet.http.HttpServletResponse)">
<h3>setCookie</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setCookie</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cookieName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cookieValue,
 int&nbsp;expirationDays,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span></div>
<div class="block">Set a cookie</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cookieName</code> - </dd>
<dd><code>cookieValue</code> - </dd>
<dd><code>expirationDays</code> - can be 0 to delete the cookie or <span class="invalid-tag">invalid input: '&lt;'</span>0 to persist until browser shutdown</dd>
<dd><code>response</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCookieValue(jakarta.servlet.http.HttpServletRequest,java.lang.String)">
<h3>getCookieValue</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getCookieValue</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cookieName)</span></div>
<div class="block">Returns the value of a cookie, null if not defined</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dd><code>cookieName</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="saveBase64Image(java.lang.String,java.io.File)">
<h3>saveBase64Image</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">saveBase64Image</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;base64Image,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFileNoExtension)</span></div>
<div class="block">Save to disk a base64 image received from javascript.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>base64Image</code> - the image string in the format data:image/png;base64,xxxxxxxxx where image/png can be any image format and xxxxxxxxx is the encoded image</dd>
<dd><code>targetFileNoExtension</code> - the target file without extension: the extension is taken from base64Image</dd>
<dt>Returns:</dt>
<dd>the saved file (with extension)</dd>
<dt>Throws:</dt>
<dd><code><a href="../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - if the image can't be decoded</dd>
<dd><code><a href="../exceptions/YadaSystemException.html" title="class in net.yadaframework.exceptions">YadaSystemException</a></code> - if saving fails</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="autowireAndInitialize(java.lang.Object)">
<h3>autowireAndInitialize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span>&nbsp;<span class="element-name">autowireAndInitialize</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;instance)</span></div>
<div class="block">Perform autowiring of an instance that doesn't come from the Spring context, e.g. a JPA @Entity or normal java instance made with new.
 Post processing (@PostConstruct etc) and initialization are also performed.
 This version can inject beans from the WebApplicationContext like @Controllers. You may consider a class hierarchy redesign before using this method.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>instance</code> - to autowire</dd>
<dt>Returns:</dt>
<dd>the autowired/initialized bean instance, either the original or a wrapped one</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isEmpty(net.yadaframework.web.YadaPageRows)">
<h3>isEmpty</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isEmpty</span><wbr><span class="parameters">(<a href="../web/YadaPageRows.html" title="class in net.yadaframework.web">YadaPageRows</a>&lt;?&gt;&nbsp;yadaPageRows)</span></div>
</section>
</li>
<li>
<section class="detail" id="getRequestMapping(jakarta.servlet.http.HttpServletRequest)">
<h3>getRequestMapping</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRequestMapping</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="block">Returns the last part of the current request, for example from "/some/product" returns "product"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getRequestMapping()"><code>getRequestMapping()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRequestMapping()">
<h3>getRequestMapping</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getRequestMapping</span>()</div>
<div class="block">Returns the last part of the current request, for example from "/some/product" returns "product"
 This version does not require the request to be passed as parameter.</div>
<dl class="notes">
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#getRequestMapping(jakarta.servlet.http.HttpServletRequest)"><code>getRequestMapping(HttpServletRequest)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeLanguageFromOurUrl(java.lang.String,jakarta.servlet.http.HttpServletRequest)">
<h3>removeLanguageFromOurUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">removeLanguageFromOurUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;fullUrlWithHttp,
 jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="block">If the url is a full url that points to our server, make it relative to the server and strip any language in the path.
 The result can be used in thymeleaf @{url} statements and will get the proper browser language when needed.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fullUrlWithHttp</code> - something like "https://my.site.com/en/something/here", can be empty or null</dd>
<dd><code>request</code> - </dd>
<dt>Returns:</dt>
<dd>someting like "/something/here", or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="passThrough(org.springframework.ui.Model,jakarta.servlet.http.HttpServletRequest,java.lang.String...)">
<h3>passThrough</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">passThrough</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model,
 jakarta.servlet.http.HttpServletRequest&nbsp;request,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;nameFilter)</span></div>
<div class="block">Adds all request parameters to the Model, optionally filtering by name.
 Existing model attributes are not overwritten.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - </dd>
<dd><code>request</code> - </dd>
<dd><code>nameFilter</code> - parameter names that should pass through to the Model</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addOrUpdateUrlParameter(java.lang.String,java.lang.String,java.lang.String)">
<h3>addOrUpdateUrlParameter</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">addOrUpdateUrlParameter</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sourceUrl,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramValue)</span></div>
<div class="block">Add a url parameter or change its value if present</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sourceUrl</code> - a full or relative url. Can also be just the query string starting with "?"</dd>
<dd><code>paramName</code> - the name of the parameter, not urlencoded</dd>
<dd><code>paramValue</code> - the value of the parameter, not urlencoded. Can be null to only have the paramName in the url</dd>
<dt>Returns:</dt>
<dd>the original url with the parameter added or updated</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFullUrl(java.lang.String,java.util.Locale,java.lang.String...)">
<h3>getFullUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFullUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;relativeUrl,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;params)</span></div>
<div class="block">Returns a full url, including the server address and any optional request parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>relativeUrl</code> - a server-relative url without language component</dd>
<dd><code>locale</code> - </dd>
<dd><code>params</code> - optional request parameters to be set on the url, in the form of comma-separated name,value pairs. E.g. "id","123","name","joe".
                        Existing parameters are not replaced. Null values become empty strings. Null names are skipped with their values.</dd>
<dt>Returns:</dt>
<dd>a full url like https://myapp.com/en/relative/url</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isErrorPage(jakarta.servlet.http.HttpServletRequest)">
<h3>isErrorPage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isErrorPage</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="block">Returns true if we are in a forward that should display an error handled by YadaController.yadaError() or YadaGlobalExceptionHandler</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="downloadTempFile(java.lang.String,java.lang.String,java.lang.String,jakarta.servlet.http.HttpServletResponse)">
<h3>downloadTempFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">downloadTempFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;tempFilename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;contentType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clientFilename,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span></div>
<div class="block">Copies the content of a file to the Response then deletes the file.
 To be used when the client needs to download a previously-created temporary file that you don't want to keep on server.
 Remember to set the "produces" attribute on the @RequestMapping with the appropriate content-type.
 
 This version is different from <a href="#downloadFile(java.nio.file.Path,boolean,java.lang.String,java.lang.String,jakarta.servlet.http.HttpServletResponse)"><code>downloadFile(Path, boolean, String, String, HttpServletResponse)</code></a> because
 it can be used when you don't have the source temp File but only its name, for example because the temp file
 has been created in a previous request that redirected to the download url sending just the name of the temp file.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>tempFilename</code> - the name (without path) of the existing temporary file, created with Files.createTempFile()</dd>
<dd><code>contentType</code> - the content-type header</dd>
<dd><code>clientFilename</code> - the filename that will be used on the client browser</dd>
<dd><code>response</code> - the HTTP response</dd>
<dt>Returns:</dt>
<dd>true if the file was sent without errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="downloadFile(java.nio.file.Path,boolean,java.lang.String,java.lang.String,jakarta.servlet.http.HttpServletResponse)">
<h3>downloadFile</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">downloadFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;fileToDownload,
 boolean&nbsp;thenDeleteFile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;contentType,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;clientFilename,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span></div>
<div class="block">Copies the content of a file to the Response then optionally deletes the file.
 To be used when the client needs to download a previously-created file that you don't want to keep on server.
 No need to set the "produces" attribute on the @RequestMapping with the appropriate content-type.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>fileToDownload</code> - the existing file to download and delete</dd>
<dd><code>thenDeleteFile</code> - true to delete the file when download ends (or fails)</dd>
<dd><code>contentType</code> - the content-type header</dd>
<dd><code>clientFilename</code> - the filename that will be used on the client browser</dd>
<dd><code>response</code> - the HTTP response</dd>
<dt>Returns:</dt>
<dd>true if the file was sent without errors</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="redirectString(java.lang.String,java.util.Locale,java.lang.String...)">
<h3>redirectString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">redirectString</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;params)</span></div>
<div class="block">Create a redirect string to be returned by a @Controller, taking into account the locale in the path.
 If you can do a redirect with a relative url ("some/url") you don't need to use this method because the language path
 won't be overwritten. Otherwise if you need to use an absolute url ("/some/url") then this method inserts the appropriate language path
 in the url (and any parameters at the end too).</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>locale</code> - can be null if the locale is not in the path, but then why use this method?</dd>
<dd><code>params</code> - optional request parameters to be set on the url, in the form of comma-separated name,value pairs. E.g. "id","123","name","joe"
                        Existing parameters are not replaced. Null values become empty strings. Null names are skipped with their values.</dd>
<dd><code>targetUrl</code> - the redirect target, like "/some/place"</dd>
<dt>Returns:</dt>
<dd>a url like "redirect:/en/some/place?par1=val1<span class="invalid-tag">invalid input: '&amp;par2'</span>=val2"</dd>
<dt>Throws:</dt>
<dd><code><a href="../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - if path locale is configured and the url is absolute and the locale is null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="enhanceUrl(java.lang.String,java.util.Locale,java.lang.String...)">
<h3>enhanceUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">enhanceUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;params)</span></div>
<div class="block">Creates a new URL string from a starting one, taking into account the locale in the path and optional url parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>url</code> - the original url, like "/some/place"</dd>
<dd><code>locale</code> - added as a path in the url, only if the url is absolute. Can be null if the locale is not needed in the path</dd>
<dd><code>params</code> - optional request parameters to be set on the url, in the form of comma-separated name,value pairs. E.g. "id","123","name","joe".
                        Existing parameters are not replaced. Null values become empty strings. Null names are skipped with their values.</dd>
<dt>Returns:</dt>
<dd>a url like "/en/some/place?id=123<span class="invalid-tag">invalid input: '&amp;name'</span>=joe"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="downloadZip(java.lang.String,java.io.File[],java.lang.String[],boolean,jakarta.servlet.http.HttpServletResponse)">
<h3>downloadZip</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">downloadZip</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;returnedFilename,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>[]&nbsp;sourceFiles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;filenamesNoExtension,
 boolean&nbsp;ignoreErrors,
 jakarta.servlet.http.HttpServletResponse&nbsp;response)</span>
                 throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></span></div>
<div class="block">Make a zip file and send it to the client. The temp file is automatically deleted.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>returnedFilename</code> - the name of the file to create and send, with extension. E.g.: data.zip</dd>
<dd><code>sourceFiles</code> - the files to zip</dd>
<dd><code>filenamesNoExtension</code> - the name of each file in the zip - null to keep the original names</dd>
<dd><code>ignoreErrors</code> - true to ignore errors when adding a file and keep going, false for an exception when a file can't be zipped</dd>
<dd><code>response</code> - </dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sortLocalEnum(java.lang.Class,java.util.Locale)">
<h3>sortLocalEnum</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="type-parameters">&lt;T extends <a href="../core/YadaLocalEnum.html" title="interface in net.yadaframework.core">YadaLocalEnum</a>&lt;?&gt;&gt;</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;T&gt;</span>&nbsp;<span class="element-name">sortLocalEnum</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Class.html" title="class or interface in java.lang" class="external-link">Class</a>&lt;T&gt;&nbsp;localEnum,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Locale.html" title="class or interface in java.util" class="external-link">Locale</a>&nbsp;locale)</span></div>
<div class="block">Sorts a localized enum according to the locale specified</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>localEnum</code> - the class of the enum, e.g. net.yadaframework.persistence.entity.YadaJobState.class</dd>
<dd><code>locale</code> - </dd>
<dt>Returns:</dt>
<dd>a list of sorted enums of the given class</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBrowserLanguage()">
<h3>getBrowserLanguage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getBrowserLanguage</span>()</div>
<div class="block">Returns the first language in the request language header as a string.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the language string, like "en_US", or "" if not found</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getBrowserCountry()">
<h3>getBrowserCountry</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getBrowserCountry</span>()</div>
<div class="block">Returns the first country in the request language header as a string.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the country string, like "US", or "" if not found</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="saveAttachment(org.springframework.web.multipart.MultipartFile)">
<h3>saveAttachment</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a></span>&nbsp;<span class="element-name">saveAttachment</span><wbr><span class="parameters">(org.springframework.web.multipart.MultipartFile&nbsp;attachment)</span>
                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Save an uploaded file to a temporary file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attachment</code> - </dd>
<dt>Returns:</dt>
<dd>the temporary file holding the uploaded file, or null if no file has bee attached</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="saveAttachment(org.springframework.web.multipart.MultipartFile,java.nio.file.Path)">
<h3>saveAttachment</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">saveAttachment</span><wbr><span class="parameters">(org.springframework.web.multipart.MultipartFile&nbsp;attachment,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/nio/file/Path.html" title="class or interface in java.nio.file" class="external-link">Path</a>&nbsp;targetPath)</span>
                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Save an uploaded file to the given target file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attachment</code> - </dd>
<dd><code>targetPath</code> - </dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="saveAttachment(org.springframework.web.multipart.MultipartFile,java.io.File)">
<h3>saveAttachment</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">saveAttachment</span><wbr><span class="parameters">(org.springframework.web.multipart.MultipartFile&nbsp;attachment,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/File.html" title="class or interface in java.io" class="external-link">File</a>&nbsp;targetFile)</span>
                    throws <span class="exceptions"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></span></div>
<div class="block">Save an uploaded file to the given target file</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>attachment</code> - </dd>
<dd><code>targetFile</code> - </dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/io/IOException.html" title="class or interface in java.io" class="external-link">IOException</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="sanitizeUrl(java.lang.String)">
<h3>sanitizeUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">sanitizeUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url)</span></div>
<div class="block">Fix a url so that it valid and doesn't allow XSS attacks</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>url</code> - some text typed by the user</dd>
<dt>Returns:</dt>
<dd>the same url or null if blank or a fixed one that may or may not work as expected, but won't pose a security risk</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeUrl(java.lang.String...)">
<h3>makeUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">makeUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;segments)</span></div>
<div class="block">Assembles a url given its parts as string.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>segments</code> - the initial parts of the url up to the query string. Leading and trailing slashes are added when missing.
 URLEncoding is not performed.</dd>
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#makeUrl(java.lang.String%5B%5D,java.util.Map,java.lang.Boolean)"><code>makeUrl(String[], Map, Boolean)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeUrl(java.lang.String[],java.util.Map,java.lang.Boolean)">
<h3>makeUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">makeUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;segments,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;requestParams,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;urlEncode)</span></div>
<div class="block">Assembles a url given its parts as string.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>segments</code> - the initial parts of the url up to the query string. Leading and trailing slashes are added when missing.</dd>
<dd><code>requestParams</code> - optional name/value pairs of request parameters that will compose the query string.
 Use null for no parameters, use a null value for no value (just the name will be added)</dd>
<dd><code>urlEncode</code> - use Boolean.TRUE to encode the parameters, null or anything else not to encode</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeSlug(java.lang.String)">
<h3>makeSlug</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">makeSlug</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;source)</span></div>
<div class="block">From a given string, creates a "slug" that can be inserted in a url and still be readable.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - the string to convert</dd>
<dt>Returns:</dt>
<dd>the slug</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="makeSlugStatic(java.lang.String)">
<h3>makeSlugStatic</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">makeSlugStatic</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;source)</span></div>
<div class="block">From a given string, creates a "slug" that can be inserted in a url and still be readable.
 It is static so that it can be used in Entity objects (no context)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - the string to convert</dd>
<dt>Returns:</dt>
<dd>the slug, which is empty for a null string</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="checkInvalidSlugCharacters(java.lang.String)">
<h3>checkInvalidSlugCharacters</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">checkInvalidSlugCharacters</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text)</span></div>
<div class="block">Find invalid characters from a slug: white space, :,;=<span class="invalid-tag">invalid input: '&amp;'</span>!+~\()@*$'
 The char "-" is excluded.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>text</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="urlDecode(java.lang.String)">
<h3>urlDecode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">urlDecode</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;source)</span></div>
<div class="block">Decodes a string with URLDecoder, handling the useless try-catch that is needed</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="urlEncode(java.lang.String)">
<h3>urlEncode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">urlEncode</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;source)</span></div>
<div class="block">Encodes a string with URLEncoder, handling the useless try-catch that is needed</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - </dd>
<dt>Returns:</dt>
<dd>the encoded source or null if the source is null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentRequest()">
<h3>getCurrentRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">jakarta.servlet.http.HttpServletRequest</span>&nbsp;<span class="element-name">getCurrentRequest</span>()</div>
<div class="block">Returns the current request or null</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isAjaxRequest()">
<h3>isAjaxRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isAjaxRequest</span>()</div>
<div class="block">Returns true if the current request is an ajax request</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isAjaxRequest(jakarta.servlet.http.HttpServletRequest)">
<h3>isAjaxRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isAjaxRequest</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="block">Returns true if the request is an ajax request</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="versionifyResourceUrl(java.lang.String)">
<h3>versionifyResourceUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">versionifyResourceUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;urlNotVersioned)</span></div>
<div class="block">Trasforma /res/img/favicon.ico in /res-0002/img/favicon.ico</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>urlNotVersioned</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="removeHtmlStatic(java.lang.String)">
<h3>removeHtmlStatic</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">removeHtmlStatic</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;source)</span></div>
<div class="block">Removes all HTML tags. Static to be used from Entity beans without forcing autowiring on each instance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>source</code> - html content</dd>
<dt>Returns:</dt>
<dd>text content</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="#cleanContent(java.lang.String,java.lang.String...)"><code>cleanContent(String, String...)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="cleanContent(java.lang.String,java.lang.String...)">
<h3>cleanContent</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">cleanContent</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;content,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;extraTags)</span></div>
<div class="block">Cleans the html content leaving only the following tags: b, em, i, strong, u, br, cite, em, i, p, strong, img, li, ul, ol, sup, sub, s</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>content</code> - html content</dd>
<dd><code>extraTags</code> - any other tags that you may want to keep, e. g. "a"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getWebappAddress(jakarta.servlet.http.HttpServletRequest)">
<h3>getWebappAddress</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getWebappAddress</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use YadaConfiguration.getWebappAddress instead, because this version does not work behind an ajp connector</div>
</div>
<div class="block">Ritorna l'indirizzo completo della webapp, tipo http://www.yadaframework.net:8080/site, senza slash finale
 Da thymeleaf si usa con $</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isWebImage(java.lang.String)">
<h3>isWebImage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isWebImage</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;extension)</span></div>
<div class="block">Indica se l'estensione appartiene a un'immagine accettabile</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>extension</code> - e.g. "jpg"</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFileExtension(org.springframework.web.multipart.MultipartFile)">
<h3>getFileExtension</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getFileExtension</span><wbr><span class="parameters">(org.springframework.web.multipart.MultipartFile&nbsp;multipartFile)</span></div>
<div class="block">ritorna ad esempio "jpg" lowercase</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>multipartFile</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalError(java.lang.String,java.lang.String,org.springframework.ui.Model)">
<h3>modalError</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">modalError</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.ui.Model&nbsp;model)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Visualizza un errore se non viene fatto redirect</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dd><code>message</code> - </dd>
<dd><code>model</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalInfo(java.lang.String,java.lang.String,org.springframework.ui.Model)">
<h3>modalInfo</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">modalInfo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.ui.Model&nbsp;model)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dd><code>message</code> - </dd>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalOk(java.lang.String,java.lang.String,org.springframework.ui.Model)">
<h3>modalOk</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">modalOk</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.ui.Model&nbsp;model)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dd><code>message</code> - </dd>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalError(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>modalError</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">modalError</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Visualizza un errore se viene fatto redirect</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dd><code>message</code> - </dd>
<dd><code>redirectAttributes</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalInfo(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>modalInfo</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">modalInfo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dd><code>message</code> - </dd>
<dd><code>redirectAttributes</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalOk(java.lang.String,java.lang.String,org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>modalOk</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">modalOk</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dd><code>message</code> - </dd>
<dd><code>redirectAttributes</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalAutoclose(long,org.springframework.ui.Model)">
<h3>modalAutoclose</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">modalAutoclose</span><wbr><span class="parameters">(long&nbsp;milliseconds,
 org.springframework.ui.Model&nbsp;model)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Set the automatic closing time for the notification - no close button is shown</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>milliseconds</code> - </dd>
<dd><code>model</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalAutoclose(long,org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>modalAutoclose</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">modalAutoclose</span><wbr><span class="parameters">(long&nbsp;milliseconds,
 org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Set the automatic closing time for the notification - no close button is shown</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>milliseconds</code> - </dd>
<dd><code>redirectAttributes</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalReloadOnClose(org.springframework.ui.Model)">
<h3>modalReloadOnClose</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">modalReloadOnClose</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Set the page to reload when the modal is closed</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalReloadOnClose(org.springframework.web.servlet.mvc.support.RedirectAttributes)">
<h3>modalReloadOnClose</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">modalReloadOnClose</span><wbr><span class="parameters">(org.springframework.web.servlet.mvc.support.RedirectAttributes&nbsp;redirectAttributes)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Set the page to reload when the modal is closed</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>redirectAttributes</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isNotifyModalPending(jakarta.servlet.http.HttpServletRequest)">
<h3>isNotifyModalPending</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isNotifyModalPending</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Test if a modal is going to be opened when back to the view (usually after a redirect)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dt>Returns:</dt>
<dd>true if a modal is going to be opened</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="notifyModal(java.lang.String,java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)">
<h3>notifyModal</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">notifyModal</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;title,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;severity,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;redirectSemiurl,
 org.springframework.ui.Model&nbsp;model)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Da usare direttamente solo quando si vuole fare un redirect dopo aver mostrato un messaggio.
 Se chiamato tante volte, i messaggi si sommano e vengono mostrati tutti all'utente.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>title</code> - </dd>
<dd><code>message</code> - </dd>
<dd><code>severity</code> - a string like YadaConstants.VAL_NOTIFICATION_SEVERITY_OK</dd>
<dd><code>redirectSemiurl</code> - e.g. "/user/profile"</dd>
<dd><code>model</code> - </dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="../core/YadaConstants.html" title="interface in net.yadaframework.core"><code>YadaConstants</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isModalError(org.springframework.ui.Model)">
<h3>isModalError</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isModalError</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Return true if modalError has been called before</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isNotifyModalRequested(org.springframework.ui.Model)">
<h3>isNotifyModalRequested</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isNotifyModalRequested</span><wbr><span class="parameters">(org.springframework.ui.Model&nbsp;model)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Return true if for this thread the notifyModal (or a variant) has been called</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>model</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClientAddress(jakarta.servlet.http.HttpServletRequest)">
<h3>getClientAddress</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getClientAddress</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="block">Returns the browser's remote ip address.
 If the connection uses a proxy that sets the "X-Forwarded-For" header, the result is taken from that header.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dt>Returns:</dt>
<dd>The client IP address, ignoring any proxy address when possible</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getClientIp(jakarta.servlet.http.HttpServletRequest)">
<h3>getClientIp</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getClientIp</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
</section>
</li>
<li>
<section class="detail" id="modalConfirm(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)">
<h3>modalConfirm</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">modalConfirm</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;confirmButton,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cancelButton,
 org.springframework.ui.Model&nbsp;model)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - text to be displayed (can be null for default value)</dd>
<dd><code>confirmButton</code> - text of the confirm button (can be null for default value)</dd>
<dd><code>cancelButton</code> - text of the cancel button (can be null for default value)</dd>
<dd><code>model</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalConfirmAndReload(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model)">
<h3>modalConfirmAndReload</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">modalConfirmAndReload</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;confirmButton,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cancelButton,
 org.springframework.ui.Model&nbsp;model)</span></div>
<div class="block">Show the confirm modal and reloads when the confirm button is pressed, adding the confirmation parameter to the url.
 The modal will be opened on load.
 Usually used by non-ajax methods.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - text to be displayed (can be null for default value)</dd>
<dd><code>confirmButton</code> - text of the confirm button (can be null for default value)</dd>
<dd><code>cancelButton</code> - text of the cancel button (can be null for default value)</dd>
<dd><code>model</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="modalConfirm(java.lang.String,java.lang.String,java.lang.String,org.springframework.ui.Model,java.lang.Boolean,java.lang.Boolean)">
<h3>modalConfirm</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">modalConfirm</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;message,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;confirmButton,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cancelButton,
 org.springframework.ui.Model&nbsp;model,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;reloadOnConfirm,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;openModal)</span></div>
<div class="block">Show the confirm modal and optionally reloads when the confirm button is pressed</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>message</code> - text to be displayed (can be null for default value)</dd>
<dd><code>confirmButton</code> - text of the confirm button (can be null for default value)</dd>
<dd><code>cancelButton</code> - text of the cancel button (can be null for default value)</dd>
<dd><code>model</code> - </dd>
<dd><code>reloadOnConfirm</code> - (optional) when true, the browser will reload on confirm, adding the confirmation parameter to the url</dd>
<dd><code>openModal</code> - (optional) when true, the modal will be opened. To be used when the call is not ajax</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="callScriptOnModal(java.lang.String,org.springframework.ui.Model)">
<h3>callScriptOnModal</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">callScriptOnModal</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;scriptId,
 org.springframework.ui.Model&nbsp;model)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">Use YadaNotify instead</div>
</div>
<div class="block">Add a script id to call when opening the notification modal</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>scriptId</code> - </dd>
<dd><code>model</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getModalConfirmViewName()">
<h3>getModalConfirmViewName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getModalConfirmViewName</span>()</div>
<div class="block">Return the view name for the confirmation modal. Used in html to get the correct modal.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>something like "/yada/modalConfirmB5" depending on the current bootstrap version</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="ensureThymeleafUrl(java.lang.String)">
<h3>ensureThymeleafUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">ensureThymeleafUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url)</span></div>
<div class="block">Encloses the string in a thymeleaf url operator when missing</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>url</code> - some url</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
