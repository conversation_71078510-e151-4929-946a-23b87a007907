# Meglio avere la Subfamily nel modulo piutosto che i prodotti

ALTER TABLE NewsModule_Prodotto  DROP FOREIGN KEY FKdnf4wdokdax0tljfsd2fw4ket;
ALTER TABLE NewsModule_Prodotto  DROP FOREIGN KEY FKbvehbhmpoeyar5myy26lu3l9t;
DROP TABLE NewsModule_Prodotto;

create table NewsModule_Subfamily (NewsModule_id bigint not null, subfamilies_id bigint not null) engine=InnoDB;
alter table NewsModule_Subfamily add constraint FK5kecqa4hmksis228liurqd7hd foreign key (subfamilies_id) references Subfamily (id);
alter table NewsModule_Subfamily add constraint FK94qq97g51acu6cyw91cp6n2mb foreign key (NewsModule_id) references NewsModule (id);
