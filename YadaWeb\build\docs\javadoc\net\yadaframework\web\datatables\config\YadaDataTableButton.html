<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaDataTableButton (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web.datatables.config, class: YadaDataTableButton">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li><a href="#field-summary">Field</a></li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li><a href="#field-detail">Field</a></li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field-summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li><a href="#field-detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web.datatables.config</a></div>
<h1 title="Class YadaDataTableButton" class="title">Class YadaDataTableButton</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">net.yadaframework.core.YadaFluentBase</a>&lt;<a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>&gt;
<div class="inheritance">net.yadaframework.web.datatables.config.YadaDataTableButton</div>
</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="../proxy/YadaDataTableButtonProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableButtonProxy</a></code></dd>
</dl>
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaDataTableButton</span>
<span class="extends-implements">extends <a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;<a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>&gt;</span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#ajax" class="member-name-link">ajax</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#elementLoader" class="member-name-link">elementLoader</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#global" class="member-name-link">global</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></code></div>
<div class="col-second odd-row-color"><code><a href="#hidePageLoader" class="member-name-link">hidePageLoader</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#icon" class="member-name-link">icon</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#idName" class="member-name-link">idName</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected boolean</code></div>
<div class="col-second even-row-color"><code><a href="#multiRow" class="member-name-link">multiRow</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#roles" class="member-name-link">roles</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#showCommandIcon" class="member-name-link">showCommandIcon</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#text" class="member-name-link">text</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#toolbarCssClass" class="member-name-link">toolbarCssClass</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#type" class="member-name-link">type</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#url" class="member-name-link">url</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#urlProvider" class="member-name-link">urlProvider</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#windowFeatures" class="member-name-link">windowFeatures</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="#windowTarget" class="member-name-link">windowTarget</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected <a href="YadaDataTableConfirmDialog.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableConfirmDialog</a></code></div>
<div class="col-second even-row-color"><code><a href="#yadaDataTableConfirmDialog" class="member-name-link">yadaDataTableConfirmDialog</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="inherited-list">
<h3 id="fields-inherited-from-class-net.yadaframework.core.YadaFluentBase">Fields inherited from class&nbsp;net.yadaframework.core.<a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a></h3>
<code><a href="../../../core/YadaFluentBase.html#parent">parent</a></code></div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,net.yadaframework.web.datatables.config.YadaDataTableHTML)" class="member-name-link">YadaDataTableButton</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text,
 <a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>&nbsp;parent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#back()" class="member-name-link">back</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Method to return to parent for fluent chaining.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableConfirmDialog.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableConfirmDialog</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtConfirmDialogObj()" class="member-name-link">dtConfirmDialogObj</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enable javascript-side confirmation dialog for button action.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtElementLoader(java.lang.String)" class="member-name-link">dtElementLoader</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Show the loader on the selected element</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtGlobal()" class="member-name-link">dtGlobal</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set for a button that is always enabled regardless of row selection, for example an Add button.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtHidePageLoader()" class="member-name-link">dtHidePageLoader</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Do not show the page loader when the button is clicked</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtIcon(java.lang.String)" class="member-name-link">dtIcon</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;iconHTML)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">HTML content for the button's icon.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtIdName(java.lang.String)" class="member-name-link">dtIdName</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;idName)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Name of the ID request parameter, default is "id".</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtMultiRow()" class="member-name-link">dtMultiRow</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Enable the toolbar button when one or many rows are selected.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtNoAjax()" class="member-name-link">dtNoAjax</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Indicate that the button should use a normal request</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtRole(java.lang.String)" class="member-name-link">dtRole</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;roleKey)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Role that can access the button.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtShowCommandIcon(java.lang.String)" class="member-name-link">dtShowCommandIcon</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;function)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Javascript function that determines whether to show the button icon for each row.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtToolbarCssClass(java.lang.String)" class="member-name-link">dtToolbarCssClass</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssClass)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">CSS class to be applied to the button in the toolbar, for example 'btn-primary'.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtUrl(java.lang.String)" class="member-name-link">dtUrl</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">URL to be called when the button is clicked, it can be a thymeleaf expression and will be inserted in a @{} when missing</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtUrlProvider(java.lang.String)" class="member-name-link">dtUrlProvider</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;function)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">javascript function to be called when the button is clicked in order to compute the target URL for the button action.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtWindowFeatures(java.lang.String)" class="member-name-link">dtWindowFeatures</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;windowFeatures)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Features of the window when opened, such as its size, scrollbars, and whether it is resizable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#dtWindowTarget(java.lang.String)" class="member-name-link">dtWindowTarget</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;windowTarget)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Name of the window for opening the URL in a new window.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="type">
<h3>type</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">type</span></div>
</section>
</li>
<li>
<section class="detail" id="url">
<h3>url</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">url</span></div>
</section>
</li>
<li>
<section class="detail" id="urlProvider">
<h3>urlProvider</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">urlProvider</span></div>
</section>
</li>
<li>
<section class="detail" id="text">
<h3>text</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">text</span></div>
</section>
</li>
<li>
<section class="detail" id="icon">
<h3>icon</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">icon</span></div>
</section>
</li>
<li>
<section class="detail" id="global">
<h3>global</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">global</span></div>
</section>
</li>
<li>
<section class="detail" id="multiRow">
<h3>multiRow</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">multiRow</span></div>
</section>
</li>
<li>
<section class="detail" id="toolbarCssClass">
<h3>toolbarCssClass</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toolbarCssClass</span></div>
</section>
</li>
<li>
<section class="detail" id="idName">
<h3>idName</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">idName</span></div>
</section>
</li>
<li>
<section class="detail" id="ajax">
<h3>ajax</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">ajax</span></div>
</section>
</li>
<li>
<section class="detail" id="hidePageLoader">
<h3>hidePageLoader</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a></span>&nbsp;<span class="element-name">hidePageLoader</span></div>
</section>
</li>
<li>
<section class="detail" id="elementLoader">
<h3>elementLoader</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">elementLoader</span></div>
</section>
</li>
<li>
<section class="detail" id="yadaDataTableConfirmDialog">
<h3>yadaDataTableConfirmDialog</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="YadaDataTableConfirmDialog.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableConfirmDialog</a></span>&nbsp;<span class="element-name">yadaDataTableConfirmDialog</span></div>
</section>
</li>
<li>
<section class="detail" id="windowTarget">
<h3>windowTarget</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">windowTarget</span></div>
</section>
</li>
<li>
<section class="detail" id="windowFeatures">
<h3>windowFeatures</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">windowFeatures</span></div>
</section>
</li>
<li>
<section class="detail" id="showCommandIcon">
<h3>showCommandIcon</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">showCommandIcon</span></div>
</section>
</li>
<li>
<section class="detail" id="roles">
<h3>roles</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</span>&nbsp;<span class="element-name">roles</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,net.yadaframework.web.datatables.config.YadaDataTableHTML)">
<h3>YadaDataTableButton</h3>
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="element-name">YadaDataTableButton</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;text,
 <a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>&nbsp;parent)</span></div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="dtConfirmDialogObj()">
<h3>dtConfirmDialogObj</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableConfirmDialog.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableConfirmDialog</a></span>&nbsp;<span class="element-name">dtConfirmDialogObj</span>()</div>
<div class="block">Enable javascript-side confirmation dialog for button action. 
 Note: a backend call will always be able to trigger a confirmation dialog regardless of this setting.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtMultiRow()">
<h3>dtMultiRow</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtMultiRow</span>()</div>
<div class="block">Enable the toolbar button when one or many rows are selected.
 The default is for the toolbar button to be enabled only when one row is selected.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtToolbarCssClass(java.lang.String)">
<h3>dtToolbarCssClass</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtToolbarCssClass</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssClass)</span></div>
<div class="block">CSS class to be applied to the button in the toolbar, for example 'btn-primary'.
 The 'btn' class is added automatically.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cssClass</code> - </dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtIcon(java.lang.String)">
<h3>dtIcon</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtIcon</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;iconHTML)</span></div>
<div class="block">HTML content for the button's icon. The icon is used in the command column when enabled, and on the left of the toolbar button text.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>iconHTML</code> - </dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtUrlProvider(java.lang.String)">
<h3>dtUrlProvider</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtUrlProvider</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;function)</span></div>
<div class="block">javascript function to be called when the button is clicked in order to compute the target URL for the button action.
 
 When the row icon is clicked, it will receive the row data as a parameter. 
 When the toolbar button is clicked, it will receive the datatable api object and the array of selected ids as parameters.
 
 The function should return the target URL or null to prevent any further action.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>function</code> - the name of the js function that must be visible in page</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - when both url and urlProvider are set</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/api/row().data()">DataTables row().data() API</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtUrl(java.lang.String)">
<h3>dtUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtUrl</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url)</span></div>
<div class="block">URL to be called when the button is clicked, it can be a thymeleaf expression and will be inserted in a @{} when missing</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>url</code> - </dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - when both url and urlProvider are set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtGlobal()">
<h3>dtGlobal</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtGlobal</span>()</div>
<div class="block">Set for a button that is always enabled regardless of row selection, for example an Add button.
 Global buttons are not shown in the command column.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - when both showCommandIcon and global are set</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtIdName(java.lang.String)">
<h3>dtIdName</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtIdName</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;idName)</span></div>
<div class="block">Name of the ID request parameter, default is "id". Useless for global buttons.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>idName</code> - </dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtNoAjax()">
<h3>dtNoAjax</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtNoAjax</span>()</div>
<div class="block">Indicate that the button should use a normal request</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtHidePageLoader()">
<h3>dtHidePageLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtHidePageLoader</span>()</div>
<div class="block">Do not show the page loader when the button is clicked</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtElementLoader(java.lang.String)">
<h3>dtElementLoader</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtElementLoader</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;cssSelector)</span></div>
<div class="block">Show the loader on the selected element</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>cssSelector</code> - a CSS selector for the element that will be hidden by the loader</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtWindowTarget(java.lang.String)">
<h3>dtWindowTarget</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtWindowTarget</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;windowTarget)</span></div>
<div class="block">Name of the window for opening the URL in a new window.
 Special values are "_blank", "_self", "_parent", "_top"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>windowTarget</code> - </dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://developer.mozilla.org/en-US/docs/Web/API/Window/open">Window: open() method</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtWindowFeatures(java.lang.String)">
<h3>dtWindowFeatures</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtWindowFeatures</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;windowFeatures)</span></div>
<div class="block">Features of the window when opened, such as its size, scrollbars, and whether it is resizable.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>windowFeatures</code> - </dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://developer.mozilla.org/en-US/docs/Web/API/Window/open">Window: open() method</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtShowCommandIcon(java.lang.String)">
<h3>dtShowCommandIcon</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtShowCommandIcon</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;function)</span></div>
<div class="block">Javascript function that determines whether to show the button icon for each row.
 It receives the "data", "row" and "meta" parameters as for the render functions, and also dataTableJson and currentUserRoles. 
 It must return true/false to show/hide the icon or "disabled" to show it disabled.
 These buttons are not shown in the toolbar.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>function</code> - some javascript function that returns true or false or "disabled".</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
<dt>Throws:</dt>
<dd><code><a href="../../../exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></code> - when both showCommandIcon and global are set</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="https://datatables.net/reference/api/row().data()">DataTables row().data() API</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="dtRole(java.lang.String)">
<h3>dtRole</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></span>&nbsp;<span class="element-name">dtRole</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;roleKey)</span></div>
<div class="block">Role that can access the button. Will be removed if the user does not have this role. 
 Can be called multiple times.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>roleKey</code> - the key of the role as configured in the application xml</dd>
<dt>Returns:</dt>
<dd>this instance for method chaining</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="back()">
<h3>back</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></span>&nbsp;<span class="element-name">back</span>()</div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="../../../core/YadaFluentBase.html#back()">YadaFluentBase</a></code></span></div>
<div class="block">Method to return to parent for fluent chaining.</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="../../../core/YadaFluentBase.html#back()">back</a></code>&nbsp;in class&nbsp;<code><a href="../../../core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase</a>&lt;<a href="YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a>&gt;</code></dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
