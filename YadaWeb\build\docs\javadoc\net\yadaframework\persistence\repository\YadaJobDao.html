<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>Ya<PERSON><PERSON><PERSON><PERSON><PERSON> (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.persistence.repository, class: YadaJobDao">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.persistence.repository</a></div>
<h1 title="Class YadaJobDao" class="title">Class YadaJobDao</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.persistence.repository.YadaJobDao</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Repository
@Transactional(readOnly=true)
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaJobDao</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaJobDao</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>long</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#countByJobGroupAndStates(java.lang.String,java.util.Collection)" class="member-name-link">countByJobGroupAndStates</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;&gt;&nbsp;stateObjects)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the number of jobs for the given group, that are in one of the given states</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#delete(java.lang.Long)" class="member-name-link">delete</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Deletes a job</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findById(java.lang.Long)" class="member-name-link">findById</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;entityId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findByJobGroupAndState(java.lang.String,net.yadaframework.persistence.entity.YadaPersistentEnum,net.yadaframework.web.YadaPageRequest)" class="member-name-link">findByJobGroupAndState</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup,
 <a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;&nbsp;stateObject,
 <a href="../../web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a>&nbsp;pageable)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns all jobs for the given group that are in the given state</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findByJobGroupAndStates(java.lang.String,java.util.Collection)" class="member-name-link">findByJobGroupAndStates</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;&gt;&nbsp;stateObjects)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns all jobs for the given group, that are in one of the given states</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#findRunning(java.lang.String)" class="member-name-link">findRunning</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the running job for the given group if any</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJobStartTime(java.lang.Long)" class="member-name-link">getJobStartTime</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the start time of a job</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a><wbr>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getJobState(java.lang.Long)" class="member-name-link">getJobState</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Get the current job state from the database</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getRecoverableJobs()" class="member-name-link">getRecoverableJobs</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Return the list of jobs that can be recovered after a crash.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isJobGroupPaused(java.lang.String)" class="member-name-link">isJobGroupPaused</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the job group has been paused.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#save(net.yadaframework.persistence.entity.YadaJob)" class="member-name-link">save</a><wbr>(<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;entity)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setJobGroupPaused(java.lang.String,boolean)" class="member-name-link">setJobGroupPaused</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup,
 boolean&nbsp;paused)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the pause flag on all jobs of a jobGroup</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setStartTime(long,java.util.Date)" class="member-name-link">setStartTime</a><wbr>(long&nbsp;jobId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;startTime)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setState(java.lang.Long,net.yadaframework.persistence.entity.YadaJobState)" class="member-name-link">setState</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId,
 <a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&nbsp;yadaJobState)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the state of a job in the database.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setUnrecoverableJobState()" class="member-name-link">setUnrecoverableJobState</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Disable all RUNNING jobs that are not recoverable and not group-paused.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stateChangeFromTo(java.lang.Long,net.yadaframework.persistence.entity.YadaPersistentEnum,net.yadaframework.persistence.entity.YadaPersistentEnum)" class="member-name-link">stateChangeFromTo</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId,
 <a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;&nbsp;fromStateEnum,
 <a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;&nbsp;toStateEnum)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Invoked by the scheduler classes when starting a job.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#stateChangeFromTo(net.yadaframework.persistence.entity.YadaJob,net.yadaframework.persistence.entity.YadaJobState,net.yadaframework.persistence.entity.YadaJobState)" class="member-name-link">stateChangeFromTo</a><wbr>(<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob,
 <a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&nbsp;fromStateObject,
 <a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&nbsp;toStateObject)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Invoked by the scheduler classes when starting a job.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaJobDao</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaJobDao</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="setState(java.lang.Long,net.yadaframework.persistence.entity.YadaJobState)">
<h3>setState</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setState</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId,
 <a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&nbsp;yadaJobState)</span></div>
<div class="block">Set the state of a job in the database. If the job does not exist, nothing happens.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJobId</code> - </dd>
<dd><code>yadaJobState</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="delete(java.lang.Long)">
<h3>delete</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">delete</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</span></div>
<div class="block">Deletes a job</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>long1</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getRecoverableJobs()">
<h3>getRecoverableJobs</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;</span>&nbsp;<span class="element-name">getRecoverableJobs</span>()</div>
<div class="block">Return the list of jobs that can be recovered after a crash.</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="setUnrecoverableJobState()">
<h3>setUnrecoverableJobState</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setUnrecoverableJobState</span>()</div>
<div class="block">Disable all RUNNING jobs that are not recoverable and not group-paused. Called at server startup.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>stateObject</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJobState(java.lang.Long)">
<h3>getJobState</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;</span>&nbsp;<span class="element-name">getJobState</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</span></div>
<div class="block">Get the current job state from the database</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJobId</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="stateChangeFromTo(java.lang.Long,net.yadaframework.persistence.entity.YadaPersistentEnum,net.yadaframework.persistence.entity.YadaPersistentEnum)">
<h3>stateChangeFromTo</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stateChangeFromTo</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId,
 <a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;&nbsp;fromStateEnum,
 <a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;&nbsp;toStateEnum)</span></div>
<div class="block">Invoked by the scheduler classes when starting a job. Only an active job can become running</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJobId</code> - the id of the job</dd>
<dd><code>fromId</code> - the id of the state enum that the job must have in order to be changed</dd>
<dd><code>toId</code> - the id of the state enum to assign</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="stateChangeFromTo(net.yadaframework.persistence.entity.YadaJob,net.yadaframework.persistence.entity.YadaJobState,net.yadaframework.persistence.entity.YadaJobState)">
<h3>stateChangeFromTo</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">stateChangeFromTo</span><wbr><span class="parameters">(<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;yadaJob,
 <a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&nbsp;fromStateObject,
 <a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&nbsp;toStateObject)</span></div>
<div class="block">Invoked by the scheduler classes when starting a job. Only an active job can become running</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJob</code> - the job</dd>
<dd><code>fromState</code> - the state enum that the job must have in order to be changed</dd>
<dd><code>toState</code> - the state enum to assign</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getJobStartTime(java.lang.Long)">
<h3>getJobStartTime</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">getJobStartTime</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;yadaJobId)</span></div>
<div class="block">Returns the start time of a job</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaJobId</code> - </dd>
<dt>Returns:</dt>
<dd>might be null when not running or not existing</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isJobGroupPaused(java.lang.String)">
<h3>isJobGroupPaused</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a></span>&nbsp;<span class="element-name">isJobGroupPaused</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</span></div>
<div class="block">Check if the job group has been paused. It is considered paused if at least one job in the group has the jobGroupPaused set</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - </dd>
<dt>Returns:</dt>
<dd>1 if the group is paused, null otherwise.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setJobGroupPaused(java.lang.String,boolean)">
<h3>setJobGroupPaused</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setJobGroupPaused</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup,
 boolean&nbsp;paused)</span></div>
<div class="block">Set the pause flag on all jobs of a jobGroup</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - </dd>
<dd><code>paused</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="findByJobGroupAndState(java.lang.String,net.yadaframework.persistence.entity.YadaPersistentEnum,net.yadaframework.web.YadaPageRequest)">
<h3>findByJobGroupAndState</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;</span>&nbsp;<span class="element-name">findByJobGroupAndState</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup,
 <a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;&nbsp;stateObject,
 <a href="../../web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a>&nbsp;pageable)</span></div>
<div class="block">Returns all jobs for the given group that are in the given state</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - the job group</dd>
<dd><code>stateObject</code> - the job state</dd>
<dd><code>pageable</code> - can be null for all results</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="findRunning(java.lang.String)">
<h3>findRunning</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></span>&nbsp;<span class="element-name">findRunning</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup)</span></div>
<div class="block">Returns the running job for the given group if any</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - </dd>
<dt>Returns:</dt>
<dd>the running job or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="countByJobGroupAndStates(java.lang.String,java.util.Collection)">
<h3>countByJobGroupAndStates</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">long</span>&nbsp;<span class="element-name">countByJobGroupAndStates</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;&gt;&nbsp;stateObjects)</span></div>
<div class="block">Returns the number of jobs for the given group, that are in one of the given states</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - </dd>
<dd><code>stateObjects</code> - a collection of job states</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="findByJobGroupAndStates(java.lang.String,java.util.Collection)">
<h3>findByJobGroupAndStates</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;</span>&nbsp;<span class="element-name">findByJobGroupAndStates</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;jobGroup,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Collection.html" title="class or interface in java.util" class="external-link">Collection</a>&lt;<a href="../entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum</a>&lt;<a href="../entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a>&gt;&gt;&nbsp;stateObjects)</span></div>
<div class="block">Returns all jobs for the given group, that are in one of the given states</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobGroup</code> - </dd>
<dd><code>stateObjects</code> - a collection of job states</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="setStartTime(long,java.util.Date)">
<h3>setStartTime</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setStartTime</span><wbr><span class="parameters">(long&nbsp;jobId,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a>&nbsp;startTime)</span></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>jobId</code> - </dd>
<dd><code>startTime</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="save(net.yadaframework.persistence.entity.YadaJob)">
<h3>save</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></span>&nbsp;<span class="element-name">save</span><wbr><span class="parameters">(<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&nbsp;entity)</span></div>
</section>
</li>
<li>
<section class="detail" id="findById(java.lang.Long)">
<h3>findById</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="../entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a>&gt;</span>&nbsp;<span class="element-name">findById</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;entityId)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
