<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaUserMessageDao (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.security.persistence.repository, class: YadaUserMessageDao">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.security.persistence.repository</a></div>
<h1 title="Class YadaUserMessageDao" class="title">Class YadaUserMessageDao</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.security.persistence.repository.YadaUserMessageDao</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Repository
@Transactional(readOnly=true)
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaUserMessageDao</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaUserMessageDao</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createOrIncrement(net.yadaframework.security.persistence.entity.YadaUserMessage)" class="member-name-link">createOrIncrement</a><wbr>(<a href="../entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&lt;?&gt;&nbsp;m)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Save a message.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteBelongingTo(net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">deleteBelongingTo</a><wbr>(<a href="../entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;userProfile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Delete all messages that do not involve users other than the one specified (no other users as sender o recipient)</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#find(java.lang.Long)" class="member-name-link">find</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;id)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>net.yadaframework.web.YadaPageRows<wbr>&lt;<a href="../entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#find(java.lang.Long,net.yadaframework.web.YadaPageRequest,net.yadaframework.persistence.entity.YadaPersistentEnum...)" class="member-name-link">find</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;recipientUserProfileId,
 net.yadaframework.web.YadaPageRequest&nbsp;yadaPageRequest,
 net.yadaframework.persistence.entity.YadaPersistentEnum&lt;?&gt;...&nbsp;types)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Find a page of notifications</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getLastDate(net.yadaframework.security.persistence.entity.YadaUserMessage)" class="member-name-link">getLastDate</a><wbr>(<a href="../entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&nbsp;message)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the most recent date of the message stack - which is the initial date if the message is not stackable</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasUnreadMessage(net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">hasUnreadMessage</a><wbr>(<a href="../entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;userProfile)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if there exists at least one unread message for the user</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#markAsRead(java.util.List)" class="member-name-link">markAsRead</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&gt;&nbsp;messages)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaUserMessageDao</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaUserMessageDao</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="getLastDate(net.yadaframework.security.persistence.entity.YadaUserMessage)">
<h3>getLastDate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Date.html" title="class or interface in java.util" class="external-link">Date</a></span>&nbsp;<span class="element-name">getLastDate</span><wbr><span class="parameters">(<a href="../entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&nbsp;message)</span></div>
<div class="block">Returns the most recent date of the message stack - which is the initial date if the message is not stackable</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="markAsRead(java.util.List)">
<h3>markAsRead</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">markAsRead</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&gt;&nbsp;messages)</span></div>
</section>
</li>
<li>
<section class="detail" id="find(java.lang.Long)">
<h3>find</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a></span>&nbsp;<span class="element-name">find</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;id)</span></div>
</section>
</li>
<li>
<section class="detail" id="find(java.lang.Long,net.yadaframework.web.YadaPageRequest,net.yadaframework.persistence.entity.YadaPersistentEnum...)">
<h3>find</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">net.yadaframework.web.YadaPageRows&lt;<a href="../entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&gt;</span>&nbsp;<span class="element-name">find</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&nbsp;recipientUserProfileId,
 net.yadaframework.web.YadaPageRequest&nbsp;yadaPageRequest,
 net.yadaframework.persistence.entity.YadaPersistentEnum&lt;?&gt;...&nbsp;types)</span></div>
<div class="block">Find a page of notifications</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaPageRequest</code> - </dd>
<dd><code>types</code> - required types, can be omitted</dd>
<dd><code>currentUserProfileId</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasUnreadMessage(net.yadaframework.security.persistence.entity.YadaUserProfile)">
<h3>hasUnreadMessage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasUnreadMessage</span><wbr><span class="parameters">(<a href="../entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;userProfile)</span></div>
<div class="block">Returns true if there exists at least one unread message for the user</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userProfile</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="deleteBelongingTo(net.yadaframework.security.persistence.entity.YadaUserProfile)">
<h3>deleteBelongingTo</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">deleteBelongingTo</span><wbr><span class="parameters">(<a href="../entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;userProfile)</span></div>
<div class="block">Delete all messages that do not involve users other than the one specified (no other users as sender o recipient)</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userProfile</code> - the receiver/sender of the message</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="createOrIncrement(net.yadaframework.security.persistence.entity.YadaUserMessage)">
<h3>createOrIncrement</h3>
<div class="member-signature"><span class="annotations">@Transactional(readOnly=false)
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">createOrIncrement</span><wbr><span class="parameters">(<a href="../entity/YadaUserMessage.html" title="class in net.yadaframework.security.persistence.entity">YadaUserMessage</a>&lt;?&gt;&nbsp;m)</span></div>
<div class="block">Save a message. If the message is stackable, only increment the counter of an existing message with identical
 content and same recipient and same sender and same data, if not older than one day.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>m</code> - the new message that will be persisted if not already in the database. On return, in any case this will hold
          the result of the database operation (either a new instance or an existing element with incremented counter)</dd>
<dt>Returns:</dt>
<dd>true if the message has been created, false if the counter incremented</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
