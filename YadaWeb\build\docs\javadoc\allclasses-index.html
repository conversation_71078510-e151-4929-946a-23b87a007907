<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>All Classes and Interfaces (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li>Package</li>
<li>Class</li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="deprecated-list.html">Deprecated</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="help-doc.html#all-classes">Help</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="All Classes and Interfaces" class="title">All Classes and Interfaces</h1>
</div>
<div id="all-classes-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="all-classes-table-tab0" role="tab" aria-selected="true" aria-controls="all-classes-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="all-classes-table-tab1" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab1', 2)" class="table-tab">Interfaces</button><button id="all-classes-table-tab2" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab2', 2)" class="table-tab">Classes</button><button id="all-classes-table-tab3" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab3', 2)" class="table-tab">Enum Classes</button><button id="all-classes-table-tab5" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab5', 2)" class="table-tab">Exception Classes</button><button id="all-classes-table-tab6" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab6', 2)" class="table-tab">Annotation Interfaces</button></div>
<div id="all-classes-table.tabpanel" role="tabpanel">
<div class="summary-table two-column-summary" aria-labelledby="all-classes-table-tab0">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/tools/AntIncrementBuild.html" title="class in net.yadaframework.tools">AntIncrementBuild</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Questa classe incrementa di uno il valore che trova per la property indicata.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="net/yadaframework/core/CloneableDeep.html" title="interface in net.yadaframework.core">CloneableDeep</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Marker interface that tells that if this object is inside a collection,
 it will be recursively cloned before being added to the cloned collection of the parent.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="net/yadaframework/core/CloneableFiltered.html" title="interface in net.yadaframework.core">CloneableFiltered</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Marker interface to allow an object to be cloned with YadaUtil.copyEntity().</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/InternalException.html" title="class in net.yadaframework.exceptions">InternalException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">Deprecated.
<div class="deprecation-comment">use YadaInternalException instead</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/InvalidValueException.html" title="class in net.yadaframework.exceptions">InvalidValueException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">Deprecated.
<div class="deprecation-comment">use YadaInvalidException instead</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/SystemException.html" title="class in net.yadaframework.exceptions">SystemException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">Deprecated.
<div class="deprecation-comment">use YadaSystemException instead</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="sogei/utility/UCheckDigit.html" title="class in sogei.utility">UCheckDigit</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Downloaded from http://www.agenziaentrate.gov.it/wps/content/Nsilib/Nsi/Home/CosaDeviFare/Richiedere/Codice+fiscale+e+tessera+sanitaria/Richiesta+TS_CF/SchedaI/Programma+correttezza+formale+CF/</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="sogei/utility/UCheckNum.html" title="class in sogei.utility">UCheckNum</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Classe che implementa e svolge l'algoritmo di controllo di validità della partita iva
 e del codice fiscale delle persone non fisiche confrontando anche il valore del check-digit
 la classe restituisce un codice di ritorno che può assumere dei valori predeterminati

 Downloaded from http://www.agenziaentrate.gov.it/wps/content/Nsilib/Nsi/Home/CosaDeviFare/Richiedere/Codice+fiscale+e+tessera+sanitaria/Richiesta+TS_CF/SchedaI/Programma+correttezza+formale+CF/</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaAjaxAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaAjaxAttrProcessor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class that handles the convenience attribute yada:ajax="url".</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaAjaxInterceptor.html" title="class in net.yadaframework.core">YadaAjaxInterceptor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/YadaAlreadyRunningException.html" title="class in net.yadaframework.exceptions">YadaAlreadyRunningException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">Thrown when a YadaLongRunningExclusive class is being called while already running</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaAppConfig.html" title="class in net.yadaframework.core">YadaAppConfig</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/entity/YadaAttachedFile.html" title="class in net.yadaframework.persistence.entity">YadaAttachedFile</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A "pointer" to a file that has been copied into the "contents" folder.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="net/yadaframework/persistence/entity/YadaAttachedFile.YadaAttachedFileType.html" title="enum class in net.yadaframework.persistence.entity">YadaAttachedFile.YadaAttachedFileType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaAttachedFileCloneSet.html" title="class in net.yadaframework.components">YadaAttachedFileCloneSet</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used when cloning some object that contains instances of YadaAttachedFile at any level.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/repository/YadaAttachedFileDao.html" title="class in net.yadaframework.persistence.repository">YadaAttachedFileDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaBrOnFirstSpaceAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaBrOnFirstSpaceAttrProcessor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adds the yada:brspace="" attribute that insert a &lt;br&gt; tag after the first space.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/entity/YadaBrowserId.html" title="class in net.yadaframework.persistence.entity">YadaBrowserId</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class uniquely identifies the user browser.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/repository/YadaBrowserIdDao.html" title="class in net.yadaframework.persistence.repository">YadaBrowserIdDao</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/entity/YadaClause.html" title="class in net.yadaframework.persistence.entity">YadaClause</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/repository/YadaClauseDao.html" title="class in net.yadaframework.persistence.repository">YadaClauseDao</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="org/springframework/web/multipart/commons/YadaCommonsMultipartResolver.html" title="class in org.springframework.web.multipart.commons">YadaCommonsMultipartResolver</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaConfiguration.html" title="class in net.yadaframework.core">YadaConfiguration</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Classe che estende CombinedConfiguration aggiungendo metodi di gestione della configurazione specifici.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/YadaConfigurationException.html" title="class in net.yadaframework.exceptions">YadaConfigurationException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Unchecked exception thrown when some needed configuration element is missing</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="net/yadaframework/core/YadaConstants.html" title="interface in net.yadaframework.core">YadaConstants</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaController.html" title="class in net.yadaframework.web">YadaController</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab6"><a href="net/yadaframework/components/YadaCopyNot.html" title="annotation interface in net.yadaframework.components">YadaCopyNot</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab6">
<div class="block">Fields marked by this annotation are not copied at all when cloning with YadaUtil, and the value is not initialized (e.g.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab6"><a href="net/yadaframework/components/YadaCopyShallow.html" title="annotation interface in net.yadaframework.components">YadaCopyShallow</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab6">
<div class="block">Fields marked by this annotation are not recursively copied when cloning with YadaUtil, but copied by value,
 unless their value has been cloned in an ancestor object, in which case the cloned version will be used as a value.<br>
 <br>
 For example, if ProjectA contains ModuleA that has a project field referring to ProjectA with this annotation, then when
 cloning ModuleA the new ModuleB will keep a reference to ProjectA (shallow copy); when
 cloning ProjectA (the parent of ModuleA) the new ProjectB will contain a ModuleB with a reference to ProjectB (not
 to ProjectA because a clone of it has been made already).<br>
 <br>
 If this annotation is not used, it makes no difference for the latter case because ModuleB would get a reference to ProjectB
 that has already been cloned (there's a check to prevent infinite loops).</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaCropImage.html" title="class in net.yadaframework.web">YadaCropImage</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Kept in HttpSession, it stores all info needed to crop images.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaCropQueue.html" title="class in net.yadaframework.web">YadaCropQueue</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Kept in HttpSession, it stores all info needed to crop images.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/YadaDao.html" title="class in net.yadaframework.persistence">YadaDao</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.
<div class="deprecation-comment">because you will eventually need a specific select, so better make a specific DAO which has type checking</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/YadaDataTable.html" title="class in net.yadaframework.web.datatables">YadaDataTable</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class representing a DataTable.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/config/YadaDataTableButton.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableButton</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/proxy/YadaDataTableButtonProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableButtonProxy</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class implements the methods needed for <b>internal use</b> 
 so that they don't pollute the fluent interface.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/config/YadaDataTableColumn.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableColumn</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Configuration for a column of a DataTables table.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/proxy/YadaDataTableColumnProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableColumnProxy</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class implements the methods needed for <b>internal use</b> 
 so that they don't pollute the fluent interface.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="net/yadaframework/web/datatables/YadaDataTableConfigurer.html" title="interface in net.yadaframework.web.datatables">YadaDataTableConfigurer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Functional interface used internally</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/config/YadaDataTableConfirmDialog.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableConfirmDialog</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/proxy/YadaDataTableConfirmDialogProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableConfirmDialogProxy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/YadaDataTableDao.html" title="class in net.yadaframework.persistence">YadaDataTableDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaDataTableFactory.html" title="class in net.yadaframework.components">YadaDataTableFactory</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Returns YadaDataTable instances, either creating new ones or reusing existing ones.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/YadaDataTableHelper.html" title="class in net.yadaframework.web.datatables">YadaDataTableHelper</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/config/YadaDataTableHTML.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableHTML</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/proxy/YadaDataTableHTMLProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableHTMLProxy</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class implements the methods needed for <b>internal use</b> 
 so that they don't pollute the fluent interface.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/config/YadaDataTableLanguage.html" title="class in net.yadaframework.web.datatables.config">YadaDataTableLanguage</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/proxy/YadaDataTableLanguageProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableLanguageProxy</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class implements the methods needed for <b>internal use</b> 
 so that they don't pollute the fluent interface.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/proxy/YadaDataTableProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDataTableProxy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class implements the methods needed for <b>internal use</b> 
 so that they don't pollute the fluent interface.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaDatatablesColumn.html" title="class in net.yadaframework.web">YadaDatatablesColumn</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a map because Spring wouldn't map it from the request otherwise</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaDatatablesColumnSearch.html" title="class in net.yadaframework.web">YadaDatatablesColumnSearch</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a map because Spring wouldn't map it from the request otherwise</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaDatatablesOrder.html" title="class in net.yadaframework.web">YadaDatatablesOrder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This is a map because Spring wouldn't map it from the request otherwise</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaDatatablesRequest.html" title="class in net.yadaframework.web">YadaDatatablesRequest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Mapped by Spring automatically from the request</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaDataTableTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaDataTableTagProcessor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Defines the &lt;yada:dataTable&gt; tag</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaDateFormatter.html" title="class in net.yadaframework.components">YadaDateFormatter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaDialect.html" title="class in net.yadaframework.web.dialect">YadaDialect</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaDialectUtil.html" title="class in net.yadaframework.web.dialect">YadaDialectUtil</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="net/yadaframework/web/datatables/YadaDtAjaxHandler.html" title="interface in net.yadaframework.web.datatables">YadaDtAjaxHandler</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Functional interface for DataTable ajax handler method references.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/options/YadaDTBreakpoint.html" title="class in net.yadaframework.web.datatables.options">YadaDTBreakpoint</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/options/YadaDTColumnDef.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumnDef</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents column definition configuration for DataTables.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/proxy/YadaDTColumnDefProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTColumnDefProxy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class implements the methods needed for <b>internal use</b> 
 so that they don't pollute the fluent interface.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/options/YadaDTColumns.html" title="class in net.yadaframework.web.datatables.options">YadaDTColumns</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Defines a column in DataTables.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/proxy/YadaDTColumnsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTColumnsProxy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class implements the methods needed for <b>internal use</b> 
 so that they don't pollute the fluent interface.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/options/YadaDTOptions.html" title="class in net.yadaframework.web.datatables.options">YadaDTOptions</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class representing options for configuring DataTables.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/proxy/YadaDTOptionsProxy.html" title="class in net.yadaframework.web.datatables.proxy">YadaDTOptionsProxy</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class implements the methods needed for <b>internal use</b> 
 so that they don't pollute the fluent interface.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/options/YadaDTOrder.html" title="class in net.yadaframework.web.datatables.options">YadaDTOrder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">DataTables ordering object.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/options/YadaDTResponsive.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsive</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class representing responsive options for DataTables.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/datatables/options/YadaDTResponsiveDetails.html" title="class in net.yadaframework.web.datatables.options">YadaDTResponsiveDetails</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class representing detailed options for responsive behavior in DataTables.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaDummyDatasource.html" title="class in net.yadaframework.core">YadaDummyDatasource</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaDummyEntityManagerFactory.html" title="class in net.yadaframework.core">YadaDummyEntityManagerFactory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaDummyJpaConfig.html" title="class in net.yadaframework.core">YadaDummyJpaConfig</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaEmailBuilder.html" title="class in net.yadaframework.components">YadaEmailBuilder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Api for building and sending emails using Thymeleaf templates.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaEmailContent.html" title="class in net.yadaframework.web">YadaEmailContent</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/YadaEmailException.html" title="class in net.yadaframework.exceptions">YadaEmailException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Runtime exception that wraps email checked exceptions.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaEmailParam.html" title="class in net.yadaframework.web">YadaEmailParam</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaEmailService.html" title="class in net.yadaframework.components">YadaEmailService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/social/YadaFacebookRequest.html" title="class in net.yadaframework.web.social">YadaFacebookRequest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Abstract superclass for Facebook operations.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/social/YadaFacebookRequestV9.html" title="class in net.yadaframework.web.social">YadaFacebookRequestV9</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class holds an operation that has to be done on Facebook using API version 9.0.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaFileManager.html" title="class in net.yadaframework.components">YadaFileManager</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The File Manager handles uploaded files.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/repository/YadaFileManagerDao.html" title="class in net.yadaframework.persistence.repository">YadaFileManagerDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/flot/YadaFlotAxis.html" title="class in net.yadaframework.web.flot">YadaFlotAxis</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class to setup X or Y axis options.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/flot/YadaFlotChart.html" title="class in net.yadaframework.web.flot">YadaFlotChart</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Flot chart, composed of many series</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/flot/YadaFlotGrid.html" title="class in net.yadaframework.web.flot">YadaFlotGrid</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The grid is the thing with the axes and a number of ticks.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/flot/YadaFlotPlotOptions.html" title="class in net.yadaframework.web.flot">YadaFlotPlotOptions</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/flot/YadaFlotSeriesObject.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesObject</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A Flot Series in Object format.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/flot/YadaFlotSeriesOptions.html" title="class in net.yadaframework.web.flot">YadaFlotSeriesOptions</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The options inside "series: {}" are copied to each of the series.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaFluentBase.html" title="class in net.yadaframework.core">YadaFluentBase&lt;T&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for fluent interface implementations that allows nesting</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/form/YadaFormFieldMap.html" title="class in net.yadaframework.web.form">YadaFormFieldMap</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generic form bean for String values.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaFormHelper.html" title="class in net.yadaframework.components">YadaFormHelper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility methods to be used in thymeleaf forms</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/form/YadaFormPasswordChange.html" title="class in net.yadaframework.web.form">YadaFormPasswordChange</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaGlobalExceptionHandler.html" title="class in net.yadaframework.web">YadaGlobalExceptionHandler</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Handles all exceptions exiting a @Controller that have not been annotated with @ResponseStatus
 http://ankursinghal86.blogspot.it/2014/07/exception-handling-in-spring-mvc.html</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaHrefAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaHrefAttrProcessor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class that handles the convenience attribute yada:href="url".</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/web/exceptions/YadaHttpNotFoundException.html" title="class in net.yadaframework.web.exceptions">YadaHttpNotFoundException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">Throw this exception to generate a 404 HTTP return code.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/raw/YadaHttpUtil.html" title="class in net.yadaframework.raw">YadaHttpUtil</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Miscellaneous HTTP functions</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaInputCounterTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaInputCounterTagProcessor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Defines the &lt;yada:inputCounter&gt; tag</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaInputTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaInputTagProcessor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Defines the &lt;yada:input&gt; tag</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="net/yadaframework/web/dialect/YadaInputTagSuggestion.html" title="interface in net.yadaframework.web.dialect">YadaInputTagSuggestion</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">An object implementing this interface can be returned by the</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/raw/YadaIntDimension.html" title="class in net.yadaframework.raw">YadaIntDimension</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Like java.awt.Dimension but with int values.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/YadaInternalException.html" title="class in net.yadaframework.exceptions">YadaInternalException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">Unchecked exception thrown when something is inconsistent</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/YadaInvalidUsageException.html" title="class in net.yadaframework.exceptions">YadaInvalidUsageException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Thrown when some prerequisite is missing when calling a method.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/YadaInvalidValueException.html" title="class in net.yadaframework.exceptions">YadaInvalidValueException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">A method parameter is invalid.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/entity/YadaJob.html" title="class in net.yadaframework.persistence.entity">YadaJob</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The base class for jobs handled by the YadaScheduler.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/repository/YadaJobDao.html" title="class in net.yadaframework.persistence.repository">YadaJobDao</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/YadaJobFailedException.html" title="class in net.yadaframework.exceptions">YadaJobFailedException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Convenience exception to throw when a YadaJob implementation fails execution, if the application doesn't implement a more specific exception.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaJobManager.html" title="class in net.yadaframework.components">YadaJobManager</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Job handling.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/repository/YadaJobSchedulerDao.html" title="class in net.yadaframework.persistence.repository">YadaJobSchedulerDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">This class is used internally by the YadaJobScheduler and should never be used directly.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="net/yadaframework/persistence/entity/YadaJobState.html" title="enum class in net.yadaframework.persistence.entity">YadaJobState</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">The localized state of a YadaJob:
 
 ACTIVE: the job is waiting to be run
 RUNNING: the job is running
 PAUSED: scheduling on this job has been paused (by the user) and the job should not run
 COMPLETED: the job has completed its purpose and should not run again
 DISABLED: the job has been disabled because of errors
 </div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaJpaConfig.html" title="class in net.yadaframework.core">YadaJpaConfig</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaJsonDateSimpleSerializer.html" title="class in net.yadaframework.web">YadaJsonDateSimpleSerializer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Date serializer that produces a date in the format "yyyy-MM-dd" using the thread's timezone and locale</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaJsonDateTimeShortSerializer.html" title="class in net.yadaframework.web">YadaJsonDateTimeShortSerializer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Date serializer that produces a date in the FastDateFormat.SHORT format using the thread's timezone and locale</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaJsonMapper.html" title="class in net.yadaframework.components">YadaJsonMapper</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaJsonRawStringSerializer.html" title="class in net.yadaframework.web">YadaJsonRawStringSerializer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Serialize a String to JSON as a raw value i.e.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaJsonView.html" title="class in net.yadaframework.web">YadaJsonView</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaKeyRateLimiter.html" title="class in net.yadaframework.components">YadaKeyRateLimiter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A rate limiter by key.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaLinkBuilder.html" title="class in net.yadaframework.core">YadaLinkBuilder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Called when @{/somePath} is found.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/repository/YadaLocaleDao.html" title="class in net.yadaframework.persistence.repository">YadaLocaleDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Locale-related operations on entities</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="net/yadaframework/core/YadaLocalEnum.html" title="interface in net.yadaframework.core">YadaLocalEnum&lt;E&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">This interface must be applied to a normal enum that needs to be localized in order to perform sort and search operations
 on the localized text.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaLocalePathChangeInterceptor.html" title="class in net.yadaframework.core">YadaLocalePathChangeInterceptor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Locale in the path
 See https://stackoverflow.com/a/23847484/587641</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaLocalePathLinkBuilder.html" title="class in net.yadaframework.core">YadaLocalePathLinkBuilder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaLocalePathVariableFilter.html" title="class in net.yadaframework.components">YadaLocalePathVariableFilter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Locale in the path
 See https://stackoverflow.com/a/23847484/587641
 This must be added to the WebApplicationInitializer.getServletFilters() method in each webapp (see yada docs)</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaLongRunningExclusive.html" title="class in net.yadaframework.components">YadaLongRunningExclusive&lt;T&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Class that performs a long running operation that should not be invoked again before it completes.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/raw/YadaLookupTable.html" title="class in net.yadaframework.raw">YadaLookupTable&lt;K,<wbr>V&gt;</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Lookup table with any number of keys of the same checked type or any unchecked types.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/raw/YadaLookupTableFive.html" title="class in net.yadaframework.raw">YadaLookupTableFive&lt;K1,<wbr>K2,<wbr>K3,<wbr>K4,<wbr>V&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implements a table with five columns: four keys and one value.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/raw/YadaLookupTableFour.html" title="class in net.yadaframework.raw">YadaLookupTableFour&lt;K1,<wbr>K2,<wbr>K3,<wbr>V&gt;</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implements a table with four columns: three keys and one value.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/raw/YadaLookupTableSix.html" title="class in net.yadaframework.raw">YadaLookupTableSix&lt;K1,<wbr>K2,<wbr>K3,<wbr>K4,<wbr>K5,<wbr>V&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implements a table with six columns: five keys and one value.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/raw/YadaLookupTableThree.html" title="class in net.yadaframework.raw">YadaLookupTableThree&lt;K1,<wbr>K2,<wbr>V&gt;</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implements a table with three columns: two keys and one value.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/entity/YadaManagedFile.html" title="class in net.yadaframework.persistence.entity">YadaManagedFile</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A "pointer" to a file that has been uploaded to the "uploads" folder.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaMariaDB.html" title="class in net.yadaframework.components">YadaMariaDB</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Allows reopening of an existing MariaDB data folder</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaMariaDBServer.html" title="class in net.yadaframework.components">YadaMariaDBServer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/YadaMoney.html" title="class in net.yadaframework.persistence">YadaMoney</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An amount of money with a 1/10000 precision, stored as a long both in java and in the database.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/YadaMoneyConverter.html" title="class in net.yadaframework.persistence">YadaMoneyConverter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">JPA converter between Object representation "YadaMoney" and DB column "Long"</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/raw/YadaNetworkUtil.html" title="class in net.yadaframework.raw">YadaNetworkUtil</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaNewlineTextAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaNewlineTextAttrProcessor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adds the yada:newlinetext="" attribute that converts ASCII newlines to HTML newlines, and the yada:unewlinetext that does not escape the original text</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaNotify.html" title="class in net.yadaframework.components">YadaNotify</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Build a notification dialog to be shown on the response page or after a redirect.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaNotifyData.html" title="class in net.yadaframework.components">YadaNotifyData</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A page for pageable content.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaPageRows.html" title="class in net.yadaframework.web">YadaPageRows&lt;T&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A page of rows fetched using YadaPageRequest</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaPageSort.html" title="class in net.yadaframework.web">YadaPageSort</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Database "order by" definition.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/entity/YadaPersistentEnum.html" title="class in net.yadaframework.persistence.entity">YadaPersistentEnum&lt;E&gt;</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Needed to store a localized enum in the database, on which to perform localized search and sort operations.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/repository/YadaPersistentEnumDao.html" title="class in net.yadaframework.persistence.repository">YadaPersistentEnumDao</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaPersistentEnumEditor.html" title="class in net.yadaframework.web">YadaPersistentEnumEditor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Used by thymeleaf to convert between string representation and YadaPersistentEnum.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/YadaPublicSuffix.html" title="class in net.yadaframework.web">YadaPublicSuffix</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Parser for https://publicsuffix.org list of domain names</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/entity/YadaRateLog.html" title="class in net.yadaframework.persistence.entity">YadaRateLog</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Keeps a timestamped log of events for rate-limiting purposes
 NOT USED YET!!! Aggiungere a persistence.xml poi
 TODO vedi quanto fatto per FeccMe_Proxy</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="net/yadaframework/raw/YadaRegexReplacer.html" title="interface in net.yadaframework.raw">YadaRegexReplacer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/raw/YadaRegexUtil.html" title="class in net.yadaframework.raw">YadaRegexUtil</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Miscellaneous Regular Expression functions</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="net/yadaframework/core/YadaRegistrationType.html" title="enum class in net.yadaframework.core">YadaRegistrationType</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">Una YadaRegistrationRequest può essere usata anche per altri scopi oltre alla registrazione.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/tools/YadaSchemaGenerator.html" title="class in net.yadaframework.tools">YadaSchemaGenerator</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Generates a db schema using the JPA standard.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaSecurityUtilStub.html" title="class in net.yadaframework.components">YadaSecurityUtilStub</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">SecurityUtils methods that are used in YadaWeb only when YadaWebSecurity is present.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/selenium/YadaSeleniumUtil.html" title="class in net.yadaframework.selenium">YadaSeleniumUtil</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaSetup.html" title="class in net.yadaframework.components">YadaSetup</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaSimpleAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaSimpleAttrProcessor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Converts from a "yada:xxx" thymeleaf attribute to a plain html attribute.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaSimpleRateLimiter.html" title="class in net.yadaframework.components">YadaSimpleRateLimiter</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A simple rate limiter that just counts and resets at intervals.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaSleepingRateLimiter.html" title="class in net.yadaframework.components">YadaSleepingRateLimiter</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A rate limiter that sleeps to distribute calls in a time interval.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/social/YadaSocial.html" title="class in net.yadaframework.web.social">YadaSocial</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/YadaSocialException.html" title="class in net.yadaframework.exceptions">YadaSocialException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">Error while using the social functions</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/YadaSql.html" title="class in net.yadaframework.persistence">YadaSql</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Incrementally and conditionally builds a sql select/update query with MySQL syntax.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/persistence/YadaSqlBuilder.html" title="class in net.yadaframework.persistence">YadaSqlBuilder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaSrcAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaSrcAttrProcessor</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">Deprecated.</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaSrcsetAttrProcessor.html" title="class in net.yadaframework.web.dialect">YadaSrcsetAttrProcessor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">Deprecated.</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="net/yadaframework/exceptions/YadaSystemException.html" title="class in net.yadaframework.exceptions">YadaSystemException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Unchecked exception thrown when the system is in error (filesystem problems, memory exceptions, etc)</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/web/dialect/YadaTextareaTagProcessor.html" title="class in net.yadaframework.web.dialect">YadaTextareaTagProcessor</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Defines the &lt;yada:textarea&gt; tag</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaTomcatServer.html" title="class in net.yadaframework.core">YadaTomcatServer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Tomcat Embedded.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaUtil.html" title="class in net.yadaframework.components">YadaUtil</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="net/yadaframework/web/YadaViews.html" title="interface in net.yadaframework.web">YadaViews</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Some view strings that have specific effects when returned from a @Controller</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaWebApplicationInitializer.html" title="class in net.yadaframework.core">YadaWebApplicationInitializer</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Adds configuration from Yada optional projects.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/core/YadaWebConfig.html" title="class in net.yadaframework.core">YadaWebConfig</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="net/yadaframework/components/YadaWebUtil.html" title="class in net.yadaframework.components">YadaWebUtil</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility methods to be used for web development</div>
</div>
</div>
</div>
</div>
</main>
</div>
</div>
</body>
</html>
