<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaPageRequest (YadaWeb '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.web, class: YadaPageRequest">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.web</a></div>
<h1 title="Class YadaPageRequest" class="title">Class YadaPageRequest</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.web.YadaPageRequest</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">YadaPageRequest</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">A page for pageable content.
 A common use case is to implement web pagination in both directions.
 It also solves the problem of the browser back button not loading previously loaded values, via the loadPrevious flag.</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaPageRequest</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new "invalid" YadaPageRequest, with <code>page=-1, size=0</code>.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(int,int)" class="member-name-link">YadaPageRequest</a><wbr>(int&nbsp;page,
 int&nbsp;size)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new <a href="YadaPageRequest.html" title="class in net.yadaframework.web"><code>YadaPageRequest</code></a>.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(int,int,boolean)" class="member-name-link">YadaPageRequest</a><wbr>(int&nbsp;page,
 int&nbsp;size,
 boolean&nbsp;loadPrevious)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new <a href="YadaPageRequest.html" title="class in net.yadaframework.web"><code>YadaPageRequest</code></a>.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(int,int,boolean,java.lang.String)" class="member-name-link">YadaPageRequest</a><wbr>(int&nbsp;page,
 int&nbsp;size,
 boolean&nbsp;loadPrevious,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramPrefix)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new <a href="YadaPageRequest.html" title="class in net.yadaframework.web"><code>YadaPageRequest</code></a>.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#addSort(java.lang.String,java.lang.Boolean,java.lang.Boolean)" class="member-name-link">addSort</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;desc,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;ignoreCase)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use <a href="#appendSort(java.lang.String)"><code>appendSort(String)</code></a> and <a href="#prependSort(java.lang.String)"><code>prependSort(String)</code></a> instead</div>
</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaPageSort.YadaPageSortApi.html" title="class in net.yadaframework.web">YadaPageSort.YadaPageSortApi</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#appendSort(java.lang.String)" class="member-name-link">appendSort</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramNames)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add sort parameters after any existing ones</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#equals(java.lang.Object)" class="member-name-link">equals</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFirstPageRequest()" class="member-name-link">getFirstPageRequest</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getFirstResult()" class="member-name-link">getFirstResult</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the position of the first element to be loaded from the database:
 <code>size*page</code> when loadPrevious is false, 0 otherwise</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getMaxResults()" class="member-name-link">getMaxResults</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the amount of rows to fetch from the database + 1.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getNextPageRequest()" class="member-name-link">getNextPageRequest</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getOffset()" class="member-name-link">getOffset</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns the position of the first element of this page in the database: <code>size*page</code></div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPage()" class="member-name-link">getPage</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPageSize()" class="member-name-link">getPageSize</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Spring Data - compatible method to get the page size (number of rows)</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaPageSort.html" title="class in net.yadaframework.web">YadaPageSort</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPageSort()" class="member-name-link">getPageSort</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getParamPrefix()" class="member-name-link">getParamPrefix</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPreviousOrFirstRequest()" class="member-name-link">getPreviousOrFirstRequest</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getPreviousPageRequest()" class="member-name-link">getPreviousPageRequest</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSize()" class="member-name-link">getSize</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSort()" class="member-name-link">getSort</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sort strings passed as request parameters - not to be used by the application</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getYadaContainer()" class="member-name-link">getYadaContainer</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getYadaScroll()" class="member-name-link">getYadaScroll</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hashCode()" class="member-name-link">hashCode</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isFirst()" class="member-name-link">isFirst</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isLoadPrevious()" class="member-name-link">isLoadPrevious</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isValid()" class="member-name-link">isValid</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if this object has been created with actual values.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#of(int,int)" class="member-name-link">of</a><wbr>(int&nbsp;page,
 int&nbsp;size)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Creates a YadaPageRequest with the given page and size.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="YadaPageSort.YadaPageSortApi.html" title="class in net.yadaframework.web">YadaPageSort.YadaPageSortApi</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#prependSort(java.lang.String)" class="member-name-link">prependSort</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramNames)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add sort parameters before any existing ones</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setLoadPrevious(boolean)" class="member-name-link">setLoadPrevious</a><wbr>(boolean&nbsp;loadPrevious)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Tell if data from previous pages should also be returned</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPage(int)" class="member-name-link">setPage</a><wbr>(int&nbsp;page)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setPageSort(net.yadaframework.web.YadaPageSort)" class="member-name-link">setPageSort</a><wbr>(<a href="YadaPageSort.html" title="class in net.yadaframework.web">YadaPageSort</a>&nbsp;pageSort)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set the page sort options</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setParamPrefix(java.lang.String)" class="member-name-link">setParamPrefix</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramPrefix)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSize(int)" class="member-name-link">setSize</a><wbr>(int&nbsp;size)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setSort(java.util.List)" class="member-name-link">setSort</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;sort)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">sort strings passed as request parameters - not to be used by the application</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setYadaContainer(java.lang.String)" class="member-name-link">setYadaContainer</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;yadaContainer)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#setYadaScroll(int)" class="member-name-link">setYadaScroll</a><wbr>(int&nbsp;yadaScroll)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#toString()" class="member-name-link">toString</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaPageRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaPageRequest</span>()</div>
<div class="block">Creates a new "invalid" YadaPageRequest, with <code>page=-1, size=0</code>.
 Has the same meaning of a "null" value.
 Used by Spring when injecting method parameters in a @Controller
 with no request values to set.
 See <a href="#isValid()"><code>isValid()</code></a></div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int)">
<h3>YadaPageRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaPageRequest</span><wbr><span class="parameters">(int&nbsp;page,
 int&nbsp;size)</span></div>
<div class="block">Creates a new <a href="YadaPageRequest.html" title="class in net.yadaframework.web"><code>YadaPageRequest</code></a>. Pages are zero indexed, thus providing 0 for <code>page</code> will return the first
 page.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>page</code> - zero-based page index, must not be less than zero.</dd>
<dd><code>size</code> - the size of the page to be returned, must not be less than one.</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int,boolean)">
<h3>YadaPageRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaPageRequest</span><wbr><span class="parameters">(int&nbsp;page,
 int&nbsp;size,
 boolean&nbsp;loadPrevious)</span></div>
<div class="block">Creates a new <a href="YadaPageRequest.html" title="class in net.yadaframework.web"><code>YadaPageRequest</code></a>. Pages are zero indexed, thus providing 0 for <code>page</code> will return the first
 page.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>page</code> - zero-based page index, must not be less than zero.</dd>
<dd><code>size</code> - the size of the page to be returned, must not be less than one.</dd>
<dd><code>loadPrevious</code> - true if all pages before this one must be fetched from database</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(int,int,boolean,java.lang.String)">
<h3>YadaPageRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaPageRequest</span><wbr><span class="parameters">(int&nbsp;page,
 int&nbsp;size,
 boolean&nbsp;loadPrevious,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramPrefix)</span></div>
<div class="block">Creates a new <a href="YadaPageRequest.html" title="class in net.yadaframework.web"><code>YadaPageRequest</code></a>. Pages are zero indexed, thus providing 0 for <code>page</code> will return the first
 page.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>page</code> - zero-based page index, must not be less than zero.</dd>
<dd><code>size</code> - the size of the page to be returned, must not be less than one.</dd>
<dd><code>loadPrevious</code> - true if all pages before this one must be fetched from database</dd>
<dd><code>paramPrefix</code> - prefix to pagination request parameters to use for multiple paginations on same page</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="appendSort(java.lang.String)">
<h3>appendSort</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaPageSort.YadaPageSortApi.html" title="class in net.yadaframework.web">YadaPageSort.YadaPageSortApi</a></span>&nbsp;<span class="element-name">appendSort</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramNames)</span></div>
<div class="block">Add sort parameters after any existing ones</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>paramNames</code> - comma-separated list of sort parameters</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="prependSort(java.lang.String)">
<h3>prependSort</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaPageSort.YadaPageSortApi.html" title="class in net.yadaframework.web">YadaPageSort.YadaPageSortApi</a></span>&nbsp;<span class="element-name">prependSort</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramNames)</span></div>
<div class="block">Add sort parameters before any existing ones</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>paramNames</code> - comma-separated list of sort parameters</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="of(int,int)">
<h3>of</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></span>&nbsp;<span class="element-name">of</span><wbr><span class="parameters">(int&nbsp;page,
 int&nbsp;size)</span></div>
<div class="block">Creates a YadaPageRequest with the given page and size.
 Drop-in replacement for the equivalent Spring Data PageRequest.of() method</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>page</code> - </dd>
<dd><code>size</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isValid()">
<h3>isValid</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isValid</span>()</div>
<div class="block">Check if this object has been created with actual values.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>true if this object has been initialized</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addSort(java.lang.String,java.lang.Boolean,java.lang.Boolean)">
<h3>addSort</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></span>&nbsp;<span class="element-name">addSort</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramName,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;desc,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;ignoreCase)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use <a href="#appendSort(java.lang.String)"><code>appendSort(String)</code></a> and <a href="#prependSort(java.lang.String)"><code>prependSort(String)</code></a> instead</div>
</div>
<div class="block">Add a sort order to this request</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>paramName</code> - the name to sort on, can be multiple comma-separated names</dd>
<dd><code>desc</code> - true for descending order, otherwise false or null</dd>
<dd><code>ignoreCase</code> - true to ignore case when sorting, otherwise false or null</dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPageSort()">
<h3>getPageSort</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaPageSort.html" title="class in net.yadaframework.web">YadaPageSort</a></span>&nbsp;<span class="element-name">getPageSort</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the page sort options</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPageSort(net.yadaframework.web.YadaPageSort)">
<h3>setPageSort</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPageSort</span><wbr><span class="parameters">(<a href="YadaPageSort.html" title="class in net.yadaframework.web">YadaPageSort</a>&nbsp;pageSort)</span></div>
<div class="block">Set the page sort options</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>pageSort</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setLoadPrevious(boolean)">
<h3>setLoadPrevious</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setLoadPrevious</span><wbr><span class="parameters">(boolean&nbsp;loadPrevious)</span></div>
<div class="block">Tell if data from previous pages should also be returned</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>loadPrevious</code> - true to load data from previous pages (only applicabile when page&gt;1)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getNextPageRequest()">
<h3>getNextPageRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></span>&nbsp;<span class="element-name">getNextPageRequest</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getPreviousPageRequest()">
<h3>getPreviousPageRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></span>&nbsp;<span class="element-name">getPreviousPageRequest</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getFirstPageRequest()">
<h3>getFirstPageRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></span>&nbsp;<span class="element-name">getFirstPageRequest</span>()</div>
</section>
</li>
<li>
<section class="detail" id="toString()">
<h3>toString</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">toString</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSize()">
<h3>getSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getSize</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the number of rows for this page</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getPage()">
<h3>getPage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getPage</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The current page number starting at 0</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getOffset()">
<h3>getOffset</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getOffset</span>()</div>
<div class="block">Returns the position of the first element of this page in the database: <code>size*page</code></div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getFirstResult()">
<h3>getFirstResult</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getFirstResult</span>()</div>
<div class="block">Returns the position of the first element to be loaded from the database:
 <code>size*page</code> when loadPrevious is false, 0 otherwise</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="getMaxResults()">
<h3>getMaxResults</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getMaxResults</span>()</div>
<div class="block">Returns the amount of rows to fetch from the database + 1.
 It is equal to 
<details class="invalid-tag">
<summary>invalid @link</summary>
<pre>{@link #getSize()+1</pre>
</details>
} when loadPrevious is false, otherwise it
 adds the count of all the previous pages to the value then adds 1
 to find out if there are more rows to fetch after this page.
 Note: this method must only be used when the results are stored into a YadaPageRows object
 otherwise the page size will be one element bigger than expected.
 <p>If you are not going to store the result in YadaPageRows, use <a href="#getSize()"><code>getSize()</code></a> instead</p></div>
<dl class="notes">
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="YadaPageRows.html" title="class in net.yadaframework.web"><code>YadaPageRows</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isFirst()">
<h3>isFirst</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isFirst</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getPreviousOrFirstRequest()">
<h3>getPreviousOrFirstRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="YadaPageRequest.html" title="class in net.yadaframework.web">YadaPageRequest</a></span>&nbsp;<span class="element-name">getPreviousOrFirstRequest</span>()</div>
</section>
</li>
<li>
<section class="detail" id="isLoadPrevious()">
<h3>isLoadPrevious</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isLoadPrevious</span>()</div>
</section>
</li>
<li>
<section class="detail" id="hashCode()">
<h3>hashCode</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">hashCode</span>()</div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="equals(java.lang.Object)">
<h3>equals</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">equals</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;obj)</span></div>
<dl class="notes">
<dt>Overrides:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></code></dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setPage(int)">
<h3>setPage</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setPage</span><wbr><span class="parameters">(int&nbsp;page)</span></div>
</section>
</li>
<li>
<section class="detail" id="setSize(int)">
<h3>setSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSize</span><wbr><span class="parameters">(int&nbsp;size)</span></div>
</section>
</li>
<li>
<section class="detail" id="getPageSize()">
<h3>getPageSize</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getPageSize</span>()</div>
<div class="block">Spring Data - compatible method to get the page size (number of rows)</div>
<dl class="notes">
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getSize()"><code>getSize()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getSort()">
<h3>getSort</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getSort</span>()</div>
<div class="block">sort strings passed as request parameters - not to be used by the application</div>
<dl class="notes">
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#getPageSort()"><code>getPageSort()</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setSort(java.util.List)">
<h3>setSort</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setSort</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;sort)</span></div>
<div class="block">sort strings passed as request parameters - not to be used by the application</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>sort</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getYadaContainer()">
<h3>getYadaContainer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getYadaContainer</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setYadaContainer(java.lang.String)">
<h3>setYadaContainer</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setYadaContainer</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;yadaContainer)</span></div>
</section>
</li>
<li>
<section class="detail" id="getYadaScroll()">
<h3>getYadaScroll</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">getYadaScroll</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setYadaScroll(int)">
<h3>setYadaScroll</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setYadaScroll</span><wbr><span class="parameters">(int&nbsp;yadaScroll)</span></div>
</section>
</li>
<li>
<section class="detail" id="getParamPrefix()">
<h3>getParamPrefix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getParamPrefix</span>()</div>
</section>
</li>
<li>
<section class="detail" id="setParamPrefix(java.lang.String)">
<h3>setParamPrefix</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">setParamPrefix</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;paramPrefix)</span></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
