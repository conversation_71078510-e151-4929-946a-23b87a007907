package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.MyCollection;

@Repository
@Transactional(readOnly = true)
public class MyCollectionDao {

    @PersistenceContext EntityManager em;

    /**
     * Trova una collection per l'utente specificato
     * @param collectionId
     * @param userProfileId
     * @return
     */
    public MyCollection findOneByIdAndUserProfileId(long collectionId, Long userProfileId) {
        String sql = "from MyCollection where id = :collectionId and userProfile.id = :userProfileId";
        try {
            return em.createQuery(sql, MyCollection.class)
                .setMaxResults(1)
                .setParameter("collectionId", collectionId)
                .setParameter("userProfileId", userProfileId)
                .getSingleResult();
        } catch (NonUniqueResultException | NoResultException e) {
            return null;
        }
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from MyCollection", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public MyCollection save(MyCollection entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<MyCollection> findById(Long entityId) {
        MyCollection result = em.find(MyCollection.class, entityId);
        return Optional.ofNullable(result);
    }

    public MyCollection findOne(Long entityId) {
        return em.find(MyCollection.class, entityId);
    }

    public List<MyCollection> findAll() {
        return em.createQuery("from MyCollection", MyCollection.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<MyCollection> batchToSave) {
        for (MyCollection entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(MyCollection entity) {
        em.remove(entity);
    }
}
