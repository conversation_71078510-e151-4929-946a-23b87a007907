create table Address (id bigint not null auto_increment, cap varchar(255), cellulare varchar(255), citta varchar(255), cognome varchar(255), indirizzoLinea1 varchar(255), indirizzoLinea2 varchar(255), nazione varchar(255), nome varchar(255), note varchar(255), provincia varchar(255), stato varchar(255), telefono varchar(255), primary key (id)) engine=InnoDB;
create table AmdRegistrationRequest (newsletterFlag bit, newsletterFlag2 bit, id bigint not null, newsletterFlagDate datetime(6), destinationUrl varchar(255), name varchar(255), surname varchar(255), primary key (id)) engine=InnoDB;
create table Articolo (id bigint not null auto_increment, lastUpdate datetime(6), descrizioneSap varchar(40), codiceSap varchar(255) not null, primary key (id)) engine=InnoDB;
create table Articolo_SortedUploadedFiles (Articolo_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (Articolo_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
create table ArticoloDescrizione (webDescription bit not null, localeCode varchar(5), Articolo_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table CatalogConfig (enableNovelties bit, id bigint not null auto_increment, version bigint not null, primary key (id)) engine=InnoDB;
create table CatalogConfig_noveltiesName (CatalogConfig_id bigint not null, locale varchar(32) not null, noveltiesName varchar(255), primary key (CatalogConfig_id, locale)) engine=InnoDB;
create table CatalogConfig_YadaAttachedFile (CatalogConfig_id bigint not null, noveltiesHero_id bigint not null, primary key (CatalogConfig_id, noveltiesHero_id)) engine=InnoDB;
create table CodiceProdotto (id bigint not null auto_increment, value varchar(255), primary key (id)) engine=InnoDB;
create table Configurator (enabled bit not null, recessedAvailable bit not null, smdAvailable bit not null, suspensionAvailable bit not null, famiglia_id bigint not null, id bigint not null auto_increment, version bigint not null, name varchar(255), primary key (id)) engine=InnoDB;
create table ConfiguratorShape (accessory bit not null, attachment bit not null, emissione integer, enabled bit not null, installation integer not null, magnetic bit not null, variante integer, angle varchar(8), cassaforma_id bigint, configuratorForAttach_id bigint, configurator_id bigint, defaultModel_id bigint, dxf2DModel_id bigint, dxf2DWallModel_id bigint, dxf3DModel_id bigint, icon_id bigint, id bigint not null auto_increment, pos bigint not null, subfamily_id bigint, version bigint not null, diameter varchar(16), length varchar(16), radius varchar(16), width varchar(16), text1 varchar(32), text2 varchar(32), text3 varchar(32), movableType varchar(64), additionalWhere varchar(256), keywords varchar(256), primary key (id)) engine=InnoDB;
create table ConfiguratorShape_emissions (ConfiguratorShape_id bigint not null, Subfamily_id bigint not null, emissions varchar(255), primary key (ConfiguratorShape_id, Subfamily_id)) engine=InnoDB;
create table ConfiguratorShape_Subfamily (ConfiguratorShape_id bigint not null, subfamilies_id bigint not null, primary key (ConfiguratorShape_id, subfamilies_id)) engine=InnoDB;
create table ConfiguratorShape_YadaAttachedFile (ConfiguratorShape_id bigint not null, Subfamily_id bigint not null, models_id bigint not null, primary key (ConfiguratorShape_id, Subfamily_id)) engine=InnoDB;
create table Countries (code varchar(2) not null, continent_code varchar(2) not null, display_order integer not null, iso3 varchar(3) not null, number smallint not null, country_id bigint not null auto_increment, name varchar(64) not null, full_name varchar(128) not null, primary key (country_id)) engine=InnoDB;
create table CountryProdottoList (countryCodeString varchar(4), generic bit not null, id bigint not null auto_increment, primary key (id)) engine=InnoDB;
create table CountryProdottoList_Prodotto (sort integer not null, CountryProdottoList_id bigint not null, prodotti_id bigint not null, primary key (sort, CountryProdottoList_id)) engine=InnoDB;
create table CurvaFotometrica (pos integer not null, variante integer not null, id bigint not null auto_increment, subfamily_id bigint, sorgenteLuminosa varchar(16), beam varchar(32), primary key (id)) engine=InnoDB;
create table CurvaFotometrica_SortedUploadedFiles (CurvaFotometrica_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (CurvaFotometrica_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
create table CustomButtonLink (localeCode varchar(5), Subfamily_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table CustomButtonName (localeCode varchar(5), Subfamily_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table Designer (nationality varchar(3), id bigint not null auto_increment, name varchar(50), surname varchar(50) not null, homepage varchar(255), primary key (id)) engine=InnoDB;
create table Designer_SortedUploadedFiles (Designer_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (Designer_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
create table DesignerDescription (localeCode varchar(5), Designer_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ElectricalName (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table Etichetta (anno date not null, etichetta integer not null, id bigint not null auto_increment, codice varchar(32), primary key (id)) engine=InnoDB;
create table Famiglia (appCompatible bit not null, appCompatibleColor integer not null, published bit not null, id bigint not null auto_increment, version bigint not null, primary key (id)) engine=InnoDB;
create table Famiglia_Designer (designers_ORDER integer not null, Famiglia_id bigint not null, designers_id bigint not null, primary key (designers_ORDER, Famiglia_id)) engine=InnoDB;
create table Famiglia_name (Famiglia_id bigint not null, locale varchar(32) not null, name varchar(255), primary key (Famiglia_id, locale)) engine=InnoDB;
create table FamilyImages (Famiglia_id bigint, bigImage_id bigint, id bigint not null auto_increment, largeImage_id bigint, smallImage_id bigint, tallImage_id bigint, version bigint not null, typology varchar(255), primary key (id)) engine=InnoDB;
create table FilterVariante (variante integer not null, id bigint not null auto_increment, subfamily_id bigint, primary key (id)) engine=InnoDB;
create table FilterVariante_variants (variants integer, FilterVariante_id bigint not null) engine=InnoDB;
create table FilterVarianteDescription (localeCode varchar(5), FilterVariante_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table GalleryDescription (plusPositionx float(23) not null, plusPositiony float(23) not null, pos integer not null, id bigint not null auto_increment, subfamily_id bigint, primary key (id)) engine=InnoDB;
create table GalleryDescription_SortedUploadedFiles (GalleryDescription_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (GalleryDescription_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
create table GalleryDescriptionText (localeCode varchar(5), GalleryDescription_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table HomePage2 (enabled bit not null, id bigint not null auto_increment, version bigint not null, description varchar(255), primary key (id)) engine=InnoDB;
create table Lampadina (lampClass varchar(3), id bigint not null auto_increment, beam varchar(10), colorTemperature varchar(10), lampDuration varchar(10), wattage varchar(10), lbs varchar(20), colorRendering varchar(30), ilCosL varchar(30), ilcos varchar(30), luminousFlux varchar(30), socket varchar(30), lampCategory varchar(50), codiceSap varchar(255), primary key (id)) engine=InnoDB;
create table Lampadina_SortedUploadedFiles (Lampadina_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (Lampadina_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
create table Led (cob bit not null, oled bit not null, id bigint not null auto_increment, beam varchar(10), colourRendering varchar(10), colourTemperature varchar(10), lampClass varchar(10), ledLuminousFlux varchar(10), ledNumber varchar(10), ledTipology varchar(10), ledWattage varchar(10), ledWattageSingle varchar(10), colorTolerance varchar(32), cri varchar(32), efficacy varchar(32), serviceLife varchar(32), current varchar(64), voltage varchar(64), cctLambda varchar(128), radiantFlux varchar(128), serviceLife2 varchar(128), codiceSap varchar(255), socket varchar(255), primary key (id)) engine=InnoDB;
create table Led_SortedUploadedFiles (Led_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (Led_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
create table MyCollection (id bigint not null auto_increment, lastModified TIMESTAMP NULL null, userProfile_id bigint, version bigint not null, subtitle varchar(128), title varchar(128), primary key (id)) engine=InnoDB;
create table MyCollection_productIds (sort integer not null, MyCollection_id bigint not null, productIds bigint, primary key (sort, MyCollection_id)) engine=InnoDB;
create table MyCollection_projectIds (sort integer not null, MyCollection_id bigint not null, projectIds bigint, primary key (sort, MyCollection_id)) engine=InnoDB;
create table NewsJournal (enabled bit not null, topNews bit not null, unlisted bit not null, id bigint not null auto_increment, publishDate datetime(6), showDate datetime(6), thumbnail_id bigint, topIndice_id bigint, version bigint not null, primary key (id)) engine=InnoDB;
create table NewsJournal_titlePartOne (NewsJournal_id bigint not null, locale varchar(32) not null, titlePartOne varchar(255), primary key (NewsJournal_id, locale)) engine=InnoDB;
create table NewsJournal_titlePartTwo (NewsJournal_id bigint not null, locale varchar(32) not null, titlePartTwo varchar(255), primary key (NewsJournal_id, locale)) engine=InnoDB;
create table PageModule (enabled bit not null, flag1 bit not null, flag2 bit not null, flag3 bit not null, flag4 bit not null, radioValue integer not null, swap bit not null, type integer not null, year integer, homepage_id bigint, id bigint not null auto_increment, imageTwo_id bigint, image_id bigint, news_id bigint, pdfFile_id bigint, pos bigint, project_id bigint, version bigint not null, DTYPE varchar(31) not null check ((DTYPE in ('PageModule','HomeModule','NewsModule','ProjectModule'))), linkUrl varchar(256), video varchar(1024), author varchar(255), data1 varchar(255), data2 varchar(255), data3 varchar(255), data4 varchar(255), photoby varchar(255), primary key (id), check (DTYPE <> 'ProjectModule' or (year is not null))) engine=InnoDB;
create table PageModule_linkText (PageModule_id bigint not null, locale varchar(32) not null, linkText varchar(256), primary key (PageModule_id, locale)) engine=InnoDB;
create table PageModule_Subfamily (PageModule_id bigint not null, subfamilies_id bigint not null) engine=InnoDB;
create table PageModule_textOne (PageModule_id bigint not null, locale varchar(32) not null, textOne varchar(8192), primary key (PageModule_id, locale)) engine=InnoDB;
create table PageModule_textTwo (PageModule_id bigint not null, locale varchar(32) not null, textTwo varchar(8192), primary key (PageModule_id, locale)) engine=InnoDB;
create table PageModule_video2 (PageModule_id bigint not null, locale varchar(32) not null, video2 varchar(1024), primary key (PageModule_id, locale)) engine=InnoDB;
create table PageModule_YadaAttachedFile (PageModule_id bigint not null, carrouselImages_id bigint not null) engine=InnoDB;
create table PageModule_YadaGallerySlide1 (PageModule_id bigint not null, gallery1_id bigint not null, primary key (PageModule_id, gallery1_id)) engine=InnoDB;
create table PageModule_YadaGallerySlide2 (PageModule_id bigint not null, gallery2_id bigint not null, primary key (PageModule_id, gallery2_id)) engine=InnoDB;
create table PageModule_YadaGallerySlide3 (PageModule_id bigint not null, gallery3_id bigint not null, primary key (PageModule_id, gallery3_id)) engine=InnoDB;
create table PdfData (creationDate datetime(6), id bigint not null auto_increment, productConfiguration_id bigint, version bigint not null, frontView longtext, jsonConnections longtext, jsonPowerOptions longtext, perspectiveView longtext, sideView longtext, topView longtext, primary key (id)) engine=InnoDB;
create table PrenotaSlot (capacity integer not null, sizeMinutes integer not null, id bigint not null auto_increment, prenotaStore_id bigint not null, start TIMESTAMP NULL null, version bigint not null, flagKey varchar(255), primary key (id)) engine=InnoDB;
create table PrenotaStore (enabled bit not null, id bigint not null auto_increment, version bigint not null, nameAndAddress varchar(255), storeEmail varchar(255), primary key (id)) engine=InnoDB;
create table PrenotaUser (people integer not null, cancelToken bigint not null, id bigint not null auto_increment, prenotaSlot_id bigint, version bigint not null, message varchar(8192), city varchar(255), country varchar(255), email varchar(255), name varchar(255), phone varchar(255), profession varchar(255), surname varchar(255), primary key (id)) engine=InnoDB;
create table PressKit (id bigint not null auto_increment, kit_id bigint, version bigint not null, visibleFrom datetime(6), visibleTo datetime(6), size varchar(32), primary key (id)) engine=InnoDB;
create table PressKit_title (PressKit_id bigint not null, locale varchar(32) not null, title varchar(255), primary key (PressKit_id, locale)) engine=InnoDB;
create table PrezzoSap (countryCode varchar(4), importo decimal(38,2), Articolo_id bigint, id bigint not null auto_increment, scadenza datetime(6), primary key (id)) engine=InnoDB;
create table Prodotto (appCompatible bit not null, cutoutShape integer not null, dimmable bit not null, dimmer bit not null, emissione integer not null, integralis bit not null, newProduct bit not null, nonPlanar bit not null, pos integer not null, scenarios bit not null, angle varchar(8), id bigint not null auto_increment, subfamily_id bigint not null, countryCode varchar(16), radius varchar(16), stato varchar(20), CCT varchar(32), beam varchar(32), efficacy varchar(32), efficiency varchar(32), flux varchar(32), glowWireTest varchar(60), impactResistence varchar(60), CRI varchar(255), award varchar(255), baseDescription varchar(255), baseDiameter varchar(255), baseLength varchar(255), baseWidth varchar(255), codice varchar(255) not null, configuration varchar(255), cutoutDiameter varchar(255), cutoutLength varchar(255), cutoutWidth varchar(255), depth varchar(255), diameter varchar(255), dimmableTypology varchar(255), dimmerTypology varchar(255), emergencyDuration varchar(255), falseCeilingDescription varchar(255), height varchar(255), homepage varchar(255), homologation varchar(255), insulationClass varchar(255), ip varchar(255), lPeak varchar(255), length varchar(255), maxExtensionHeight varchar(255), maxExtensionLength varchar(255), maxHeightFromCeiling varchar(255), radiantFlux varchar(255), recessedDepth varchar(255), rotation varchar(255), tilt varchar(255), trasformerPower varchar(255), voltage varchar(255), waterRepellent varchar(255), weight varchar(255), width varchar(255), year varchar(255), primary key (id)) engine=InnoDB;
create table Prodotto_Accessorio (Prodotto_id bigint not null, accessori_id bigint not null) engine=InnoDB;
create table Prodotto_Componente (Prodotto_id bigint not null, componenti_id bigint not null) engine=InnoDB;
create table Prodotto_Lampadina (Prodotto_id bigint not null, listLamps_id bigint not null) engine=InnoDB;
create table Prodotto_LampadinaEsclusa (Prodotto_id bigint not null, listLampsExcluded_id bigint not null) engine=InnoDB;
create table Prodotto_LampadinaEsclusaAlt (Prodotto_id bigint not null, listLampsExcludedAlt_id bigint not null) engine=InnoDB;
create table Prodotto_Led (Prodotto_id bigint not null, listLeds_id bigint not null) engine=InnoDB;
create table Prodotto_LedEscluso (Prodotto_id bigint not null, listLedsExcluded_id bigint not null) engine=InnoDB;
create table Prodotto_LedEsclusoAlt (Prodotto_id bigint not null, listLedsExcludedAlt_id bigint not null) engine=InnoDB;
create table Prodotto_mTipologia (mTipologia integer, Prodotto_id bigint not null) engine=InnoDB;
create table Prodotto_newForCountry (country varchar(2), Prodotto_id bigint not null) engine=InnoDB;
create table Prodotto_SortedUploadedFiles (Prodotto_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (Prodotto_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
create table Prodotto_variante (variante integer, Prodotto_id bigint not null) engine=InnoDB;
create table ProdottoBallast (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoBaseColor (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoColor (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoDescription (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoElectrical (dimmable bit not null, dimmer bit not null, id bigint not null auto_increment, prodotto_id bigint, CCT varchar(32), efficacy varchar(32), efficiency varchar(32), flux varchar(32), CRI varchar(255), dimmableTypology varchar(255), dimmerTypology varchar(255), emergencyDuration varchar(255), homologation varchar(255), insulationClass varchar(255), ip varchar(255), lPeak varchar(255), radiantFlux varchar(255), trasformerPower varchar(255), voltage varchar(255), primary key (id)) engine=InnoDB;
create table ProdottoElectricalBallast (localeCode varchar(5), ProdottoElectrical_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoElectricalEmergency (localeCode varchar(5), ProdottoElectrical_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoElectricalName (localeCode varchar(5), ProdottoElectrical_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoElectricalRemoteControl (localeCode varchar(5), ProdottoElectrical_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoElectricalTransformer (localeCode varchar(5), ProdottoElectrical_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoEmergency (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoMaterial (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoName (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoNote (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoPdfTimestamp (id bigint not null auto_increment, pdfTimestamp datetime(6), prodotto_id bigint not null, primary key (id)) engine=InnoDB;
create table ProdottoRemoteControl (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoShortName (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoSubName (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table ProdottoTransformer (localeCode varchar(5), Prodotto_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table Product_AlsoBought (Prodotto_id bigint not null, alsoBought_id bigint not null, primary key (Prodotto_id, alsoBought_id)) engine=InnoDB;
create table Product_RelatedProducts (Prodotto_id bigint not null, relatedProducts_id bigint not null, primary key (Prodotto_id, relatedProducts_id)) engine=InnoDB;
create table ProductConfiguration (installation integer, configurator_id bigint, id bigint not null auto_increment, lastSaved datetime(6), userProfile_id bigint, version bigint not null, countryCode varchar(16), floorPlanName varchar(64), name varchar(128), globalOptions longtext, scene longtext, primary key (id)) engine=InnoDB;
create table ProductFile (type tinyint check ((type between 0 and 12)), attachedFile_id bigint, id bigint not null auto_increment, subfamily_id bigint, timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP null, version bigint not null, primary key (id)) engine=InnoDB;
create table ProductFile_GalleryImage (galleryImage_id bigint not null, productfile_id bigint not null, primary key (galleryImage_id, productfile_id)) engine=InnoDB;
create table ProductFile_Prodotto (prodotto_id bigint not null, productfile_id bigint not null, primary key (prodotto_id, productfile_id)) engine=InnoDB;
create table Project (bespoken bit not null, enabled bit not null, sartorial bit not null, scenarios bit not null, selected bit not null, bigImage_id bigint, id bigint not null auto_increment, publishDate datetime(6), smallImage_id bigint, version bigint not null, primary key (id)) engine=InnoDB;
create table Project_location (Project_id bigint not null, locale varchar(32) not null, location varchar(255), primary key (Project_id, locale)) engine=InnoDB;
create table Project_titlePartOne (Project_id bigint not null, locale varchar(32) not null, titlePartOne varchar(255), primary key (Project_id, locale)) engine=InnoDB;
create table Project_titlePartTwo (Project_id bigint not null, locale varchar(32) not null, titlePartTwo varchar(255), primary key (Project_id, locale)) engine=InnoDB;
create table ProjectModule_changes (ProjectModule_id bigint not null, locale varchar(32) not null, changes varchar(8192), primary key (ProjectModule_id, locale)) engine=InnoDB;
create table Property (id bigint not null auto_increment, applicationName varchar(32), name varchar(32), value longtext, primary key (id)) engine=InnoDB;
create table Publication (hiddenBrochure bit not null, pos integer not null, typology integer, coverImage_id bigint, docFileDe_id bigint, docFileEn_id bigint, docFileEs_id bigint, docFileFr_id bigint, docFileIt_id bigint, id bigint not null auto_increment, version bigint not null, primary key (id)) engine=InnoDB;
create table Publication_link (Publication_id bigint not null, locale varchar(32) not null, link varchar(255), primary key (Publication_id, locale)) engine=InnoDB;
create table Publication_Subfamily (Publication_id bigint not null, Subfamily_id bigint not null) engine=InnoDB;
create table Publication_subtitle (Publication_id bigint not null, locale varchar(32) not null, subtitle varchar(255), primary key (Publication_id, locale)) engine=InnoDB;
create table Publication_title (Publication_id bigint not null, locale varchar(32) not null, title varchar(255), primary key (Publication_id, locale)) engine=InnoDB;
create table Publications (hiddenBrochure bit not null, pos integer not null, typology integer, id bigint not null auto_increment, primary key (id)) engine=InnoDB;
create table Publications_SortedUploadedFiles (Publications_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (Publications_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
create table Publications_Subfamily (Publications_id bigint not null, sottofamiglie_id bigint not null) engine=InnoDB;
create table PublicationsLink (localeCode varchar(5), Publications_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table PublicationsSubtitle (localeCode varchar(5), Publications_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table PublicationsTitle (localeCode varchar(5), Publications_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table Responsible (iso2 varchar(4), id bigint not null auto_increment, nazione varchar(128), accountManager varchar(256), mail varchar(256), mailTwo varchar(256), primary key (id)) engine=InnoDB;
create table Sellability (countryCode varchar(4), sellable bit not null, Prodotto_id bigint, id bigint not null auto_increment, primary key (id)) engine=InnoDB;
create table SortedUploadedFiles (id bigint not null auto_increment, fileOrder varchar(255), primary key (id)) engine=InnoDB;
create table SortedUploadedFiles_UploadedFile (SortedUploadedFiles_id bigint not null, uploadedFiles_id bigint not null) engine=InnoDB;
create table Store (hidden bit not null, importanza integer, landingPage bit not null, id bigint not null auto_increment, indirizzo_id bigint, email varchar(255), fax varchar(255), latitude varchar(255), longitude varchar(255), mapLink varchar(255), nome varchar(255) not null, promotions_sortable varchar(255), tel1 varchar(255), tel2 varchar(255), web varchar(255), webshop varchar(255), primary key (id)) engine=InnoDB;
create table Store_promotions (Store_id bigint not null, promotion_code varchar(255)) engine=InnoDB;
create table Store_tipologia (type varchar(2), Store_id bigint not null) engine=InnoDB;
create table Subfamily (appCompatible bit not null, appCompatibleColor integer not null, beam bit not null, cct bit not null, color bit not null, controlType bit not null, cri bit not null, diameter bit not null, environment integer not null, flux bit not null, height bit not null, length bit not null, lightSource bit not null, magnetic bit not null, pos integer not null, power bit not null, published bit not null, tunableWhite bit not null, typology bit not null, famiglia_id bigint, id bigint not null auto_increment, series varchar(20), videoEmbed varchar(1024), areaContract varchar(255), primary key (id)) engine=InnoDB;
create table Subfamily_multiTipologia (multiTipologia integer, Subfamily_id bigint not null) engine=InnoDB;
create table Subfamily_SortedUploadedFiles (Subfamily_id bigint not null, sortedUploadedFilesMap_id bigint not null, sortedUploadedFilesMap_KEY varchar(255) not null, primary key (Subfamily_id, sortedUploadedFilesMap_KEY)) engine=InnoDB;
create table SubfamilyConfigText (localeCode varchar(5), Subfamily_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table SubfamilyCutoutShape (localeCode varchar(5), id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table SubfamilyEmission (localeCode varchar(5), Subfamily_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table SubfamilyEnvironment (localeCode varchar(5), id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table SubfamilyName (localeCode varchar(5), Subfamily_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table Tag (id bigint not null auto_increment, pos bigint not null, version bigint not null, DTYPE varchar(31) not null check ((DTYPE in ('Tag','NewsTag','ProjectTag'))), primary key (id)) engine=InnoDB;
create table Tag_name (Tag_id bigint not null, locale varchar(32) not null, name varchar(255), primary key (Tag_id, locale)) engine=InnoDB;
create table Tag_NewsJournal (news_id bigint not null, tags_id bigint not null) engine=InnoDB;
create table Tag_Project (project_id bigint not null, tags_id bigint not null) engine=InnoDB;
create table UploadedFile (id bigint not null auto_increment, extension varchar(16), primary key (id)) engine=InnoDB;
create table UploadedFileDescription (localeCode varchar(5), UploadedFile_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table UploadedFileTitle (localeCode varchar(5), UploadedFile_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table UserProfile_favouriteFamilies (UserProfile_id bigint not null, favouriteFamilies bigint) engine=InnoDB;
create table UserProfile_favouriteProducts (UserProfile_id bigint not null, favouriteProducts bigint) engine=InnoDB;
create table UserProfile_favouriteProjects (UserProfile_id bigint not null, favouriteProjects bigint) engine=InnoDB;
create table UserProfile_favouriteSubfamilies (UserProfile_id bigint not null, favouriteSubfamilies bigint) engine=InnoDB;
create table UserProfile_recentlyViewedProducts (UserProfile_id bigint not null, recentlyViewedProducts bigint) engine=InnoDB;
create table UserProfile_recentlyViewedSubfamilies (UserProfile_id bigint not null, recentlyViewedSubfamilies bigint) engine=InnoDB;
create table VarianteDescription (localeCode varchar(5), VarianteDescriptionInSubfamily_id bigint, id bigint not null auto_increment, value TEXT, primary key (id)) engine=InnoDB;
create table VarianteDescriptionInSubfamily (overwrite bit not null, variante integer not null, Subfamily_id bigint, id bigint not null auto_increment, primary key (id)) engine=InnoDB;
create table YadaAttachedFile (height integer, heightDesktop integer, heightMobile integer, heightPdf integer, published bit not null, width integer, widthDesktop integer, widthMobile integer, widthPdf integer, id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, sortOrder bigint not null, uploadTimestamp TIMESTAMP NULL null, version bigint not null, metadata varchar(1024), clientFilename varchar(255), filename varchar(255), filenameDesktop varchar(255), filenameMobile varchar(255), filenamePdf varchar(255), forLocale varchar(255), relativeFolderPath varchar(255), primary key (id)) engine=InnoDB;
create table YadaAttachedFile_description (YadaAttachedFile_id bigint not null, locale varchar(32) not null, description varchar(8192), primary key (YadaAttachedFile_id, locale)) engine=InnoDB;
create table YadaAttachedFile_title (YadaAttachedFile_id bigint not null, locale varchar(32) not null, title varchar(1024), primary key (YadaAttachedFile_id, locale)) engine=InnoDB;
create table YadaAutoLoginToken (expiration TIMESTAMP NULL null, id bigint not null auto_increment, timestamp TIMESTAMP NULL null, token bigint not null, version bigint not null, yadaUserCredentials_id bigint, primary key (id)) engine=InnoDB;
create table YadaBrowserId (id bigint not null auto_increment, leastSigBits bigint, mostSigBits bigint, version bigint not null, primary key (id)) engine=InnoDB;
create table YadaClause (clauseVersion integer not null, id bigint not null auto_increment, version bigint not null, name varchar(32) not null, content longtext, primary key (id)) engine=InnoDB;
create table YadaGallerySlide (flag1 bit not null, flag2 bit not null, slideEnabled bit not null, id bigint not null auto_increment, image_id bigint, pos bigint, version bigint not null, video_id bigint, text1 varchar(2048), text2 varchar(2048), text3 varchar(2048), text4 varchar(2048), data1 varchar(255), data2 varchar(255), data3 varchar(255), data4 varchar(255), data5 varchar(255), data6 varchar(255), primary key (id)) engine=InnoDB;
create table YadaGallerySlide_text5local (YadaGallerySlide_id bigint not null, locale varchar(32) not null, text5local varchar(2048), primary key (YadaGallerySlide_id, locale)) engine=InnoDB;
create table YadaGallerySlide_text6local (YadaGallerySlide_id bigint not null, locale varchar(32) not null, text6local varchar(2048), primary key (YadaGallerySlide_id, locale)) engine=InnoDB;
create table YadaGallerySlide_text7local (YadaGallerySlide_id bigint not null, locale varchar(32) not null, text7local varchar(2048), primary key (YadaGallerySlide_id, locale)) engine=InnoDB;
create table YadaGallerySlide_text8local (YadaGallerySlide_id bigint not null, locale varchar(32) not null, text8local varchar(2048), primary key (YadaGallerySlide_id, locale)) engine=InnoDB;
create table YadaJob (errorStreakCount integer not null, jobGroupPaused bit not null, jobPriority integer not null, jobRecoverable bit not null, id bigint not null auto_increment, jobLastSuccessfulRun TIMESTAMP NULL null, jobScheduledTime TIMESTAMP NULL null, jobStartTime TIMESTAMP NULL null, jobStateObject_id bigint, jobGroup varchar(128), jobName varchar(128), jobDescription varchar(256), primary key (id)) engine=InnoDB;
create table YadaJob_BeActive (YadaJob_id bigint not null, jobsMustBeActive_id bigint not null) engine=InnoDB;
create table YadaJob_BeCompleted (YadaJob_id bigint not null, jobsMustComplete_id bigint not null) engine=InnoDB;
create table YadaJob_BeInactive (YadaJob_id bigint not null, jobsMustBeInactive_id bigint not null) engine=InnoDB;
create table YadaManagedFile (height integer, privateAccess bit not null, temporary bit not null, width integer, expirationTimestamp datetime(6), id bigint not null auto_increment, modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, sizeInBytes bigint, uploadTimestamp datetime(6), version bigint not null, description varchar(512), clientFilename varchar(255), filename varchar(255), relativeFolderPath varchar(255), primary key (id)) engine=InnoDB;
create table YadaManagedFile_YadaManagedFile (YadaManagedFile_id bigint not null, derivedAssets_id bigint not null, assetKey varchar(255) not null, primary key (YadaManagedFile_id, assetKey)) engine=InnoDB;
create table YadaPersistentEnum (enumOrdinal integer not null, id bigint not null auto_increment, enumClassName varchar(191) not null, enumName varchar(255) not null, primary key (id)) engine=InnoDB;
create table YadaPersistentEnum_langToText (YadaPersistentEnum_id bigint not null, language varchar(32) not null, localText varchar(128), primary key (YadaPersistentEnum_id, language)) engine=InnoDB;
create table YadaRegistrationRequest (registrationType tinyint check ((registrationType between 0 and 3)), id bigint not null auto_increment, timestamp TIMESTAMP NULL null, token bigint not null, trattamentoDati_id bigint, version bigint not null, yadaUserCredentials_id bigint, email varchar(64) not null, timezone varchar(64), password varchar(128) not null, primary key (id)) engine=InnoDB;
create table YadaSocialCredentials (type integer not null, id bigint not null auto_increment, version bigint not null, yadaUserCredentials_id bigint not null, email varchar(128) not null, socialId varchar(128) not null, primary key (id)) engine=InnoDB;
create table YadaUserCredentials (changePassword bit not null, enabled bit not null, failedAttempts integer not null, creationDate TIMESTAMP NULL null, id bigint not null auto_increment, lastFailedAttempt TIMESTAMP NULL null, lastSuccessfulLogin TIMESTAMP NULL null, passwordDate TIMESTAMP NULL null, version bigint not null, password varchar(128) not null, username varchar(128) not null, primary key (id)) engine=InnoDB;
create table YadaUserCredentials_roles (roles integer, YadaUserCredentials_id bigint not null) engine=InnoDB;
create table YadaUserProfile (newsletterFlag bit, newsletterFlag2 bit, timezoneSetByUser bit not null, avatar_id bigint, id bigint not null auto_increment, newsletterFlagDate datetime(6), userCredentials_id bigint not null, version bigint not null, DTYPE varchar(31) not null check ((DTYPE in ('YadaUserProfile','UserProfile'))), firstName varchar(32), locale varchar(32), middleName varchar(32), lastName varchar(64), timezone varchar(64), primary key (id)) engine=InnoDB;
alter table Articolo add constraint UK58p5ar5bcmcaixr1dt4eun7gq unique (codiceSap);
alter table Articolo_SortedUploadedFiles add constraint UKpp5oqu1su06u4w4c4aawm3eqa unique (sortedUploadedFilesMap_id);
alter table CatalogConfig_YadaAttachedFile add constraint UK7u4rm3pdw5jxnee190kocp1h3 unique (noveltiesHero_id);
alter table CodiceProdotto add constraint UKll5pu0v4qrtmt7jvj2jq0y9fc unique (value);
alter table Configurator add constraint UKgtuxm42v2q9ex3tp7s74p1tc4 unique (famiglia_id);
alter table ConfiguratorShape add constraint UKjtftotgktr96fmu9fx8sfwd5q unique (movableType, configurator_id);
alter table ConfiguratorShape add constraint UKh4qu9bmpohb3b88lmmaww560l unique (movableType, configuratorForAttach_id);
alter table ConfiguratorShape add constraint UK6prf3lb2wbkjtytvt9udueyxb unique (defaultModel_id);
alter table ConfiguratorShape add constraint UKouwej9oh3lb982pv74p55o08r unique (dxf2DModel_id);
alter table ConfiguratorShape add constraint UK7jsd563lha01iesvifsdoxfiy unique (dxf2DWallModel_id);
alter table ConfiguratorShape add constraint UK79ly68yjoif36h6yrhib6teue unique (dxf3DModel_id);
alter table ConfiguratorShape add constraint UKtlg4bekdu2b3r38o5a955h45h unique (icon_id);
alter table ConfiguratorShape add constraint UKsjsu3a2kgy66y7pxxe4qqwwu7 unique (subfamily_id);
alter table ConfiguratorShape_YadaAttachedFile add constraint UKnnu00umoxnw45wa4orq3hb95i unique (models_id);
alter table CurvaFotometrica_SortedUploadedFiles add constraint UKtje86fqxkr9jebmwi46sscdbc unique (sortedUploadedFilesMap_id);
alter table Designer_SortedUploadedFiles add constraint UK10sspmcibs7njja6lem1soujd unique (sortedUploadedFilesMap_id);
alter table Etichetta add constraint UKpytc9f6jdoa2kgw2s8j9bdydx unique (codice, anno);
alter table FamilyImages add constraint UKml31ctvh51ykhhnqiqhli114a unique (bigImage_id);
alter table FamilyImages add constraint UK5dhys9cn95p1spa5rl8ftllmf unique (largeImage_id);
alter table FamilyImages add constraint UKi08tfixguas7i74s2ad0494x3 unique (smallImage_id);
alter table FamilyImages add constraint UKesm45k59yk0s36kdutrjj59of unique (tallImage_id);
alter table GalleryDescription_SortedUploadedFiles add constraint UK1n1p9305kgtn6yc2sqhrpgct2 unique (sortedUploadedFilesMap_id);
alter table Lampadina_SortedUploadedFiles add constraint UKbk9eug5pm8spbauadnuq4seww unique (sortedUploadedFilesMap_id);
alter table Led_SortedUploadedFiles add constraint UKgcd5ccxq05aht2nhxwrfd4qgu unique (sortedUploadedFilesMap_id);
alter table NewsJournal add constraint UKagvshtmw7kwb3h44isst4hd0s unique (thumbnail_id);
alter table NewsJournal add constraint UKslov7d2rf4dqnemf6rdwkrttt unique (topIndice_id);
alter table PageModule add constraint UK27ndv5mq8ixo8q83jt6qmy855 unique (imageTwo_id);
alter table PageModule add constraint UKpadh96iofp2ot2xs89bowvbad unique (image_id);
alter table PageModule add constraint UKijh0j1cvtepywad1tplphdj8x unique (pdfFile_id);
alter table PageModule_YadaAttachedFile add constraint UKb2jxditsyu2mr1krxu2mffeml unique (carrouselImages_id);
alter table PageModule_YadaGallerySlide1 add constraint UKb8wgbxmhlkvdqs290rsembw5k unique (gallery1_id);
alter table PageModule_YadaGallerySlide2 add constraint UKs7866bat3xfjhgmnee1hbblt4 unique (gallery2_id);
alter table PageModule_YadaGallerySlide3 add constraint UKr6v5hskf2qlhfmsf3gk5xydme unique (gallery3_id);
alter table PdfData add constraint UKcayni7mijg77hkacjn2tdp021 unique (productConfiguration_id);
alter table PrenotaSlot add constraint UK3wk1wxx4t6bfvf4heqs9t5k2e unique (prenotaStore_id, start);
alter table PrenotaUser add constraint UKmi2igg6rsbb56spiciofx24k7 unique (email, prenotaSlot_id);
alter table PressKit add constraint UK9k4wf3pejryrfhf5mbxpq5cpt unique (kit_id);
alter table Prodotto add constraint UKb9lpkhw04gj835fkh9bhauye unique (codice);
alter table Prodotto_SortedUploadedFiles add constraint UK45a0rpg7jag8kaw1vlru13k5c unique (sortedUploadedFilesMap_id);
alter table ProdottoPdfTimestamp add constraint UKr89jvy4t5sdpp0y9bdktdti51 unique (prodotto_id);
alter table Product_AlsoBought add constraint UKl221p3ae2l4qj20jp3ta59e2v unique (alsoBought_id);
alter table Product_RelatedProducts add constraint UKi3ifyegtp6rmbe0qdbf0kdepr unique (relatedProducts_id);
alter table ProductFile add constraint UKc2jvhpxpbn327enk3bwvf0yyc unique (attachedFile_id);
alter table ProductFile_GalleryImage add constraint UK5m22dm8wvsrj0i6ne7f283ra9 unique (galleryImage_id);
alter table Project add constraint UK5dtamamoa79okawsn9mhaj1h8 unique (bigImage_id);
alter table Project add constraint UKrke5vjr6iivjeajm0mowghh unique (smallImage_id);
alter table Publication add constraint UKlvipxabuxr6gspm9q1t4tt3wv unique (coverImage_id);
alter table Publication add constraint UKoxfsqmnfgc8ss8kb2lvmwloy7 unique (docFileDe_id);
alter table Publication add constraint UKiu7bsj5o0mv8ntvr89cw0xgif unique (docFileEn_id);
alter table Publication add constraint UKsmg0gk7eeelr79vto6vu08m2n unique (docFileEs_id);
alter table Publication add constraint UKaije6n3ju7h4nqlfx3fy50jxx unique (docFileFr_id);
alter table Publication add constraint UKc89s5q8nmxhljf34huplqnh38 unique (docFileIt_id);
alter table Publication_Subfamily add constraint UK1gw2xbidlqj9uncbv1rd3dbob unique (Publication_id, Subfamily_id);
alter table Publications_SortedUploadedFiles add constraint UK5n82osvt8t32x5qobha36n1wn unique (sortedUploadedFilesMap_id);
alter table Publications_Subfamily add constraint UKp28sm2tehiuuttg7ypkf3mxdo unique (sottofamiglie_id);
alter table Responsible add constraint UKmeicyliyn57mixg62df9xb28o unique (nazione);
alter table Responsible add constraint UKdbckiaie3dyvgf77jb48u7gvc unique (iso2);
alter table SortedUploadedFiles_UploadedFile add constraint UKr0hk3jcwq4vs76uui515or8b4 unique (uploadedFiles_id);
create index idx_store_promotions_sortable on Store (promotions_sortable);
alter table Store add constraint UKnulj7u5l8i18a969v7pikn3vk unique (indirizzo_id);
alter table Store_promotions add constraint UKhqrxmtka6l72d7qr07b6a29tx unique (Store_id, promotion_code);
alter table Subfamily_SortedUploadedFiles add constraint UK6j5eelmqj98fbvj523xr7bckt unique (sortedUploadedFilesMap_id);
alter table Tag_NewsJournal add constraint UKoerw895n797780rss7p918dhe unique (tags_id, news_id);
alter table Tag_Project add constraint UK9batueabncsgmvcqq2e7pdima unique (tags_id, project_id);
alter table UserProfile_favouriteFamilies add constraint UKkfflduucwefrwyvn7gs7himyi unique (UserProfile_id, favouriteFamilies);
alter table UserProfile_favouriteProducts add constraint UKi4ab7pim6m5cptwqqw7s2rk27 unique (UserProfile_id, favouriteProducts);
alter table UserProfile_favouriteProjects add constraint UKo0omdlh7cdvyl88ldkq8yf9fe unique (UserProfile_id, favouriteProjects);
alter table UserProfile_favouriteSubfamilies add constraint UK7b18o7ejgpexjux2d6imlkh5t unique (UserProfile_id, favouriteSubfamilies);
alter table YadaAutoLoginToken add constraint UKgpwvvntka6p2tjtnut6qiokyi unique (yadaUserCredentials_id);
alter table YadaBrowserId add constraint UKlvfuna79iqujxpkn0l6xvirh4 unique (mostSigBits, leastSigBits);
alter table YadaClause add constraint UKek0brxiv78vf6idvd6dv8v69d unique (name, clauseVersion);
alter table YadaGallerySlide add constraint UKhldoti42dwyffjvaq9sy9yy6r unique (image_id);
alter table YadaGallerySlide add constraint UKticston0nmmnte6dnklks4vqo unique (video_id);
alter table YadaJob add constraint UKdv98ftndf2nyk42cjoe50i36v unique (jobStateObject_id);
alter table YadaManagedFile_YadaManagedFile add constraint UKhgxut4q36gtjhq87u79qdm1hq unique (derivedAssets_id);
alter table YadaPersistentEnum add constraint UKfuc71vofqasw0r57t7etipp7p unique (enumClassName, enumOrdinal);
alter table YadaRegistrationRequest add constraint UK66pbcq6oohrfjqwr1o7wslucn unique (trattamentoDati_id);
alter table YadaRegistrationRequest add constraint UKb0i98ixarlwa54gkxws9ykxae unique (yadaUserCredentials_id);
alter table YadaSocialCredentials add constraint UK1uppa4u7bksphbjwm4i2c8re9 unique (socialId);
alter table YadaUserCredentials add constraint UK6gbgs7fb7g5t4wo0ys7e5q31j unique (username);
alter table YadaUserProfile add constraint UKq1b54fx8m1fu9budmpt2tm80i unique (avatar_id);
alter table YadaUserProfile add constraint UK3bjn82k5gj41f9ocejoxx1uua unique (userCredentials_id);
alter table AmdRegistrationRequest add constraint FKclmnok8v3h1otw59bk89vf2vi foreign key (id) references YadaRegistrationRequest (id);
alter table Articolo_SortedUploadedFiles add constraint FKob2soi5e6c279n22ith8wkqs0 foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table Articolo_SortedUploadedFiles add constraint FKbk7e603o8d4m97xp18fytra79 foreign key (Articolo_id) references Articolo (id);
alter table ArticoloDescrizione add constraint FKsi4bdecwufbdhg5xbkhchjiaf foreign key (Articolo_id) references Articolo (id);
alter table CatalogConfig_noveltiesName add constraint FKibw2xsh5k64cxe009m12xo6a1 foreign key (CatalogConfig_id) references CatalogConfig (id);
alter table CatalogConfig_YadaAttachedFile add constraint FK8kp5n02gnpfldabi123lbpb3x foreign key (noveltiesHero_id) references YadaAttachedFile (id);
alter table CatalogConfig_YadaAttachedFile add constraint FKcbk6g91ca0n1d25k7ndpxb2j5 foreign key (CatalogConfig_id) references CatalogConfig (id);
alter table Configurator add constraint FKltply2nkit0xivh9oc5x1l1nc foreign key (famiglia_id) references Famiglia (id);
alter table ConfiguratorShape add constraint FKp8j0i6587cw52gu7ejlj546t4 foreign key (cassaforma_id) references Prodotto (id);
alter table ConfiguratorShape add constraint FK4u9ogmvicb65uxti7gwk8oarx foreign key (configurator_id) references Configurator (id);
alter table ConfiguratorShape add constraint FK7p0vbjf80i035yt6pnaajf4ql foreign key (configuratorForAttach_id) references Configurator (id);
alter table ConfiguratorShape add constraint FKbnrbelr9d90ovova8x1qm1mex foreign key (defaultModel_id) references YadaAttachedFile (id);
alter table ConfiguratorShape add constraint FKqkiowpan6lfdwh1r0nb2x5r4o foreign key (dxf2DModel_id) references YadaAttachedFile (id);
alter table ConfiguratorShape add constraint FKcomd9g81fnmmhw6b99g51jh foreign key (dxf2DWallModel_id) references YadaAttachedFile (id);
alter table ConfiguratorShape add constraint FKphhq668kw0nnpyniqnvkcibgl foreign key (dxf3DModel_id) references YadaAttachedFile (id);
alter table ConfiguratorShape add constraint FKf9yb57xkofwk5qhnhvbi2bo6o foreign key (icon_id) references YadaAttachedFile (id);
alter table ConfiguratorShape add constraint FK72wa42wf1uagbbix32b01ho49 foreign key (subfamily_id) references Subfamily (id);
alter table ConfiguratorShape_emissions add constraint FKmjach16r2807xfay54flupme4 foreign key (Subfamily_id) references Subfamily (id);
alter table ConfiguratorShape_emissions add constraint FKi0baik8i58xio18ggiblojnk2 foreign key (ConfiguratorShape_id) references ConfiguratorShape (id);
alter table ConfiguratorShape_Subfamily add constraint FK8gb5ipu60imfgga4xnavsv1iq foreign key (subfamilies_id) references Subfamily (id);
alter table ConfiguratorShape_Subfamily add constraint FK4rl75k0ocsh6uyvg6bj0j3ptl foreign key (ConfiguratorShape_id) references ConfiguratorShape (id);
alter table ConfiguratorShape_YadaAttachedFile add constraint FKthshilmssooibg8iocsy70d0x foreign key (models_id) references YadaAttachedFile (id);
alter table ConfiguratorShape_YadaAttachedFile add constraint FK1conw8c7gtwaymyks1y0ipylm foreign key (Subfamily_id) references Subfamily (id);
alter table ConfiguratorShape_YadaAttachedFile add constraint FKbd44wkqq5625flmk4e1wu5e0o foreign key (ConfiguratorShape_id) references ConfiguratorShape (id);
alter table CountryProdottoList_Prodotto add constraint FK89akjt9m56927n5h07piudng9 foreign key (prodotti_id) references Prodotto (id);
alter table CountryProdottoList_Prodotto add constraint FKi9em6kh7ikeknr7nwwlldhtan foreign key (CountryProdottoList_id) references CountryProdottoList (id);
alter table CurvaFotometrica add constraint FKrqq8xp5dp0skaj18y32w1wl7v foreign key (subfamily_id) references Subfamily (id);
alter table CurvaFotometrica_SortedUploadedFiles add constraint FKq7r56vyv4kpj1x74ld3c2e5s1 foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table CurvaFotometrica_SortedUploadedFiles add constraint FKgi6a2y9ojo0u8wlv6u7s74pq3 foreign key (CurvaFotometrica_id) references CurvaFotometrica (id);
alter table CustomButtonLink add constraint FK4vd9ifh5mu4eenmw34jkyyspy foreign key (Subfamily_id) references Subfamily (id);
alter table CustomButtonName add constraint FK7r4mloxwwx0nccmgb4jqofo90 foreign key (Subfamily_id) references Subfamily (id);
alter table Designer_SortedUploadedFiles add constraint FKm314wyiy88huqlijrl26f2qyq foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table Designer_SortedUploadedFiles add constraint FKm85pc2yxel5vw3517550k15vx foreign key (Designer_id) references Designer (id);
alter table DesignerDescription add constraint FKb1q9b9jesfhjmcq90uxbk9egn foreign key (Designer_id) references Designer (id);
alter table ElectricalName add constraint FKnjgo65gh4y6cubo2k2t4cugal foreign key (Prodotto_id) references Prodotto (id);
alter table Famiglia_Designer add constraint FKk2615gcjie63bwncydr1ljsy5 foreign key (designers_id) references Designer (id);
alter table Famiglia_Designer add constraint FKjfa28mdcprg58bnfu3vwqmvor foreign key (Famiglia_id) references Famiglia (id);
alter table Famiglia_name add constraint FK3sgg0k0kgbgvlq1m339c4hwk3 foreign key (Famiglia_id) references Famiglia (id);
alter table FamilyImages add constraint FKkd7ud5f0n90r3pk11060fcgmr foreign key (bigImage_id) references YadaAttachedFile (id);
alter table FamilyImages add constraint FK4q8nkjfr5lrr3ldob73ssu746 foreign key (largeImage_id) references YadaAttachedFile (id);
alter table FamilyImages add constraint FKjxa3gwowac7h2bhftm4qxhrh3 foreign key (smallImage_id) references YadaAttachedFile (id);
alter table FamilyImages add constraint FKfkcidmy4p6itp9um490nqcb9p foreign key (tallImage_id) references YadaAttachedFile (id);
alter table FamilyImages add constraint FK8ynq9dvh1jpnxbdrej4yvtj1q foreign key (Famiglia_id) references Famiglia (id);
alter table FilterVariante add constraint FKd32yr1b9dhwjnd4o6edxdcac2 foreign key (subfamily_id) references Subfamily (id);
alter table FilterVariante_variants add constraint FKliknn7suqyfevd83vlnim40fy foreign key (FilterVariante_id) references FilterVariante (id);
alter table FilterVarianteDescription add constraint FK36lxei4x53775lqg0a960m1bj foreign key (FilterVariante_id) references FilterVariante (id);
alter table GalleryDescription add constraint FKrgua5q31ifvfu7mnx1yly09yg foreign key (subfamily_id) references Subfamily (id);
alter table GalleryDescription_SortedUploadedFiles add constraint FK295fhmo9w1p0ac38e5oyfh5xt foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table GalleryDescription_SortedUploadedFiles add constraint FKpxtnv0tc25t4ochmuodpgn0g7 foreign key (GalleryDescription_id) references GalleryDescription (id);
alter table GalleryDescriptionText add constraint FKomqsg4q45f0vyt68v9uhg0thu foreign key (GalleryDescription_id) references GalleryDescription (id);
alter table Lampadina_SortedUploadedFiles add constraint FKly1i5gavw379puon4in7ab6g4 foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table Lampadina_SortedUploadedFiles add constraint FKhi9a8aleyemgql97ii6bke0ox foreign key (Lampadina_id) references Lampadina (id);
alter table Led_SortedUploadedFiles add constraint FKj22pldv0gqmyq6tcmle4593ie foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table Led_SortedUploadedFiles add constraint FKg9nfc37ukgljm184723rewhiy foreign key (Led_id) references Led (id);
alter table MyCollection add constraint FK2132vs4bc5a9um0cauidj68sq foreign key (userProfile_id) references YadaUserProfile (id);
alter table MyCollection_productIds add constraint FK32j5of6evt28ab1m7pan4p8nr foreign key (MyCollection_id) references MyCollection (id);
alter table MyCollection_projectIds add constraint FKvjqv8dfk0qvfxbcu38gyed5o foreign key (MyCollection_id) references MyCollection (id);
alter table NewsJournal add constraint FKiv9j2ajc9b13d2lchgqk0g8te foreign key (thumbnail_id) references YadaAttachedFile (id);
alter table NewsJournal add constraint FK3arbluwurkr1i81kseogm6k2w foreign key (topIndice_id) references YadaAttachedFile (id);
alter table NewsJournal_titlePartOne add constraint FKd0r04cta98krds0qqhw34djo foreign key (NewsJournal_id) references NewsJournal (id);
alter table NewsJournal_titlePartTwo add constraint FK56g4pd5vk0vyw6eq2hh1kt9a9 foreign key (NewsJournal_id) references NewsJournal (id);
alter table PageModule add constraint FKnjm3nn98bkqghrwo47qjhckqk foreign key (image_id) references YadaAttachedFile (id);
alter table PageModule add constraint FKhd09d9klobvpsmgdovpwjuo9l foreign key (imageTwo_id) references YadaAttachedFile (id);
alter table PageModule add constraint FKqdydviemvnjyi14kttj423h9l foreign key (pdfFile_id) references YadaAttachedFile (id);
alter table PageModule add constraint FK9ca0tos3a4s5a917770loslh0 foreign key (homepage_id) references HomePage2 (id);
alter table PageModule add constraint FK3e78frfmc2b28ugucngkopps6 foreign key (news_id) references NewsJournal (id);
alter table PageModule add constraint FK8peyknod1vgal2449jhma4vdf foreign key (project_id) references Project (id);
alter table PageModule_linkText add constraint FKtbvffyg6b6evrpvfm7k0w4qbl foreign key (PageModule_id) references PageModule (id);
alter table PageModule_Subfamily add constraint FK9cevx7aggju33eieef5uas7ia foreign key (subfamilies_id) references Subfamily (id);
alter table PageModule_Subfamily add constraint FKtmg23dwnjfek7x5d5hyesm3qh foreign key (PageModule_id) references PageModule (id);
alter table PageModule_textOne add constraint FKg7p7rhx6snct3nugni2gfm04w foreign key (PageModule_id) references PageModule (id);
alter table PageModule_textTwo add constraint FKm22kard3t02gsn41ed5wx9qv6 foreign key (PageModule_id) references PageModule (id);
alter table PageModule_video2 add constraint FKlchmo3g48cqlnryfba0e9sfbg foreign key (PageModule_id) references PageModule (id);
alter table PageModule_YadaAttachedFile add constraint FKt98fg8mxxwn6eh2r6ww32qtsu foreign key (carrouselImages_id) references YadaAttachedFile (id);
alter table PageModule_YadaAttachedFile add constraint FKpiw8pknboux7ik31vo7cfx7gu foreign key (PageModule_id) references PageModule (id);
alter table PageModule_YadaGallerySlide1 add constraint FKl53cjteqrmcdhs3qoxq52cem1 foreign key (gallery1_id) references YadaGallerySlide (id);
alter table PageModule_YadaGallerySlide1 add constraint FKmvf1rjra3cc3scyrm5ipdbppt foreign key (PageModule_id) references PageModule (id);
alter table PageModule_YadaGallerySlide2 add constraint FKmr2u5vhrhpg0v37nbys0eg5qu foreign key (gallery2_id) references YadaGallerySlide (id);
alter table PageModule_YadaGallerySlide2 add constraint FKgwm7gomq0vvyubdvfl7xrh09g foreign key (PageModule_id) references PageModule (id);
alter table PageModule_YadaGallerySlide3 add constraint FKhem2wik6hnl32f98pq20swsbo foreign key (gallery3_id) references YadaGallerySlide (id);
alter table PageModule_YadaGallerySlide3 add constraint FK5oqwmnyxpvmitxusrqkdwmx8m foreign key (PageModule_id) references PageModule (id);
alter table PdfData add constraint FKmb3mrifqjusor1al88vxm3vp4 foreign key (productConfiguration_id) references ProductConfiguration (id);
alter table PrenotaSlot add constraint FKoj55bskt5qtjyq4c0w7q9b8ht foreign key (prenotaStore_id) references PrenotaStore (id);
alter table PrenotaUser add constraint FK5gpa7sctserj18fxclrl22nof foreign key (prenotaSlot_id) references PrenotaSlot (id);
alter table PressKit add constraint FKppqxvdorio7pyj1428dqetb9t foreign key (kit_id) references YadaAttachedFile (id);
alter table PressKit_title add constraint FKk4i7qq04rsjty0rd22yeakqou foreign key (PressKit_id) references PressKit (id);
alter table PrezzoSap add constraint FKowsjsq7o7rr95fkkvtm93jur7 foreign key (Articolo_id) references Articolo (id);
alter table Prodotto add constraint FK4mmwd4ibr1laq6e92ft9k7xer foreign key (subfamily_id) references Subfamily (id);
alter table Prodotto_Accessorio add constraint FK5nhnqa60hwn39ty3r4hf4n22c foreign key (accessori_id) references Articolo (id);
alter table Prodotto_Accessorio add constraint FK3k2fsnotcoibuy70hnq5biai7 foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_Componente add constraint FK77rvgwuk79s2a9y5708f9dand foreign key (componenti_id) references Articolo (id);
alter table Prodotto_Componente add constraint FK2393oxm6fe0wk0i2f4jimythv foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_Lampadina add constraint FKqd72k0xuoyjysb8hp03bm3mmn foreign key (listLamps_id) references Lampadina (id);
alter table Prodotto_Lampadina add constraint FKsr3qhh5j9l50hgprvxaus3pxk foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_LampadinaEsclusa add constraint FK43gtty2vdcwoo2bdbf0uj95ls foreign key (listLampsExcluded_id) references Lampadina (id);
alter table Prodotto_LampadinaEsclusa add constraint FK8jlhf45h3ncsdk0dn6013qdcq foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_LampadinaEsclusaAlt add constraint FKgeia4p46gxv9i3sc5p5odcehv foreign key (listLampsExcludedAlt_id) references Lampadina (id);
alter table Prodotto_LampadinaEsclusaAlt add constraint FK4c71rt99sh4pv2kdisx4512lt foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_Led add constraint FK94s80lg4spc0jjngauc9euo83 foreign key (listLeds_id) references Led (id);
alter table Prodotto_Led add constraint FK4yf62m8ce27w47jrjxfa7wew6 foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_LedEscluso add constraint FKgbfywgacd69h3wisat8ka8flw foreign key (listLedsExcluded_id) references Led (id);
alter table Prodotto_LedEscluso add constraint FKcucj5jnx8s8221xoocxeqj22e foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_LedEsclusoAlt add constraint FKn36k4k6slxx6qr7ti37fsgr7g foreign key (listLedsExcludedAlt_id) references Led (id);
alter table Prodotto_LedEsclusoAlt add constraint FK7w1q3fixr8ip9ohg2igvd3qw1 foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_mTipologia add constraint FK3jrt4eyxal95o710oc47e5j7 foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_newForCountry add constraint FK8ppmk7sjgvtrbpnx6tnvhbclt foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_SortedUploadedFiles add constraint FKlfaqxvkdxmr8oj13rufpw6cgn foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table Prodotto_SortedUploadedFiles add constraint FKfb46i10s8m2hapopl9rggoq88 foreign key (Prodotto_id) references Prodotto (id);
alter table Prodotto_variante add constraint FKq814mnpmo861f16psot0fi3ng foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoBallast add constraint FK1lhv6uq0gq1f7ld2767m44qtd foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoBaseColor add constraint FKbc87kgpu206l9tqqnedggltag foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoColor add constraint FK8fmsj45yqtlg2polrmwqgg94r foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoDescription add constraint FKjm7pqneqhc25c4d1a2wt0c754 foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoElectrical add constraint FK3w2l3g9w8cffqxqapm003q990 foreign key (prodotto_id) references Prodotto (id);
alter table ProdottoElectricalBallast add constraint FKe872iyhh5cap42pj1lriavdco foreign key (ProdottoElectrical_id) references ProdottoElectrical (id);
alter table ProdottoElectricalEmergency add constraint FK5e8awus9klmwfviyi4ebnq2j foreign key (ProdottoElectrical_id) references ProdottoElectrical (id);
alter table ProdottoElectricalName add constraint FK6ho5i5gakk1jl1if63tjf2ee6 foreign key (ProdottoElectrical_id) references ProdottoElectrical (id);
alter table ProdottoElectricalRemoteControl add constraint FKj0ho6b4wmf6gwn4ohda0stqsa foreign key (ProdottoElectrical_id) references ProdottoElectrical (id);
alter table ProdottoElectricalTransformer add constraint FK4ebr9j3clw6fx21w7p7kxiehs foreign key (ProdottoElectrical_id) references ProdottoElectrical (id);
alter table ProdottoEmergency add constraint FK93on9eujmuxm6xqr33elacb4p foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoMaterial add constraint FK8mkqvdnrxnfe7okc6et61ic0d foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoName add constraint FKhuvx0vrvyinyf7b8oaxlpnjin foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoNote add constraint FKdsbq4r2j0iath6m2cecvpdl1t foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoPdfTimestamp add constraint FKbda14cyitfmwfwwbq11unjhsi foreign key (prodotto_id) references Prodotto (id);
alter table ProdottoRemoteControl add constraint FKc6h65b9ummespdk13pb43ww4 foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoShortName add constraint FKf1h0h7yc8295v400vtulrer5i foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoSubName add constraint FKdx0x6tgytiv2ocuk65vw9brpf foreign key (Prodotto_id) references Prodotto (id);
alter table ProdottoTransformer add constraint FKkps2i4ai2mmjmr7f0rfa4a843 foreign key (Prodotto_id) references Prodotto (id);
alter table Product_AlsoBought add constraint FKratv4mw6gc2c2y5wdtqvyp7cr foreign key (alsoBought_id) references CountryProdottoList (id);
alter table Product_AlsoBought add constraint FKtnvg1j0e2u8a8uih5mvugtose foreign key (Prodotto_id) references Prodotto (id);
alter table Product_RelatedProducts add constraint FKn87491q5wmiofqdtnc8hr2q5p foreign key (relatedProducts_id) references CountryProdottoList (id);
alter table Product_RelatedProducts add constraint FKh5i2t0gyipu4riaxva5wlr7nr foreign key (Prodotto_id) references Prodotto (id);
alter table ProductConfiguration add constraint FKvn7uy2nwa5qbmkyc0wj5hwnx foreign key (configurator_id) references Configurator (id);
alter table ProductConfiguration add constraint FKpph5rs797uovncuqcs6rijfw1 foreign key (userProfile_id) references YadaUserProfile (id);
alter table ProductFile add constraint FKbc52uo3i0i58418028io1mtcf foreign key (attachedFile_id) references YadaAttachedFile (id);
alter table ProductFile add constraint FKlbpjtwgdweyi0m8nhalrxe3dq foreign key (subfamily_id) references Subfamily (id);
alter table ProductFile_GalleryImage add constraint FKda0ugbkvtgk1qh87gv34rrcfg foreign key (galleryImage_id) references YadaAttachedFile (id);
alter table ProductFile_GalleryImage add constraint FK93h1g61idr3o3iiqngpxxhfh5 foreign key (productfile_id) references ProductFile (id);
alter table ProductFile_Prodotto add constraint FKqamds506t1m5myb16wgp14dbm foreign key (prodotto_id) references Prodotto (id);
alter table ProductFile_Prodotto add constraint FKirwf9mal94xsombo6klyqu6rs foreign key (productfile_id) references ProductFile (id);
alter table Project add constraint FKlqpwkk1pptcdnor74g2eul41b foreign key (bigImage_id) references YadaAttachedFile (id);
alter table Project add constraint FK4s490p7bkrljqumiq8h63boec foreign key (smallImage_id) references YadaAttachedFile (id);
alter table Project_location add constraint FKjri72nues6ru6mh6ojebb5esv foreign key (Project_id) references Project (id);
alter table Project_titlePartOne add constraint FKisymty0b5lkx90og3a296ed7e foreign key (Project_id) references Project (id);
alter table Project_titlePartTwo add constraint FK9fpwuoa25c6psniy3jyow9nvx foreign key (Project_id) references Project (id);
alter table ProjectModule_changes add constraint FKfdm3ugag1w3un0gc2ff1bg5hx foreign key (ProjectModule_id) references PageModule (id);
alter table Publication add constraint FKnd04gn49chmxf9lhu8c2n3ul3 foreign key (coverImage_id) references YadaAttachedFile (id);
alter table Publication add constraint FK9blxk4rxahxil6dctbhl9a7gv foreign key (docFileDe_id) references YadaAttachedFile (id);
alter table Publication add constraint FKdlcblfpr3lduqgw6t2qfg3ruf foreign key (docFileEn_id) references YadaAttachedFile (id);
alter table Publication add constraint FKa8xydjimux7ymagdcc6jpkkc1 foreign key (docFileEs_id) references YadaAttachedFile (id);
alter table Publication add constraint FKoxn0d1fis4tn5ngx4txppjsta foreign key (docFileFr_id) references YadaAttachedFile (id);
alter table Publication add constraint FK6b5wslxmpjtraefu0utp0penp foreign key (docFileIt_id) references YadaAttachedFile (id);
alter table Publication_link add constraint FKbadd4qvg4rjttyecxxcwhgbu5 foreign key (Publication_id) references Publication (id);
alter table Publication_Subfamily add constraint FKct0mjwladjb7jmrhisc4j27u6 foreign key (Subfamily_id) references Subfamily (id);
alter table Publication_Subfamily add constraint FKnp509l7b0i4hh14cjcvqqfs6p foreign key (Publication_id) references Publication (id);
alter table Publication_subtitle add constraint FKp0ll95vuvdoitemeduxe9g2wk foreign key (Publication_id) references Publication (id);
alter table Publication_title add constraint FK7s2ysxlj8dx88w283r3ubdbko foreign key (Publication_id) references Publication (id);
alter table Publications_SortedUploadedFiles add constraint FKnuq4kc2v1ry2cydc5rasiaarr foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table Publications_SortedUploadedFiles add constraint FK6tskjp5b2aeprn6k5704c7ili foreign key (Publications_id) references Publications (id);
alter table Publications_Subfamily add constraint FK2aba8vr4hfaodre1p7v005gcp foreign key (sottofamiglie_id) references Subfamily (id);
alter table Publications_Subfamily add constraint FKguv4w5w3ia0p87n8r26nuexwx foreign key (Publications_id) references Publications (id);
alter table PublicationsLink add constraint FKjthcns7i5ouc8daap7ajw6iy foreign key (Publications_id) references Publications (id);
alter table PublicationsSubtitle add constraint FKghgrc211gp8lmfurc89dtgvxd foreign key (Publications_id) references Publications (id);
alter table PublicationsTitle add constraint FKb85vbujrnckjcbs1j6euvtboo foreign key (Publications_id) references Publications (id);
alter table Sellability add constraint FKb1xgibetx90veu99ostencf1l foreign key (Prodotto_id) references Prodotto (id);
alter table SortedUploadedFiles_UploadedFile add constraint FK6xsufsc5ben5bl2qm1i4ohoaw foreign key (uploadedFiles_id) references UploadedFile (id);
alter table SortedUploadedFiles_UploadedFile add constraint FKhk0gwieqlj3puf27qkd2qwceg foreign key (SortedUploadedFiles_id) references SortedUploadedFiles (id);
alter table Store add constraint FK5xw2f82d1gv4nr5htggg7k7fh foreign key (indirizzo_id) references Address (id);
alter table Store_promotions add constraint FK96klxvnk6k5olg7g3tygtsrqm foreign key (Store_id) references Store (id);
alter table Store_tipologia add constraint FKqrwqxxhqk51saj63o6mvqp5nb foreign key (Store_id) references Store (id);
alter table Subfamily add constraint FKrroq82hrol3jng52sqlf4xtwn foreign key (famiglia_id) references Famiglia (id);
alter table Subfamily_multiTipologia add constraint FKp3gutuv3i40okrvwv6vq5mh6s foreign key (Subfamily_id) references Subfamily (id);
alter table Subfamily_SortedUploadedFiles add constraint FK4uqn3urq28h3laccu9ceyogsf foreign key (sortedUploadedFilesMap_id) references SortedUploadedFiles (id);
alter table Subfamily_SortedUploadedFiles add constraint FKpvcqi7xf2caoj1yby5f72ltp3 foreign key (Subfamily_id) references Subfamily (id);
alter table SubfamilyConfigText add constraint FKg1wr9jwnk6mgomcff5v77lm2x foreign key (Subfamily_id) references Subfamily (id);
alter table SubfamilyEmission add constraint FKrpfpav7i1mfh0y4yvast8620a foreign key (Subfamily_id) references Subfamily (id);
alter table SubfamilyName add constraint FKm1xy59sr5dn1yoitagxw3xkhm foreign key (Subfamily_id) references Subfamily (id);
alter table Tag_name add constraint FK98j8p1pa1lmkhrwyi1uf6oms9 foreign key (Tag_id) references Tag (id);
alter table Tag_NewsJournal add constraint FKbi9kvka8gr1slxdu9tfb2g9rm foreign key (news_id) references NewsJournal (id);
alter table Tag_NewsJournal add constraint FK5ibxqffeg151y43hsui58l5af foreign key (tags_id) references Tag (id);
alter table Tag_Project add constraint FK4yoydb33jh5ye5h8f08boy8te foreign key (project_id) references Project (id);
alter table Tag_Project add constraint FK4i2b7p273hl63cmkoyowmdxxw foreign key (tags_id) references Tag (id);
alter table UploadedFileDescription add constraint FKqvhdu5vs6md7vfm6h61kqgt1 foreign key (UploadedFile_id) references UploadedFile (id);
alter table UploadedFileTitle add constraint FKpqb171f6bvh9c325ohrdbuger foreign key (UploadedFile_id) references UploadedFile (id);
alter table UserProfile_favouriteFamilies add constraint FKkech94kinruid1jy8cnu3ljb0 foreign key (UserProfile_id) references YadaUserProfile (id);
alter table UserProfile_favouriteProducts add constraint FKr4il7s2f2432qyl6lviql6m03 foreign key (UserProfile_id) references YadaUserProfile (id);
alter table UserProfile_favouriteProjects add constraint FKjtu1aiq3fc4sfj9tn1co8kqb1 foreign key (UserProfile_id) references YadaUserProfile (id);
alter table UserProfile_favouriteSubfamilies add constraint FKirbno55yq5w3imncd2tai2fy8 foreign key (UserProfile_id) references YadaUserProfile (id);
alter table UserProfile_recentlyViewedProducts add constraint FKgh9eb5dpukfl7qtyd678y0lkf foreign key (UserProfile_id) references YadaUserProfile (id);
alter table UserProfile_recentlyViewedSubfamilies add constraint FKefe63gnyaky81vm0vjpy6wv2i foreign key (UserProfile_id) references YadaUserProfile (id);
alter table VarianteDescription add constraint FKgyr4wg481ywu74okg1yvpgtbj foreign key (VarianteDescriptionInSubfamily_id) references VarianteDescriptionInSubfamily (id);
alter table VarianteDescriptionInSubfamily add constraint FKn1o0xvmgr5s7vobpeh0lveq2f foreign key (Subfamily_id) references Subfamily (id);
alter table YadaAttachedFile_description add constraint FKj1954nnr3hu07yak1tyb4inc6 foreign key (YadaAttachedFile_id) references YadaAttachedFile (id);
alter table YadaAttachedFile_title add constraint FKqawwx1dakd1a91pxgappdycka foreign key (YadaAttachedFile_id) references YadaAttachedFile (id);
alter table YadaAutoLoginToken add constraint FKh92vo7me2k2s4v1x1jercpuo foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaGallerySlide add constraint FK2qcwx3dwik86t72u9xoumvmpt foreign key (image_id) references YadaAttachedFile (id);
alter table YadaGallerySlide add constraint FK5rlrwp5xr62314t7wiqw8097q foreign key (video_id) references YadaAttachedFile (id);
alter table YadaGallerySlide_text5local add constraint FKtlgucqen7h79farj8vt1j1ltw foreign key (YadaGallerySlide_id) references YadaGallerySlide (id);
alter table YadaGallerySlide_text6local add constraint FK9bbf7hwefi5hfow41xc9lnsi6 foreign key (YadaGallerySlide_id) references YadaGallerySlide (id);
alter table YadaGallerySlide_text7local add constraint FKcyxt85cax5gty0gi3oy4sfumq foreign key (YadaGallerySlide_id) references YadaGallerySlide (id);
alter table YadaGallerySlide_text8local add constraint FK64m13ka5jmpm6yjdwyfkp0p3l foreign key (YadaGallerySlide_id) references YadaGallerySlide (id);
alter table YadaJob add constraint FKbly4fv9jmbvwppy5b9x79yokq foreign key (jobStateObject_id) references YadaPersistentEnum (id);
alter table YadaJob_BeActive add constraint FKfcdajxue4qegy3sh412qcqd7 foreign key (jobsMustBeActive_id) references YadaJob (id);
alter table YadaJob_BeActive add constraint FKqhqlee0k5m0ir9s6kpw8m9y6d foreign key (YadaJob_id) references YadaJob (id);
alter table YadaJob_BeCompleted add constraint FK8o25xd851myc035dwd0xm7kpd foreign key (jobsMustComplete_id) references YadaJob (id);
alter table YadaJob_BeCompleted add constraint FKgcmntp7yy872ldenedb6nnyep foreign key (YadaJob_id) references YadaJob (id);
alter table YadaJob_BeInactive add constraint FK8yfnn9cj06lrptwbtnpnevp4h foreign key (jobsMustBeInactive_id) references YadaJob (id);
alter table YadaJob_BeInactive add constraint FKamylqhhgf9gjwsux3yfosvq52 foreign key (YadaJob_id) references YadaJob (id);
alter table YadaManagedFile_YadaManagedFile add constraint FK2mw9b5mxcoo5epdpw3718m9kf foreign key (derivedAssets_id) references YadaManagedFile (id);
alter table YadaManagedFile_YadaManagedFile add constraint FKexoh53ti99il5xfow6celtn0q foreign key (YadaManagedFile_id) references YadaManagedFile (id);
alter table YadaPersistentEnum_langToText add constraint FKewmgshpqaehgfba9sp8pluddg foreign key (YadaPersistentEnum_id) references YadaPersistentEnum (id);
alter table YadaRegistrationRequest add constraint FKkn2yxfy3t9fjmuannqfph49d0 foreign key (trattamentoDati_id) references YadaClause (id);
alter table YadaRegistrationRequest add constraint FKq6guqxscpqqq7pl96md1y79rn foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaSocialCredentials add constraint FK72s54ufexgh2xk2122ihkc82l foreign key (yadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaUserCredentials_roles add constraint FK1oj60uojdn4xql004wfe2v0hp foreign key (YadaUserCredentials_id) references YadaUserCredentials (id);
alter table YadaUserProfile add constraint FKpi28ogwa7vguwb3vv1tkmpovi foreign key (avatar_id) references YadaAttachedFile (id);
alter table YadaUserProfile add constraint FKm8x7qmacvae25wmfdhnuf4e25 foreign key (userCredentials_id) references YadaUserCredentials (id);
