package com.artemide.common.repository.test;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;

/**
 * Database utility class for test classes.
 * Provides generic methods to find entity IDs without loading full entities.
 */
public class DbUtil {

    /**
     * Finds the first available ID for the given entity class.
     * Uses EntityManager to query only the ID field, avoiding loading full entities.
     * 
     * @param entityManager the EntityManager instance
     * @param entityClass the entity class (e.g., Famiglia.class)
     * @return Optional containing the first ID if found, empty if no records exist
     */
    public static <T> Optional<Long> findFirstId(EntityManager entityManager, Class<T> entityClass) {
        try {
            String entityName = entityClass.getSimpleName();
            String query = String.format("SELECT e.id FROM %s e ORDER BY e.id", entityName);
            
            List<Long> ids = entityManager
                .createQuery(query, Long.class)
                .setMaxResults(1)
                .getResultList();
                
            return ids.isEmpty() ? Optional.empty() : Optional.of(ids.get(0));
        } catch (Exception e) {
            // In case of any query issues, return empty
            return Optional.empty();
        }
    }

    /**
     * Finds multiple IDs for the given entity class.
     * Uses EntityManager to query only the ID field, avoiding loading full entities.
     * 
     * @param entityManager the EntityManager instance
     * @param entityClass the entity class (e.g., Prodotto.class)
     * @param maxResults maximum number of IDs to return
     * @return List of IDs (may be empty if no records exist)
     */
    public static <T> List<Long> findMultipleIds(EntityManager entityManager, Class<T> entityClass, int maxResults) {
        try {
            String entityName = entityClass.getSimpleName();
            String query = String.format("SELECT e.id FROM %s e ORDER BY e.id", entityName);
            
            return entityManager
                .createQuery(query, Long.class)
                .setMaxResults(maxResults)
                .getResultList();
        } catch (Exception e) {
            // In case of any query issues, return empty list
            return java.util.Collections.emptyList();
        }
    }

    /**
     * Finds the minimum ID for the given entity class.
     * Alternative method that uses MIN function instead of ORDER BY + LIMIT.
     * 
     * @param entityManager the EntityManager instance
     * @param entityClass the entity class
     * @return Optional containing the minimum ID if found, empty if no records exist
     */
    public static <T> Optional<Long> findMinId(EntityManager entityManager, Class<T> entityClass) {
        try {
            String entityName = entityClass.getSimpleName();
            String query = String.format("SELECT MIN(e.id) FROM %s e", entityName);
            
            Long minId = entityManager
                .createQuery(query, Long.class)
                .getSingleResult();
                
            return minId != null ? Optional.of(minId) : Optional.empty();
        } catch (Exception e) {
            // In case of any query issues, return empty
            return Optional.empty();
        }
    }

    /**
     * Checks if any records exist for the given entity class.
     * 
     * @param entityManager the EntityManager instance
     * @param entityClass the entity class
     * @return true if at least one record exists, false otherwise
     */
    public static <T> boolean hasAnyRecords(EntityManager entityManager, Class<T> entityClass) {
        try {
            String entityName = entityClass.getSimpleName();
            String query = String.format("SELECT COUNT(e) FROM %s e", entityName);
            
            Long count = entityManager
                .createQuery(query, Long.class)
                .getSingleResult();
                
            return count != null && count > 0;
        } catch (Exception e) {
            // In case of any query issues, assume no records
            return false;
        }
    }

    /**
     * Finds IDs with a specific condition.
     * More flexible method for complex queries.
     * 
     * @param entityManager the EntityManager instance
     * @param entityClass the entity class
     * @param whereClause the WHERE clause (without "WHERE" keyword), e.g., "e.published = true"
     * @param maxResults maximum number of IDs to return
     * @return List of IDs matching the condition
     */
    public static <T> List<Long> findIdsWithCondition(EntityManager entityManager, Class<T> entityClass, 
                                                      String whereClause, int maxResults) {
        try {
            String entityName = entityClass.getSimpleName();
            String query = String.format("SELECT e.id FROM %s e WHERE %s ORDER BY e.id", entityName, whereClause);
            
            return entityManager
                .createQuery(query, Long.class)
                .setMaxResults(maxResults)
                .getResultList();
        } catch (Exception e) {
            // In case of any query issues, return empty list
            return java.util.Collections.emptyList();
        }
    }
}
