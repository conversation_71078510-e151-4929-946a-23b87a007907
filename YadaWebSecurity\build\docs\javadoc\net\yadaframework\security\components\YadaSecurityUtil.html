<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (21) -->
<title>YadaSecurityUtil (YadaWebSecurity '0.7.8' API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="description" content="declaration: package: net.yadaframework.security.components, class: YadaSecurityUtil">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html#class">Help</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>Summary:</p>
<ul>
<li>Nested</li>
<li>Field</li>
<li><a href="#constructor-summary">Constr</a></li>
<li><a href="#method-summary">Method</a></li>
</ul>
</li>
<li>
<p>Detail:</p>
<ul>
<li>Field</li>
<li><a href="#constructor-detail">Constr</a></li>
<li><a href="#method-detail">Method</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">Method</a></li>
</ul>
<ul class="sub-nav-list">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">Method</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="Search">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">Package</span>&nbsp;<a href="package-summary.html">net.yadaframework.security.components</a></div>
<h1 title="Class YadaSecurityUtil" class="title">Class YadaSecurityUtil</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">net.yadaframework.security.components.YadaSecurityUtil</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="annotations">@Component
</span><span class="modifiers">public class </span><span class="element-name type-name-label">YadaSecurityUtil</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">YadaSecurityUtil</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button><button id="method-summary-table-tab6" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab6', 3)" class="table-tab">Deprecated Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#addLoginErrorParams(java.lang.String)" class="member-name-link">addLoginErrorParams</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Add to some url the login error request parameters defined in YadaAuthenticationFailureHandler so that the login modal
 can show them.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#caseAnonAuth(java.lang.String,java.lang.String)" class="member-name-link">caseAnonAuth</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;anonymousValue,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;authenticatedValue)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ritorna uno o l'altro parametro a seconda che l'utente corrente sia autenticato o meno.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#changePassword(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.String)" class="member-name-link">changePassword</a><wbr>(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;userProfile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newPassword)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Set a new password using the configured encoder.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#checkUrlAccess(jakarta.servlet.http.HttpServletRequest,java.lang.String)" class="member-name-link">checkUrlAccess</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Checks if the current user has access to the specified path</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#clearAnySavedRequest()" class="member-name-link">clearAnySavedRequest</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#copyLoginErrorParams(jakarta.servlet.http.HttpServletRequest,org.springframework.ui.Model)" class="member-name-link">copyLoginErrorParams</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 org.springframework.ui.Model&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Copy all not-null login error parameters to the Model</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateClearPassword()" class="member-name-link">generateClearPassword</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Generate a 32 characters random password</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateClearPassword(int)" class="member-name-link">generateClearPassword</a><wbr>(int&nbsp;length)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Generate a random password</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getCurrentRoles()" class="member-name-link">getCurrentRoles</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns Spring-formatted roles, like "ROLE_USER" i.e.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSavedRequestUrl()" class="member-name-link">getSavedRequestUrl</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Ritorna la richiesta che era stata salvata da Spring Security prima del login, bloccata perchè l'utente non era autenticato</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getUsername()" class="member-name-link">getUsername</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasAnyRole(java.lang.String...)" class="member-name-link">hasAnyRole</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;rolesToCheck)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the current user has any of the provided roles, case sensitive.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#hasCurrentRole(java.lang.String)" class="member-name-link">hasCurrentRole</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;roleToCheck)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the current user has the provided role, case sensitive.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#hasCurrentRole(java.lang.String%5B%5D)" class="member-name-link">hasCurrentRole</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;rolesToCheck)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use <a href="#hasAnyRole(java.lang.String...)"><code>hasAnyRole(String...)</code></a> instead</div>
</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isLockedOut(net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">isLockedOut</a><wbr>(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;yadaUserProfile)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if a user has been suspended for excess of login failures</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#isLoggedIn()" class="member-name-link">isLoggedIn</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the current user is authenticated (logged in) not anonymously.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#loggedIn()" class="member-name-link">loggedIn</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loggedIn(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">loggedIn</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the current user is logged in.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#logout(jakarta.servlet.http.HttpServletRequest)" class="member-name-link">logout</a><wbr>(jakarta.servlet.http.HttpServletRequest&nbsp;request)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Logs out the currently logged-in user</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#performPasswordChange(net.yadaframework.web.form.YadaFormPasswordChange)" class="member-name-link">performPasswordChange</a><wbr>(net.yadaframework.web.form.YadaFormPasswordChange&nbsp;yadaFormPasswordChange)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Change the user password and log in</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>int</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#performPasswordChange(net.yadaframework.web.form.YadaFormPasswordChange,java.lang.Boolean)" class="member-name-link">performPasswordChange</a><wbr>(net.yadaframework.web.form.YadaFormPasswordChange&nbsp;yadaFormPasswordChange,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;forceDifferentPassword)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Change the user password and log in after eventually checking that the password is actually different from the previous one</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#registrationRequestCleanup(net.yadaframework.security.persistence.entity.YadaRegistrationRequest)" class="member-name-link">registrationRequestCleanup</a><wbr>(<a href="../persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a>&nbsp;registrationRequest)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Cancello le registration request vecchie o con lo stesso email e tipo.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6"><code><a href="#setRolesWhenAllowed(net.yadaframework.security.persistence.entity.YadaUserProfile,java.util.List,java.util.List)" class="member-name-link">setRolesWhenAllowed</a><wbr>(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;actingUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;&nbsp;rolesBefore,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;&nbsp;rolesAfter)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4 method-summary-table-tab6">
<div class="block"><span class="deprecated-label">Deprecated.</span></div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#userCanChangeRole(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.Integer)" class="member-name-link">userCanChangeRole</a><wbr>(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;actingUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;targetRoleId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the given user can change the role targetRoleId on users, based on its own roles</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#userCanChangeRole(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.String)" class="member-name-link">userCanChangeRole</a><wbr>(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;actingUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetRoleKey)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Returns true if the given user can change the role targetRoleId on users, based on its own roles</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#userCanEditUser(net.yadaframework.security.persistence.entity.YadaUserProfile,java.util.List,java.util.List)" class="member-name-link">userCanEditUser</a><wbr>(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;actingUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;&nbsp;oldRoles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;&nbsp;newRoles)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the roles of the actingUser allow it to change some target user based on its roles, as configured by &lt;handles&gt;
 A target user can be changed only when both its current roles and its new roles can all be changed by any of the roles of the acting user.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#userCanImpersonate(net.yadaframework.security.persistence.entity.YadaUserProfile,net.yadaframework.security.persistence.entity.YadaUserProfile)" class="member-name-link">userCanImpersonate</a><wbr>(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;actingUser,
 <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;targetUser)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Check if the roles of the actingUser allow it to impersonate the targetUser based on its roles, as configured by &lt;handles&gt;
 The actingUser can impersonate the targetUser only when there is at least one role of actingUser that can change all roles of targetUser.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>YadaSecurityUtil</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">YadaSecurityUtil</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="checkUrlAccess(jakarta.servlet.http.HttpServletRequest,java.lang.String)">
<h3>checkUrlAccess</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">checkUrlAccess</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;path)</span></div>
<div class="block">Checks if the current user has access to the specified path</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - The current HttpServletRequest</dd>
<dd><code>path</code> - The path to check access for, e.g. "/dashboard"</dd>
<dt>Returns:</dt>
<dd>true if access is granted, false otherwise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="isLockedOut(net.yadaframework.security.persistence.entity.YadaUserProfile)">
<h3>isLockedOut</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isLockedOut</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;yadaUserProfile)</span></div>
<div class="block">Check if a user has been suspended for excess of login failures</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaUserProfile</code> - </dd>
<dt>Returns:</dt>
<dd>true if the user is locked out</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="changePassword(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.String)">
<h3>changePassword</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">changePassword</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;userProfile,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;newPassword)</span></div>
<div class="block">Set a new password using the configured encoder. Also sets the password timestamp and clears the failed attempts.
 The "force password change" flag is not cleared for the use case of a user being forced to set
 a new password after first login with a provided password.
 The password encoder is configured with &lt;encodePassword&gt;true&lt;/encodePassword&gt;
 <br>
 The userProfile is not saved.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>userProfile</code> - </dd>
<dd><code>newPassword</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="setRolesWhenAllowed(net.yadaframework.security.persistence.entity.YadaUserProfile,java.util.List,java.util.List)">
<h3>setRolesWhenAllowed</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;</span>&nbsp;<span class="element-name">setRolesWhenAllowed</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;actingUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;&nbsp;rolesBefore,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;&nbsp;rolesAfter)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Ensures that roles set on some target user can be set by the current user.
 Permissions to change roles are specified in the &lt;role&gt;&lt;handles&gt; configuration parameter.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>actingUser</code> - the user that wants to change roles on some user</dd>
<dd><code>rolesBefore</code> - the roles that the target user had before they were modified</dd>
<dd><code>rolesAfter</code> - the roles that the target user should have after modification. On exit, the roles that can't be changed are reset to the value in rolesBefore</dd>
<dt>Returns:</dt>
<dd>the roles that the actingUser can't change</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="userCanChangeRole(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.Integer)">
<h3>userCanChangeRole</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">userCanChangeRole</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;actingUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&nbsp;targetRoleId)</span></div>
<div class="block">Returns true if the given user can change the role targetRoleId on users, based on its own roles</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>actingUser</code> - the user that wants to change a role</dd>
<dd><code>targetRoleId</code> - the role that the user wants to change</dd>
<dt>Returns:</dt>
<dd>true if actingUser can set or clear the targetRoleId on users, as configured by &lt;handles&gt;</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="userCanChangeRole(net.yadaframework.security.persistence.entity.YadaUserProfile,java.lang.String)">
<h3>userCanChangeRole</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">userCanChangeRole</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;actingUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;targetRoleKey)</span></div>
<div class="block">Returns true if the given user can change the role targetRoleId on users, based on its own roles</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>actingUser</code> - the user that wants to change a role</dd>
<dd><code>targetRoleKey</code> - the role that the current user wants to change, any case</dd>
<dt>Returns:</dt>
<dd>true if actingUser can set or clear the targetRoleId on users, as configured by &lt;handles&gt;</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="userCanEditUser(net.yadaframework.security.persistence.entity.YadaUserProfile,java.util.List,java.util.List)">
<h3>userCanEditUser</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">userCanEditUser</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;actingUser,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;&nbsp;oldRoles,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Integer.html" title="class or interface in java.lang" class="external-link">Integer</a>&gt;&nbsp;newRoles)</span></div>
<div class="block">Check if the roles of the actingUser allow it to change some target user based on its roles, as configured by &lt;handles&gt;
 A target user can be changed only when both its current roles and its new roles can all be changed by any of the roles of the acting user.
 So for example a "Manager" can not change a "Guest" to an "Admin" or an "Admin" to a "Guest", nor edit an Admin.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>actingUser</code> - the user that wants to change the targetUser</dd>
<dd><code>oldRoles</code> - the roles of the target user before changing them</dd>
<dd><code>newRoles</code> - the roles of the target user after changing them</dd>
<dt>Returns:</dt>
<dd>true if actingUser can edit targetUser, false otherwise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="userCanImpersonate(net.yadaframework.security.persistence.entity.YadaUserProfile,net.yadaframework.security.persistence.entity.YadaUserProfile)">
<h3>userCanImpersonate</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">userCanImpersonate</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;actingUser,
 <a href="../persistence/entity/YadaUserProfile.html" title="class in net.yadaframework.security.persistence.entity">YadaUserProfile</a>&nbsp;targetUser)</span></div>
<div class="block">Check if the roles of the actingUser allow it to impersonate the targetUser based on its roles, as configured by &lt;handles&gt;
 The actingUser can impersonate the targetUser only when there is at least one role of actingUser that can change all roles of targetUser.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>actingUser</code> - the user that wants to impersonate the targetUser</dd>
<dd><code>targetUser</code> - the user that might be impersonated</dd>
<dt>Returns:</dt>
<dd>true if actingUser can impersonate targetUser, false otherwise</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="logout(jakarta.servlet.http.HttpServletRequest)">
<h3>logout</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">logout</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="block">Logs out the currently logged-in user</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="copyLoginErrorParams(jakarta.servlet.http.HttpServletRequest,org.springframework.ui.Model)">
<h3>copyLoginErrorParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">copyLoginErrorParams</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request,
 org.springframework.ui.Model&nbsp;model)</span></div>
<div class="block">Copy all not-null login error parameters to the Model</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dd><code>model</code> - </dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="addLoginErrorParams(java.lang.String)">
<h3>addLoginErrorParams</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">addLoginErrorParams</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url)</span></div>
<div class="block">Add to some url the login error request parameters defined in YadaAuthenticationFailureHandler so that the login modal
 can show them.
 This method should be used when opening the login modal using an ajax call form a normal page as the result of a previous login error.
 Usage example:
        <pre>
    const loginModalUrl = [[$]];
        yada.ajax(loginModalUrl);
                </pre></div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>url</code> - </dd>
<dt>Returns:</dt>
<dt>See Also:</dt>
<dd>
<ul class="tag-list-long">
<li><a href="YadaAuthenticationFailureHandler.html" title="class in net.yadaframework.security.components"><code>YadaAuthenticationFailureHandler</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateClearPassword()">
<h3>generateClearPassword</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">generateClearPassword</span>()</div>
<div class="block">Generate a 32 characters random password</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>a string like "XFofvGEtBlZIa5sH"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateClearPassword(int)">
<h3>generateClearPassword</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">generateClearPassword</span><wbr><span class="parameters">(int&nbsp;length)</span></div>
<div class="block">Generate a random password</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>length</code> - password length</dd>
<dt>Returns:</dt>
<dd>a string like "XFofvGEtBlZIa5sH"</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="performPasswordChange(net.yadaframework.web.form.YadaFormPasswordChange)">
<h3>performPasswordChange</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">performPasswordChange</span><wbr><span class="parameters">(net.yadaframework.web.form.YadaFormPasswordChange&nbsp;yadaFormPasswordChange)</span></div>
<div class="block">Change the user password and log in</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaFormPasswordChange</code> - </dd>
<dt>Returns:</dt>
<dd>true if password changed and user logged in</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="performPasswordChange(net.yadaframework.web.form.YadaFormPasswordChange,java.lang.Boolean)">
<h3>performPasswordChange</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">int</span>&nbsp;<span class="element-name">performPasswordChange</span><wbr><span class="parameters">(net.yadaframework.web.form.YadaFormPasswordChange&nbsp;yadaFormPasswordChange,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;forceDifferentPassword)</span></div>
<div class="block">Change the user password and log in after eventually checking that the password is actually different from the previous one</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>yadaFormPasswordChange</code> - </dd>
<dd><code>forceDifferentPassword</code> - set to true to force a different password</dd>
<dt>Returns:</dt>
<dd>the outcome: 0 = ok, 1 = invalid token, 2 = same password as before, 3 = generic error</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getUsername()">
<h3>getUsername</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getUsername</span>()</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>the username of the logged-in user, or null</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="loggedIn()">
<h3>loggedIn</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">loggedIn</span>()</div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span></div>
<div class="block">Check if the current user is authenticated (logged in) not anonymously.
 Use in thymeleaf with th:if="$"</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="isLoggedIn()">
<h3>isLoggedIn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">isLoggedIn</span>()</div>
<div class="block">Check if the current user is authenticated (logged in) not anonymously.
 Use in thymeleaf with th:if="$"</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="loggedIn(jakarta.servlet.http.HttpServletRequest)">
<h3>loggedIn</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">loggedIn</span><wbr><span class="parameters">(jakarta.servlet.http.HttpServletRequest&nbsp;request)</span></div>
<div class="block">Check if the current user is logged in.
 Use in thymeleaf with th:if="$"</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>request</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="clearAnySavedRequest()">
<h3>clearAnySavedRequest</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">clearAnySavedRequest</span>()</div>
</section>
</li>
<li>
<section class="detail" id="getSavedRequestUrl()">
<h3>getSavedRequestUrl</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">getSavedRequestUrl</span>()</div>
<div class="block">Ritorna la richiesta che era stata salvata da Spring Security prima del login, bloccata perchè l'utente non era autenticato</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>la url originale completa di http://, oppure null se non c'è in sessione</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="caseAnonAuth(java.lang.String,java.lang.String)">
<h3>caseAnonAuth</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></span>&nbsp;<span class="element-name">caseAnonAuth</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;anonymousValue,
 <a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;authenticatedValue)</span></div>
<div class="block">Ritorna uno o l'altro parametro a seconda che l'utente corrente sia autenticato o meno.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>anonymousValue</code> - </dd>
<dd><code>authenticatedValue</code> - </dd>
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="registrationRequestCleanup(net.yadaframework.security.persistence.entity.YadaRegistrationRequest)">
<h3>registrationRequestCleanup</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">registrationRequestCleanup</span><wbr><span class="parameters">(<a href="../persistence/entity/YadaRegistrationRequest.html" title="class in net.yadaframework.security.persistence.entity">YadaRegistrationRequest</a>&nbsp;registrationRequest)</span></div>
<div class="block">Cancello le registration request vecchie o con lo stesso email e tipo. Se la registrationRequest passata � sul database, non viene cancellata.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>registrationRequest</code> - prototipo di richiesta da cancellare (ne viene usato email e tipo)</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getCurrentRoles()">
<h3>getCurrentRoles</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">getCurrentRoles</span>()</div>
<div class="block">Returns Spring-formatted roles, like "ROLE_USER" i.e. prefixed by "ROLE_"</div>
<dl class="notes">
<dt>Returns:</dt>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasCurrentRole(java.lang.String)">
<h3>hasCurrentRole</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasCurrentRole</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;roleToCheck)</span></div>
<div class="block">Check if the current user has the provided role, case sensitive.
 This method finds roles in the current SecurityContextHolder and never goes
 to database so it may be better than <a href="../web/YadaSession.html#isCurrentRole(java.lang.String)"><code>YadaSession.isCurrentRole(String)</code></a>
 for time-critical use cases</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>roleToCheck</code> - without "ROLE_" prefix</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasCurrentRole(java.lang.String[])">
<h3>hasCurrentRole</h3>
<div class="member-signature"><span class="annotations"><a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/Deprecated.html" title="class or interface in java.lang" class="external-link">@Deprecated</a>
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasCurrentRole</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>[]&nbsp;rolesToCheck)</span></div>
<div class="deprecation-block"><span class="deprecated-label">Deprecated.</span>
<div class="deprecation-comment">use <a href="#hasAnyRole(java.lang.String...)"><code>hasAnyRole(String...)</code></a> instead</div>
</div>
<div class="block">Check if the current user has any of the provided roles, case sensitive.
 This method finds roles in the current SecurityContextHolder and never goes
 to database but if an instance of the current YadaUserProfile is available,
 the <a href="../persistence/entity/YadaUserProfile.html#hasAnyRoleId(java.lang.Integer...)"><code>YadaUserProfile.hasAnyRoleId(Integer...)</code></a> method may be faster.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rolesToCheck</code> - without "ROLE_" prefix</dd>
<dt>See Also:</dt>
<dd>
<ul class="tag-list">
<li><a href="#hasAnyRole(java.lang.String...)"><code>hasAnyRole(String...)</code></a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="hasAnyRole(java.lang.String...)">
<h3>hasAnyRole</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">hasAnyRole</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>...&nbsp;rolesToCheck)</span></div>
<div class="block">Check if the current user has any of the provided roles, case sensitive.
 This method finds roles in the current SecurityContextHolder and never goes
 to database but if an instance of the current YadaUserProfile is available,
 the <a href="../persistence/entity/YadaUserProfile.html#hasAnyRoleId(java.lang.Integer...)"><code>YadaUserProfile.hasAnyRoleId(Integer...)</code></a> method may be faster.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>rolesToCheck</code> - without "ROLE_" prefix</dd>
</dl>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
</div>
</div>
</body>
</html>
