package com.artemide.common.repository.test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.NewsJournal;
import com.artemide.common.repository.NewsJournalDao;
import com.artemide.common.repository.NewsJournalRepository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

/**
 * Real database integration tester that verifies NewsJournalDao behaves exactly like NewsJournalRepository
 * using actual database operations. All test data is cleaned up after each test.
 * This class can be invoked from a web controller to run tests via web interface.
 */
@Transactional
@Repository
public class NewsJournalRepositoryDaoTester {
	private final transient Logger log = LoggerFactory.getLogger(getClass());
	
    @Autowired
    private NewsJournalRepository newsJournalRepository;

    @Autowired
    private NewsJournalDao newsJournalDao;

    @PersistenceContext
    private EntityManager entityManager;

    // Track created entities for cleanup
    private List<Long> createdEntityIds;

    private void setUp() {
        createdEntityIds = new java.util.ArrayList<>();
        cleanupTestData();
    }

    private void tearDown() {
        cleanupTestData();
    }

    private void cleanupTestData() {
        if (createdEntityIds != null && !createdEntityIds.isEmpty()) {
            for (Long id : createdEntityIds) {
                try {
                    NewsJournal entity = entityManager.find(NewsJournal.class, id);
                    if (entity != null) {
                        entityManager.remove(entity);
                    }
                } catch (Exception e) {
                    // Ignore cleanup errors
                }
            }
            entityManager.flush();
            createdEntityIds.clear();
        }
        
        try {
            // NewsJournal uses Map for titlePartOne, so we need a different cleanup approach
            entityManager.createQuery("DELETE FROM NewsJournal n WHERE n.id IN (SELECT n2.id FROM NewsJournal n2 JOIN n2.titlePartOne t WHERE KEY(t) = :locale AND VALUE(t) LIKE 'TEST_%' OR VALUE(t) LIKE 'INTEGRATION_%')")
                .setParameter("locale", java.util.Locale.ENGLISH)
                .executeUpdate();
            entityManager.flush();
        } catch (Exception e) {
            log.error("Cleanup failed", e);
        }
    }

    private NewsJournal createTestEntity(String title) {
        NewsJournal entity = new NewsJournal();
        // NewsJournal uses Map<Locale, String> for titlePartOne
        Map<java.util.Locale, String> titleMap = new HashMap<>();
        titleMap.put(java.util.Locale.ENGLISH, title);
        entity.setTitlePartOne(titleMap);
        entity.setEnabled(true);
        return entity;
    }

    private void trackEntity(NewsJournal entity) {
        if (entity != null && entity.getId() != null) {
            createdEntityIds.add(entity.getId());
        }
    }

    public String testCount() {
        setUp();
        try {
            long initialRepoCount = newsJournalRepository.count();
            long initialDaoCount = newsJournalDao.count();
            
            if (initialRepoCount != initialDaoCount) {
                return "FAIL: Initial counts don't match - Repository: " + initialRepoCount + ", DAO: " + initialDaoCount;
            }

            return "PASS: count() test successful - both return " + initialRepoCount;
        } finally {
            tearDown();
        }
    }

    public String testSaveAndFindById() {
        setUp();
        try {
            NewsJournal testEntity = createTestEntity("INTEGRATION_SAVE_001");

            NewsJournal repoSaved = newsJournalRepository.save(testEntity);
            trackEntity(repoSaved);
            entityManager.flush();

            NewsJournal testEntity2 = createTestEntity("INTEGRATION_SAVE_002");
            NewsJournal daoSaved = newsJournalDao.save(testEntity2);
            trackEntity(daoSaved);
            entityManager.flush();

            if (repoSaved.getId() == null) {
                return "FAIL: Repository saved entity should have ID";
            }
            if (daoSaved.getId() == null) {
                return "FAIL: DAO saved entity should have ID";
            }

            Optional<NewsJournal> repoFound = newsJournalRepository.findById(repoSaved.getId());
            Optional<NewsJournal> daoFoundOptional = newsJournalDao.findById(daoSaved.getId());
            NewsJournal daoFoundDirect = newsJournalDao.findOne(daoSaved.getId());

            if (!repoFound.isPresent()) {
                return "FAIL: Repository should find saved entity";
            }
            if (!daoFoundOptional.isPresent()) {
                return "FAIL: DAO findById should find saved entity";
            }
            if (daoFoundDirect == null) {
                return "FAIL: DAO findOne should find saved entity";
            }
            
            return "PASS: save() and findById() test successful";
        } finally {
            tearDown();
        }
    }

    public String testFindAll() {
        setUp();
        try {
            List<NewsJournal> repoResults = newsJournalRepository.findAll();
            List<NewsJournal> daoResults = newsJournalDao.findAll();

            if (repoResults.size() != daoResults.size()) {
                return "FAIL: Repository and DAO should return same number of entities - Repository: " + repoResults.size() + ", DAO: " + daoResults.size();
            }
            
            return "PASS: findAll() test successful - both return " + repoResults.size() + " entities";
        } finally {
            tearDown();
        }
    }

    public String testDelete() {
        setUp();
        try {
            NewsJournal entityForRepo = createTestEntity("INTEGRATION_DELETE_001");
            NewsJournal entityForDao = createTestEntity("INTEGRATION_DELETE_002");
            
            NewsJournal savedForRepo = newsJournalRepository.save(entityForRepo);
            NewsJournal savedForDao = newsJournalDao.save(entityForDao);
            entityManager.flush();
            
            if (!newsJournalRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }
            if (!newsJournalDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: Entity should exist before deletion";
            }

            newsJournalRepository.delete(savedForRepo);
            newsJournalDao.delete(savedForDao);
            entityManager.flush();

            if (newsJournalRepository.findById(savedForRepo.getId()).isPresent()) {
                return "FAIL: Repository deleted entity should not be findable";
            }
            if (newsJournalDao.findById(savedForDao.getId()).isPresent()) {
                return "FAIL: DAO deleted entity should not be findable";
            }
            
            // Remove from tracking since they're already deleted
            createdEntityIds.remove(savedForRepo.getId());
            createdEntityIds.remove(savedForDao.getId());
            
            return "PASS: delete() test successful";
        } finally {
            tearDown();
        }
    }

    @Transactional(readOnly = false)
    public String testSetFalseNewsTop() {
        setUp();
        try {
            // Create test entities with topNews set to true
            NewsJournal testEntity1 = createTestEntity("INTEGRATION_TOP_001");
            NewsJournal testEntity2 = createTestEntity("INTEGRATION_TOP_002");
            
            NewsJournal saved1 = newsJournalRepository.save(testEntity1);
            NewsJournal saved2 = newsJournalRepository.save(testEntity2);
            trackEntity(saved1);
            trackEntity(saved2);
            entityManager.flush();

            // Test the method - this modifies all records
            newsJournalRepository.setFalseNewsTop();
            newsJournalDao.setFalseNewsTop();
            entityManager.flush();
            
            // Both methods should execute without error
            return "PASS: setFalseNewsTop() test successful";
        } finally {
            tearDown();
        }
    }

    /**
     * Run all tests and return a summary report
     */
    public String runAllTests() {
        StringBuilder report = new StringBuilder();
        report.append("=== NewsJournalRepository vs NewsJournalDao Test Results ===\n\n");
        
        try {
            String countResult = testCount();
            report.append("1. Count Test: ").append(countResult).append("\n");
            
            String saveAndFindResult = testSaveAndFindById();
            report.append("2. Save & FindById Test: ").append(saveAndFindResult).append("\n");
            
            String findAllResult = testFindAll();
            report.append("3. FindAll Test: ").append(findAllResult).append("\n");
            
            String deleteResult = testDelete();
            report.append("4. Delete Test: ").append(deleteResult).append("\n");
            
            String setFalseNewsTopResult = testSetFalseNewsTop();
            report.append("5. SetFalseNewsTop Test: ").append(setFalseNewsTopResult).append("\n");
            
            report.append("\n=== SUMMARY ===\n");
            if (report.toString().contains("FAIL")) {
                report.append("❌ TESTS FAILED - There are differences between Repository and DAO behavior\n");
            } else {
                report.append("✅ ALL TESTS PASSED - NewsJournalDao behaves exactly like NewsJournalRepository\n");
            }
            
        } catch (Exception e) {
            report.append("ERROR: Test execution failed - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
}
