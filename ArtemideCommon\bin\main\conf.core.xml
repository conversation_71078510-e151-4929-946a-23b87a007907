<?xml version="1.0" encoding="UTF-8" ?>
<config>
	<!-- 
		# Parametri comuni sia al CMS che al sito
	 -->
	<resources>
		<dir>
			<images>
				<articolo>immagini/articolo</articolo>
				<famiglia>immagini/famiglia</famiglia>
				<subfamily>immagini/subfamily</subfamily>
				<designer>immagini/designer</designer>
				<lightemission>immagini/lightemission</lightemission>
				<lampadina>immagini/lampadina</lampadina>
				<led>immagini/led</led>
				<prodotto>immagini/prodotto</prodotto>
				<project>immagini/project</project>
				<eventonews>immagini/eventonews</eventonews>
				<publications>immagini/publications</publications>
				<homepage>immagini/homepage</homepage>
				<homepagegallery>immagini/homepagegallery</homepagegallery>
			</images>
			<documents>
				<prodotto>documenti/prodotto</prodotto>
				<publications>documenti/publications</publications>
			</documents>
			<cv>
				<upload>cv</upload>
				<download>cv/download</download>
			</cv>
		</dir>
	</resources>
	<measurements>
		<!-- Attenzione che questi valori sono cablati in BabkaMeasurementsConverter -->
		<measurement default="true">SI</measurement>
		<measurement>SCS</measurement>
		<measurement>SUS</measurement>
	</measurements>
	<stores>
		<maxSearchResults>500</maxSearchResults>
		<maxRadiusResults>0.1</maxRadiusResults>
	</stores>
	<!-- Lista di tipologie per ogni catalogo. Serve ad evitare di andare su db per compilare le tendine di ricerca -->
	<catalogTipologies>
		<catalog0>0,1,2,3,4,5,6,7</catalog0>
		<catalog1>0,1,2,3,5,7,8,9</catalog1>
		<catalog7>0,1,2,3,4,5,6,9</catalog7>
		<catalog4>2,5,9</catalog4>
	</catalogTipologies>
	<language>
		<available>
			it_IT, en_GB, en_US, es_ES, de_DE, fr_CA, fr_FR, ru_RU
		</available>
		<international>it=it_IT</international>
		<international>en=en_GB</international>
		<international>es=es_ES</international>
		<international>de=de_DE</international>
		<international>fr=fr_FR</international>
		<international>ru=ru_RU</international>
	</language>
	<pdfletterus>
		<country>US</country>
	</pdfletterus>
</config>
