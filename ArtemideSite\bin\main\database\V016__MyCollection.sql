create table MyCollection (id bigint not null auto_increment, lastModified TIMESTAMP NULL, subtitle varchar(32), title varchar(32), version bigint not null, userProfile_id bigint, primary key (id)) engine=InnoDB;
create table MyCollection_productIds (MyCollection_id bigint not null, productIds bigint, sort integer not null, primary key (MyCollection_id, sort)) engine=InnoDB;
create table MyCollection_projectIds (MyCollection_id bigint not null, projectIds bigint, sort integer not null, primary key (MyCollection_id, sort)) engine=InnoDB;
alter table MyCollection add constraint FK2132vs4bc5a9um0cauidj68sq foreign key (userProfile_id) references YadaUserProfile (id);
