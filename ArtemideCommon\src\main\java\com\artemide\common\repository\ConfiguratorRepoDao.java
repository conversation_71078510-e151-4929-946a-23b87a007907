package com.artemide.common.repository;

import java.util.List;
import java.util.Optional;

import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.NonUniqueResultException;
import jakarta.persistence.PersistenceContext;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.artemide.common.persistence.entity.Configurator;

@Repository
@Transactional(readOnly = true)
public class ConfiguratorRepoDao {

    @PersistenceContext EntityManager em;

    /**
     * Ritorna il primo Configurator abilitato
     * @return
     */
    public Configurator findFirstByEnabledTrueOrderById() {
        String sql = "from Configurator where enabled = true order by id";
        List<Configurator> resultList = em.createQuery(sql, Configurator.class)
            .setMaxResults(1)
            .getResultList();
        return normaliseSingleResult(resultList);
    }

    /**
     * Conta quanti sono i configuratori abilitati
     * @param enabled
     * @return
     */
    public int countByEnabled(Boolean enabled) {
        String sql = "select count(*) from Configurator where enabled = :enabled";
        return em.createQuery(sql, Long.class)
            .setParameter("enabled", enabled)
            .getSingleResult().intValue();
    }

    /**
     * Trova il primo configuratore relativo alla famiglia con l'id passato
     * @param famigliaId
     * @return
     */
    public Configurator findFirstByFamigliaId(Long famigliaId) {
        String sql = "from Configurator where famigliaId = :famigliaId";
        List<Configurator> resultList = em.createQuery(sql, Configurator.class)
            .setMaxResults(1)
            .setParameter("famigliaId", famigliaId)
            .getResultList();
        return normaliseSingleResult(resultList);
    }

    /**
     * For backwards compatibility, returns null when no result is found
     * @param resultList
     * @return
     */
    private Configurator normaliseSingleResult(List<Configurator> resultList) {
        // Need to keep the contract of the Spring Data Repository, so we return null when no value found.
        if (resultList.isEmpty()) {
            return null;
        } else {
            return resultList.get(0);
        }
    }

    // Legacy methods for compatibility with Spring Data Repository
    public long count() {
        return em.createQuery("select count(*) from Configurator", Long.class).getSingleResult().longValue();
    }

    @Transactional(readOnly = false)
    public Configurator save(Configurator entity) {
        if (entity == null) {
            return null;
        }
        if (entity.getId() == null) {
            em.persist(entity);
            return entity;
        }
        return em.merge(entity);
    }

    public Optional<Configurator> findById(Long entityId) {
        Configurator result = em.find(Configurator.class, entityId);
        return Optional.ofNullable(result);
    }

    public Configurator findOne(Long entityId) {
        return em.find(Configurator.class, entityId);
    }

    public List<Configurator> findAll() {
        return em.createQuery("from Configurator", Configurator.class)
            .getResultList();
    }

    @Transactional(readOnly = false)
    public void saveAll(List<Configurator> batchToSave) {
        for (Configurator entity : batchToSave) {
            save(entity);
        }
    }

    @Transactional(readOnly = false)
    public void delete(Configurator entity) {
        em.remove(entity);
    }
}
